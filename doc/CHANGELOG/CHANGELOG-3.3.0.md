feat(Agent编程): 前端页面生成智能体&生成待办列表&已知问题修复

### Feature

* `前端页面生成` 新增前端页面生成智能体，支持图表生成，优化工具调用和提示词
* `知识库管理` 支持多种文档类型上传和管理，新增通过URL和本地文件添加知识库功能，支持@doc引用文档
* `智能体功能` 新增联网搜索功能，支持图片文件处理，优化思考模式显示，添加可切换的思考过程展示
* `待办事项` 支持在对话中创建和编辑待办列表，新增任务管理功能
* `聊天体验` 支持文件和图片拖拽到聊天框，优化聊天界面交互，添加快捷工具栏
* `一键部署` 新增应用一键部署功能，支持快速发布到测试环境
* `记忆管理` 新增AI记忆管理功能，支持个性化设置和交互优化
* `自动执行` 支持全局自动执行开关，优化批准设置和界面交互
* `MCP工具` 优化MCP服务器连接管理，增强模型视觉能力检测
* `用户体验` 更新品牌名称为JoyCode，优化登录和用户信息同步

### Fix

* `智能体` 修复默认模式设置，优化模型选择逻辑，解决页面白屏和交互问题
* `聊天功能` 修复输入框样式问题，优化用户交互提示，改善重试按钮体验
* `文件处理` 修复文件读取和处理相关问题，优化图片大小限制至5MB
* `浏览器工具` 修复Windows系统下浏览器工具无法使用的问题
* `待办事项` 修复待办列表相关的执行和显示问题
* `主题适配` 修复浅色主题下的UI显示问题，优化按钮和输入框样式
* `登录系统` 修复内外网切换时的登录状态同步问题
* `MCP配置` 优化MCP配置界面，修复相关设置和显示问题

### Style

* `聊天界面` 优化聊天区域布局，调整消息间距和按钮样式
* `设置界面` 改善设置弹窗样式，优化主题适配效果
* `输入框` 支持拖拽调整高度，添加工具提示和快捷功能
* `智能体管理` 优化智能体配置界面，改善弹窗位置和样式
* `MCP组件` 统一按钮样式，优化在不同屏幕尺寸下的显示效果

### Refactor

* `构建系统` 优化构建配置，提升开发效率
* `图片处理` 优化图片压缩功能，改善处理性能
* `上下文管理` 优化上下文压缩和窗口管理
* `终端处理` 支持自动清理不活跃终端，优化输出处理
* `代码分析` 升级代码库版本，优化分析性能