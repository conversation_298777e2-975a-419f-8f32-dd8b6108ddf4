import * as vscode from 'vscode';
import { reportAction, ActionType, Logger } from '@joycoder/shared';
import { updateSecIssuesPanelData } from '@joycoder/plugin-base-sec';
import { flattenFileTree } from '../utils/file';
import FileTreeNode from '../tree/fileTreeNode';
import { SecCommitCheckParams } from '@joycoder/plugin-base-sec/src/SecTreeDataProvider';

let CRDisposable: vscode.Disposable = new vscode.Disposable(() => {});

export default function (context: vscode.ExtensionContext) {
  CRDisposable && CRDisposable.dispose();
  // const debouncedSearch = _.debounce(loadCRView, 800);
  //多文件代码审核
  CRDisposable = vscode.commands.registerCommand('JoyCode.code.review', async (...file) => {
    initCodeReview(file[1] || []);
  });

  context.subscriptions.push(CRDisposable);
}
async function initCodeReview(files: vscode.Uri[]) {
  if (files.length === 0) {
    return;
  }
  try {
    reportAction({
      actionCate: 'sec',
      actionType: ActionType.fRightKey,
    });
    const paths: SecCommitCheckParams[] = ((await flattenFileTree(files)) ?? [])
      .filter((item: FileTreeNode) => item.fileType != 'directory')
      .map((item: FileTreeNode) => {
        return {
          ...item,
          fileName: item.fileName ?? '',
          filePath: item.filePath ?? '',
          fileLanguage: item.fileLanguage ?? '',
          code: item.fileContent ?? '',
          fileSource: ActionType.batchCodeReviewEnum,
        };
      });
    //检测后拉起CR窗口
    vscode.commands.executeCommand('JoyCoderFE.sec.showSecIssues');
    updateSecIssuesPanelData(paths);
  } catch (error) {
    Logger.warn('%c [ error ]-31', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
}
