
.tab-button {
  border: none;
  cursor: pointer;
  position: relative;
  font-family: <PERSON>Fang SC;
  font-size: 15px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  background-color: var(--vscode-editor-background);

  &.active {
    font-weight: 600;
    color: var(--vscode-foreground);
  }

  &:not(.active) {
    font-weight: normal;
    color: var(--vscode-foreground);
    opacity: 0.5;
  }

  .active-indicator {
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 2px;
    background: rgba(36, 127, 255, 1);
  }
}
