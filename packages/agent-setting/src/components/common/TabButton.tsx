import React from 'react';
import './TabButton.scss';

interface TabButtonProps {
  active: boolean;
  onClick: () => void;
  children: React.ReactNode;
}

const TabButton: React.FC<TabButtonProps> = React.memo(({ active, onClick, children }) => {
  return (
    <div onClick={onClick} aria-selected={active} role="tab" className={`tab-button ${active ? 'active' : ''}`}>
      {children}
      {active && <div className="active-indicator" />}
    </div>
  );
});

export default TabButton;
