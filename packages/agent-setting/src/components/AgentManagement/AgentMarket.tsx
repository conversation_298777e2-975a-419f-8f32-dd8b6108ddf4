import React from 'react';
import { List, Avatar, Tag } from 'antd';
import './index.scss';

interface MarketAgent {
  id: string;
  name: string;
  description: string;
  tags: string[];
  avatar?: string;
}

const mockMarketAgents: MarketAgent[] = [
  {
    id: '1',
    name: '代码评审助手',
    description: '专注于代码审查，提供详细的代码质量分析和改进建议',
    tags: ['代码质量', '最佳实践'],
  },
  {
    id: '2',
    name: '性能优化专家',
    description: '帮助识别和解决性能瓶颈，提供优化建议',
    tags: ['性能优化', '代码效率'],
  },
  {
    id: '3',
    name: '安全测试专家',
    description: '专注于代码安全性分析，识别潜在的安全漏洞',
    tags: ['安全测试', '漏洞检测'],
  },
];

export default function AgentMarket() {
  return (
    <List
      className="agent-market-list"
      itemLayout="horizontal"
      dataSource={mockMarketAgents}
      renderItem={(item) => (
        <List.Item
          actions={[
            <span key="install" className="agent-market-install">
              安装
            </span>,
          ]}
        >
          <List.Item.Meta
            avatar={<Avatar src={item.avatar} />}
            title={item.name}
            description={
              <>
                <div>{item.description}</div>
                <div className="agent-market-tags">
                  {item.tags.map((tag) => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </div>
              </>
            }
          />
        </List.Item>
      )}
    />
  );
}
