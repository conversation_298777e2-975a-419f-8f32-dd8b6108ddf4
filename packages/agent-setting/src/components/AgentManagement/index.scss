.joycoder-setting-content .setting-tabs .setting-agent-tabs {
  .joycoder-dark-tabs {
    &-nav {
      padding-top: 0;

    }
    &-nav::before{
      border-bottom: 0;
    }
    &-tab{
      font-size: 15px !important;
    }
  }
}
.joycoder-setting-content {
  .joycoder-setting-box-text {
    .joycoder-dark-input-number {
      border:1px solid var(--vscode-input-foreground, #72747c);
    }
    .joycoder-dark-input-number:hover {
      border:1px solid var(--vscode-input-foreground, #72747c);
    }
    .joycoder-dark-input-number .joycoder-dark-input-number-handler-wrap {
      opacity: 1;
    }
  }
  .joycoder-dark-empty-description {
    color: var(--vscode-descriptionForeground, #72747c);
  }
}