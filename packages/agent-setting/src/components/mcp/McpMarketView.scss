.mcp-market-view {
  // height: 824px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  background-color: var(--vscode-editor-background);
  color: var(--vscode-foreground);
  position: relative;

  &__content {
    flex: 1;
    //overflow: auto;
    //padding: 20px;

    .joycoder-dark-input-affix-wrapper {
      color: var(--vscode-input-foreground, #72747c);
    }

    .joycoder-dark-input {
      color: var(--vscode-input-foreground, #72747c);
    }
  }
  &__loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: var(--vscode-editor-background);
      opacity: 0.5;
  }


  &__popup {
    left: 0;
    top: 0;
    width: 320px;
    background-color: var(--vscode-editor-background);
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    overflow: hidden;
    color: var(--vscode-foreground);

    &-header {
      width: 100%;
      height: 56px;
      padding: 12px;
      border-bottom: 1px solid var(--vscode-widget-border);
      h3 {
        color: var(--vscode-foreground);
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        line-height: 32px;
      }
    }

    &-content {
      width: 100%;
      height: 270px;
      padding: 12px;
      p {
        color: var(--vscode-descriptionForeground);
        margin: 8px 0;
        font-size: 14px;
        line-height: 1.4;
      }
    }
  }

  &__search {
    width: 100%;
    margin-bottom: 20px;
  }

  &__options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 13px;
    font-family: var(--vscode-font-family);
    font-weight: normal;
    opacity: 0.8;

    .mcp-checkbox-context {
      display: flex;
      align-items: center;
      cursor: pointer;

      input[type="checkbox"] {
        appearance: none;
        -webkit-appearance: none;
        width: 16px;
        height: 16px;
        border: 1px solid var(--vscode-checkbox-border);
        border-radius: 3px;
        outline: none;
        cursor: pointer;
        margin-right: 8px;

        &:checked {
          background-color: var(--vscode-checkbox-background);
          border-color: var(--vscode-checkbox-border);

          &::after {
            content: '\2714';
            display: block;
            text-align: center;
            color: var(--vscode-checkbox-foreground);
            font-size: 12px;
            line-height: 16px;
          }
        }
      }
    }
  }


  &__service-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: calc(100vh - 270px);
    overflow-y: auto;
    // -ms-overflow-style: none;  // IE和Edge
    scrollbar-width: none;     // Firefox
    
    &::-webkit-scrollbar {     // Chrome, Safari, Opera
      width: 0;
      display: none;
    }
  }

  &__service-item {
    display: flex;
    align-items: stretch;
    position: relative;
    justify-content: 'space-between'
  }

  &__service-icon {
    display: flex;
    justify-content: center;
    margin-right: 15px;
    font-size: 18px;
    font-weight: bold;
    flex-shrink: 0;
  }

  &__service-logo,
  &__service-default-logo {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__service-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  &__service-name {
    display: flex;
    margin: 0;
    font-size: 13px;
    font-weight: 600;
    color: var(--vscode-foreground);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__service-description {
    max-height: 36px;
    margin: 5px 0;
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    line-height: 18px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }

  &__service-creator {
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    line-height: 18px;
  }

  &__service-button-container {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}

.mcp-button {
  height: 28px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;

  &__install {
    width: 49px;
    height: 28px;
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
  }

  &__configure {
    width: 51px;
    height: 30px;
    background: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);

  }

  &__deployment-type {
    width: 62px;
    height: 20px;
    background: var(--vscode-button-secondaryBackground);
    border-radius: 2px;
    color: var(--vscode-button-secondaryForeground);
    font-size: 10px;
    margin-left: 8px;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
      margin-right: 4px;
    }

    .icon-juli,
    .icon-bendibushu {
      display: inline-block;
    }
  }

  &__checkmark {
    margin-right: 4px;
    font-size: 14px;
    line-height: 0;
    display: flex;
    align-items: center;
  }
}
