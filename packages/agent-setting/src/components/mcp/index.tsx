import './index.scss';
import McpMarketView from './McpMarketView';
import McpInstalledView from './McpInstalledView';
import React, { useState, useCallback } from 'react';
import { McpModal } from './McpModal';
import TabButton from '../common/TabButton';
import { McpActiveTab } from '../../typings/marketplace';
import { Button } from 'antd';
import { PlusOutlined, RedoOutlined } from '@ant-design/icons';

interface McpMarketProps {
  defaultMcpTab?: McpActiveTab;
}

export default function McpMarket(props: McpMarketProps) {
  const { defaultMcpTab = McpActiveTab.market } = props;
  const [activeTab, setActiveTab] = useState<McpActiveTab>(defaultMcpTab);
  const [isManualConfigModalVisible, setIsManualConfigModalVisible] = useState(false);
  const [openDefaultServer, setOpenDefaultServer] = useState('');

  const installedViewRef = React.useRef<{ fetchMcpConnectionServer: () => void }>(null);

  const handleOpenDefaultServerUsed = useCallback(() => {
    setOpenDefaultServer('');
  }, []);

  const handleOpenModal = () => {
    setIsManualConfigModalVisible(true);
  };

  const handleCloseModal = () => {
    setIsManualConfigModalVisible(false);
  };

  const handleInstalledSuccess = (installedServers: string[]) => {
    // 处理安装成功后的逻辑
    // console.log('Installed servers:', installedServers);
  };
  const handleConfigure = (serverName: string) => {
    setActiveTab(McpActiveTab.installed);
    setOpenDefaultServer(serverName);
  };

  return (
    <div className="mcp-container">
      <div className="mcp-content setting-tabs-item">
        <div className="mcp-header">
          <div className="setting-tabs-item-title">MCP 服务器</div>
          {/* <h2 className="mcp-title">MCP 服务器</h2> */}
          {/* <a href="http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/start/intro">
            <span className="mcp-tutorial">
              <QuestionCircleOutlined className="mcp-tutorial-icon" />
              使用教程
            </span>
          </a> */}
        </div>

        <div className="mcp-tabs">
          <div className="mcp-tab-buttons">
            <div style={{ marginRight: '15px' }}>
              <TabButton active={activeTab === McpActiveTab.market} onClick={() => setActiveTab(McpActiveTab.market)}>
                MCP市场
              </TabButton>
            </div>
            <TabButton
              active={activeTab === McpActiveTab.installed}
              onClick={() => {
                setActiveTab(McpActiveTab.installed);
                setOpenDefaultServer('');
              }}
            >
              已安装
            </TabButton>
            {activeTab === McpActiveTab.market && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleOpenModal}
                style={{
                  backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                  borderColor: 'var(--vscode-input-border, #72747c)',
                  width: '84px',
                  height: '28px',
                  borderRadius: '4px',
                  color: 'var(--vscode-button-secondaryForeground, #72747c)',
                  fontSize: '12px',
                  padding: '0',
                  float: 'right',
                  marginLeft: 'auto',
                  marginRight: '0',
                }}
              >
                手动添加
              </Button>
            )}
            {activeTab === McpActiveTab.installed && (
              <div className="add-mcp-config-content">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleOpenModal}
                  style={{
                    backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                    borderColor: 'var(--vscode-input-border, #72747c)',
                    width: '84px',
                    height: '28px',
                    borderRadius: '4px',
                    color: 'var(--vscode-button-secondaryForeground, #72747c)',
                    fontSize: '12px',
                    padding: '0',
                    float: 'right',
                    marginLeft: 'auto',
                  }}
                >
                  手动添加
                </Button>
                <Button
                  type="primary"
                  icon={<RedoOutlined />}
                  onClick={() => installedViewRef.current?.fetchMcpConnectionServer()}
                  style={{
                    backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                    borderColor: 'var(--vscode-input-border, #72747c)',
                    width: '84px',
                    height: '28px',
                    borderRadius: '4px',
                    color: 'var(--vscode-button-secondaryForeground, #72747c)',
                    fontSize: '12px',
                    padding: '0',
                    float: 'right',
                    marginLeft: 'auto',
                    marginRight: '0',
                  }}
                >
                  刷新
                </Button>
              </div>
            )}
          </div>

          <div className="mcp-tab-content">
            {activeTab === McpActiveTab.market && <McpMarketView onConfigure={handleConfigure} />}
            {activeTab === McpActiveTab.installed && (
              <McpInstalledView
                ref={installedViewRef}
                openDefaultServer={openDefaultServer}
                onOpenDefaultServerUsed={handleOpenDefaultServerUsed}
              />
            )}
          </div>
        </div>
      </div>
      <McpModal
        isManualConfigModalVisible={isManualConfigModalVisible}
        selectedService={null}
        onClose={handleCloseModal}
        onInstalledSuccess={handleInstalledSuccess}
      />
    </div>
  );
}
