import React, { useState, useEffect, useRef, useCallback, UIEvent } from 'react';
import { Popover, Input, Spin, message as antdMessage, Checkbox } from 'antd';
import { McpModal, adjustServerParam } from './McpModal';

import { debounce } from 'lodash';
import './McpMarketView.scss';
import { colors, generateColorForText } from './Avatar';
import {
  McpDeploymentType,
  getDeploymentTypeStr,
  McpNodeService,
  McpSendMsgType,
  McpCommonSendMsgType,
} from '@joycoder/shared/src/mcp/McpTypes';
import { useHandleMessage } from '../../hooks/useHandleMessage';
import { mcpPostMessage } from './McpSendMsgUtils';

type McpMarketViewProps = {
  // 如果需要props，可以在这里定义
  onConfigure: (serverName: string) => void;
};

const McpMarketView: React.FC<McpMarketViewProps> = React.memo(({ onConfigure }) => {
  const isInitialMount = useRef(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [quickConfig, setQuickConfig] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isManualConfigModalVisible, setIsManualConfigModalVisible] = useState(false);
  const [selectedService, setSelectedService] = useState<McpNodeService | null>(null);
  const [mcpServices, setMcpServices] = useState<McpNodeService[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(30);
  const [loading, setLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [installedServers, setInstalledServers] = useState<string[]>([]);

  //const [installParamContent, setInstallParamContent] = useState<string>('');

  useHandleMessage(({ type, data }) => {
    if (type === McpCommonSendMsgType.SETTING_MCP && data.type === McpSendMsgType.MCP_LIST) {
      const resMcpList = data.mcpList || [];
      try {
        if (resMcpList.success && resMcpList.data.code === 0) {
          const mcpList = resMcpList.data.data.list || [];
          setMcpServices((prevServices) => {
            const updatedServices = [...prevServices, ...mcpList];
            setHasMore(updatedServices.length < resMcpList.data.data.total);
            return updatedServices;
          });
          setTotal(resMcpList.data.data.total);
        } else {
          console.error('获取 MCP 服务列表失败:', resMcpList.data?.msg || resMcpList.message);
        }
      } catch (error) {
        console.error('获取 MCP 服务列表失败:', error);
      }
      setTimeout(() => {
        setLoading(false);
      }, 200);
    }
    try {
      if (type === McpCommonSendMsgType.SETTING_MCP && data.type === McpSendMsgType.GET_MCP_SETTINGS_FILE_CONTENT) {
        const mcpServers = data.fileContent?.mcpServers;
        if (mcpServers) {
          const serverNames = Object.keys(mcpServers);
          setInstalledServers(serverNames);
        }
      }
    } catch (error) {
      console.error('MCP 获取设置文件内容失败:', error);
    }
  });

  const fetchMcpList = useCallback((page: number, query: string, size: number, quickConfig: boolean) => {
    setLoading(true);
    const expressSetup = quickConfig ? true : undefined;
    const trimQuery = query && query.length > 0 ? query.trim() : query;
    mcpPostMessage(McpSendMsgType.MCP_LIST, { keyword: trimQuery, pageNum: page, pageSize: size, expressSetup });
    setTimeout(() => {
      setLoading(false);
    }, 500);
  }, []);

  const debouncedFetchMcpList = useCallback(
    debounce((page: number, query: string, size: number, quickConfig: boolean) => {
      fetchMcpList(page, query, size, quickConfig);
    }, 500), // 增加延迟时间到500ms
    [fetchMcpList]
  );

  const handleScroll = useCallback(
    (event: UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
      if (scrollHeight - scrollTop <= clientHeight * 1.5 && !loading && hasMore) {
        setLoading(true);
        setCurrentPage((prevPage) => {
          const nextPage = prevPage + 1;
          debouncedFetchMcpList(nextPage, searchQuery, pageSize, quickConfig);
          return nextPage;
        });
      }
    },
    [loading, hasMore, searchQuery, pageSize, debouncedFetchMcpList]
  );

  useEffect(() => {
    mcpPostMessage(McpSendMsgType.GET_MCP_SETTINGS_FILE_CONTENT);
    debouncedFetchMcpList(1, searchQuery, pageSize, quickConfig);
  }, []); // 空依赖数组，确保只在挂载时执行一次

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }
    setMcpServices([]);
    setCurrentPage(1);
    setHasMore(true); // 重置hasMore状态
    debouncedFetchMcpList(1, searchQuery, pageSize, quickConfig);
  }, [searchQuery, pageSize, debouncedFetchMcpList, quickConfig]);
  const manualConfigModalVisible = () => {
    setIsManualConfigModalVisible(true);
  };

  const filteredServices = mcpServices;

  const handleInstall = (service: McpNodeService) => {
    setSelectedService(service);
    setIsModalVisible(true);
  };

  const handleModalClose = () => {
    cleanModelParam();
  };
  const cleanModelParam = () => {
    // 清除安装状态
    setIsManualConfigModalVisible(false);
    setIsModalVisible(false);
    setSelectedService(null);
  };

  const handleConfigure = (serverName: string) => {
    onConfigure(serverName);
    // 打开配置页面
    // mcpPostMessage(McpSendMsgType.OPEN_OLD_MCP_SERVER_CONFIG_PAGE);
    // //刷新服务
    // mcpPostMessage(McpSendMsgType.REFRESH_MCP_SERVICE);
    // 实现配置逻辑
  };

  const getDefaultName = (service: any) => {
    let defaultName = '';
    if (service.displayName && service.displayName.length > 0) {
      defaultName = service.displayName.slice(0, 2);
    } else if (service.name && service.name.length > 0) {
      defaultName = service.name.slice(0, 2);
    } else {
      defaultName = 'e';
    }
    const finalColor = generateColorForText(defaultName);
    return { defaultName, finalColor };
  };

  const renderLogo = (service: any) => {
    if (service.logoUrl) {
      return <img src={service.logoUrl} alt={service.name} className="mcp-market-view__service-logo" />;
    } else {
      const { defaultName, finalColor } = getDefaultName(service);
      return (
        <div className="mcp-market-view__service-default-logo" style={{ backgroundColor: colors[finalColor] }}>
          {defaultName.toUpperCase()}
        </div>
      );
    }
  };

  const renderPopupContent = (service: any) => (
    <div className="mcp-market-view__popup">
      <div className="mcp-market-view__popup-header">
        <h3>{service.displayName || service.name}</h3>
      </div>
      <div className="mcp-market-view__popup-content">
        <p>更新于: {service.updateTime}</p>
        <p>{service.description}</p>
      </div>
    </div>
  );

  const renderMarketContent = () => (
    <div className="mcp-market-view__content">
      <Input
        className="mcp-market-view__search"
        placeholder="搜索 MCP 服务"
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        prefix={<span className="icon iconfont icon-sousuo" />}
      />

      <div className="mcp-market-view__options">
        <div className="mcp-market-view__manual-config">
          想要添加市场以外的 MCP Servers？打开&nbsp;
          <a href="#" onClick={() => mcpPostMessage(McpSendMsgType.OPEN_MCP_SETTING_FILE)} style={{ color: '#247FFF' }}>
            全局配置
          </a>
          &nbsp; 或 &nbsp;
          <a
            href="#"
            onClick={() => mcpPostMessage(McpSendMsgType.OPEN_MCP_SETTING_FILE, { projectType: 'project' })}
            style={{ color: '#247FFF' }}
          >
            项目配置
          </a>
        </div>
        {/* <Checkbox
          className="joycoder-setting-box-context"
          checked={quickConfig}
          onChange={(e) => {
            setMcpServices([]);
            setQuickConfig(e.target.checked);
          }}
        >
          快速配置
        </Checkbox> */}
      </div>

      <div className="mcp-market-view__service-list" onScroll={handleScroll}>
        {filteredServices.map((service) => (
          <div key={service.serviceId} className="mcp-market-view__service-item">
            <div style={{ position: 'relative', display: 'flex', alignItems: 'stretch', width: 'calc(100% - 55px)' }}>
              {/* <Popover
                content={() => renderPopupContent(service)}
                title={null}
                trigger="hover"
                placement="left"
              > */}
              <div className="mcp-market-view__service-icon">{renderLogo(service)}</div>
              <div className="mcp-market-view__service-info">
                <div>
                  <h3 className="mcp-market-view__service-name">
                    {service.name}
                    {(() => {
                      if (service.depType === McpDeploymentType.HOSTED || service.expressSetup) {
                        return (
                          <span
                            className="icon iconfont icon-shandian"
                            style={{
                              background: 'linear-gradient(146.23deg, rgba(88,177,242,1) 0%, rgba(255,38,61,1) 100%)',
                              WebkitBackgroundClip: 'text',
                              WebkitTextFillColor: 'transparent',
                              backgroundClip: 'text',
                              display: 'inline-block',
                              marginLeft: '13px',
                              marginRight: '13px,',
                            }}
                          />
                        );
                      }
                      return null;
                    })()}

                    <button className="mcp-button mcp-button__deployment-type">
                      {service.depType === McpDeploymentType.HOSTED ? (
                        <span className="icon iconfont icon-juli" style={{ color: '#247FFF' }} />
                      ) : (
                        <span className="icon iconfont icon-bendibushu" style={{ color: 'rgba(44,179,56,1)' }} />
                      )}
                      {getDeploymentTypeStr(service.depType)}
                    </button>
                  </h3>
                  <p className="mcp-market-view__service-description">{service.description}</p>
                  <div className="mcp-market-view__service-creator">{/* @{service.uploader} */}</div>
                </div>
              </div>
              <div style={{ position: 'relative', display: 'flex', alignItems: 'center' }}></div>
              {/* </Popover> */}
            </div>
            <div className="mcp-market-view__service-button-container">
              {(() => {
                let serverName = '';
                if (service.depType === McpDeploymentType.HOSTED && service.isPluginMcp && service.pluginId) {
                  serverName = service.name;
                } else {
                  const parsedParam = adjustServerParam(service, service.serverParam || '{}');
                  serverName =
                    (JSON.parse(parsedParam).mcpServers && Object.keys(JSON.parse(parsedParam).mcpServers)[0]) || '';
                }
                const isInstalled = installedServers.includes(serverName);

                return isInstalled ? (
                  <button className="mcp-button mcp-button__configure" onClick={() => handleConfigure(service.name)}>
                    设置
                  </button>
                ) : (
                  <button className="mcp-button mcp-button__install" onClick={() => handleInstall(service)}>
                    安装
                  </button>
                );
              })()}
            </div>
          </div>
        ))}
        {!hasMore && <p style={{ textAlign: 'center', marginBottom: '0' }}>没有更多了~</p>}
      </div>
    </div>
  );

  return (
    <div className="mcp-market-view">
      {renderMarketContent()}
      {loading && (
        <div className="mcp-market-view__loading">
          <Spin />
        </div>
      )}
      <McpModal
        isModalVisible={isModalVisible}
        isManualConfigModalVisible={isManualConfigModalVisible}
        selectedService={selectedService}
        onClose={handleModalClose}
        onInstalledSuccess={(serviceNames) => {
          if (serviceNames && serviceNames.length > 0) {
            setInstalledServers(serviceNames);
          }
        }}
      />
    </div>
  );
});

export default McpMarketView;
