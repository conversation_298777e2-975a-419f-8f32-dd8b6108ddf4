.mcp-installed-view {
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  background-color: var(--vscode-editor-background);
  color: var(--vscode-foreground);
  position: relative;
  gap: 8px;

  &__loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: var(--vscode-editor-background);
      opacity: 0.5;
  }
  .header-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 8px;
  }

  .refresh-all-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--vscode-button-hoverBackground);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    svg {
      transition: transform 0.3s ease;
    }

    &:hover svg {
      transform: rotate(180deg);
    }
  }

  .server-item {
    border-radius: 8px;
    border: 1px solid var(--vscode-widget-border);
    background-color: var(--vscode-editor-background);
    transition: all 0.2s ease;

    &:hover {
      //border-color: var(--vscode-focusBorder);
      //box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .server-header {
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    //cursor: pointer;
    border-radius: 8px 8px 0 0;

    &:hover {
      background-color: var(--vscode-list-hoverBackground);
    }
  }
  .server-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .server-right {
    display: flex;
    align-items: center;
  }
  .server-center {
    height: 20px;
    background: var(--vscode-button-secondaryBackground);
    border-radius: 2px;
    color: var(--vscode-button-secondaryForeground);
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0px;
  }

  .expand-button {
    background: none;
    border: none;
    color: var(--vscode-foreground);
    cursor: pointer;
    padding: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;

    &.expanded {
      transform: rotate(90deg);
    }

    &:hover {
     // background-color: var(--vscode-toolbar-hoverBackground);
      border-radius: 2px;
    }
  }

  .server-name {
    font-weight: 600;
    font-size: 15px;
    color: var(--vscode-foreground);
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    box-shadow: 0 0 0 2px var(--vscode-editor-background);
    flex-shrink: 0;
    position: relative;
    cursor: help;

    &.connected {
      background-color: #22c55e;
      box-shadow: 0 0 0 2px var(--vscode-editor-background), 0 0 0 1px #22c55e;
    }

    &.error {
      background-color: #ef4444;
      box-shadow: 0 0 0 2px var(--vscode-editor-background), 0 0 0 1px #ef4444;
    }

    &.loading {
      background-color: #f59e0b;
      animation: pulse 1.5s infinite;
      box-shadow: 0 0 0 2px var(--vscode-editor-background), 0 0 0 1px #f59e0b;
    }

    &.disconnected {
      background-color: #6b7280;
      box-shadow: 0 0 0 2px var(--vscode-editor-background), 0 0 0 1px #6b7280;
    }
  }

  .status-text {
    font-size: 12px;
    font-weight: 500;

    &.connected {
      color: #22c55e;
    }

    &.error {
      color: #ef4444;
    }

    &.loading {
      color: #f59e0b;
    }

    &.disconnected {
      color: #6b7280;
    }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  .error-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
  }

  .retry-button, .ai-fix-button {
    background: none;
    border: none;
    color: #007acc;
    cursor: pointer;
    font-size: 12px;
    text-decoration: underline;
    padding: 0;

    &:hover {
      color: #005a9e;
    }
  }

  .separator {
    color: var(--vscode-descriptionForeground);
    font-size: 12px;
  }

  .refresh-button, .quick-action-button {
    background: none;
    border: none;
    color: var(--vscode-foreground);
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      //background-color: var(--vscode-toolbar-hoverBackground);
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  .server-content {
    padding: 0 12px 12px 12px;
    border-top: 1px solid var(--vscode-widget-border);
  }

  .server-description {
    font-size: 13px;
    color: var(--vscode-descriptionForeground);
    margin-bottom: 12px;
    padding-top: 8px;
    line-height: 1.4;
  }

  .server-tools {
   // background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 4px;
  }

  .tools-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid var(--vscode-widget-border);
  }

  .tools-left {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 13px;
    font-weight: 500;
  }

  .tab-buttons {
    display: flex;
    gap: 4px;
  }

  .tab-button {
    background: none;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    color: var(--vscode-descriptionForeground);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      //background-color: var(--vscode-toolbar-hoverBackground);
      color: var(--vscode-foreground);
    }

    &.active {
     // background-color: var(--vscode-button-background);
      //color: var(--vscode-button-foreground);

      &:hover {
       // background-color: var(--vscode-button-hoverBackground);
      }
    }
  }

  .tools-right {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .tools-count {
    color: var(--vscode-foreground);
    font-weight: 600;
  }

  .resource-count {
    color: var(--vscode-descriptionForeground);
    font-weight: normal;
    font-size: 12px;
  }

  .edit-button, .delete-button {
    background: none;
    border: none;
    color: var(--vscode-foreground);
    cursor: pointer;
    padding: 4px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: var(--vscode-toolbar-hoverBackground);
    }
  }

  .delete-button:hover {
    color: #f44336;
  }

  .tools-list, .resources-list {
    padding: 8px 0;
  }

  .tool-item, .resource-item {
    padding: 12px 16px;
    border-bottom: 1px solid var(--vscode-widget-border);
    background-color: var(--vscode-editor-background);

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: var(--vscode-list-hoverBackground);
    }
  }

  .tool-header, .resource-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .tool-name, .resource-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--vscode-foreground);
    font-family: var(--vscode-editor-font-family);
  }

  .resource-uri {
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    font-family: var(--vscode-editor-font-family);
    background-color: var(--vscode-textCodeBlock-background);
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 8px;
  }

  .resource-description {
    font-size: 13px;
    color: var(--vscode-descriptionForeground);
    line-height: 1.5;
    margin-bottom: 8px;
    padding: 8px 12px;
    background-color: var(--vscode-textCodeBlock-background);
    border-radius: 6px;
    border-left: 4px solid var(--vscode-textLink-foreground);
    font-style: italic;
  }

  .resource-mime-type {
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    font-weight: 500;
  }

  .tool-auto-approve {
    .auto-approve-checkbox {
      display: flex;
      align-items: center;
      gap: 6px;
      cursor: pointer;
      font-size: 12px;
      color: var(--vscode-foreground);

      input[type="checkbox"] {
        width: 14px;
        height: 14px;
        cursor: pointer;
        accent-color: var(--vscode-checkbox-background);

        &:checked {
          accent-color: var(--vscode-checkbox-selectBackground);
        }

        &:focus {
          outline: 1px solid var(--vscode-focusBorder);
          outline-offset: 1px;
        }
      }

      .checkbox-label {
        font-weight: 500;
        user-select: none;

        &:hover {
          color: var(--vscode-textLink-foreground);
        }
      }

      &:hover {
        input[type="checkbox"] {
          border-color: var(--vscode-checkbox-selectBorder);
        }
      }
    }
  }

  .tool-description {
    font-size: 13px;
    color: var(--vscode-descriptionForeground);
    line-height: 1.5;
    margin-bottom: 12px;
    padding: 10px 12px;
    background-color: var(--vscode-textCodeBlock-background);
    border-radius: 6px;
   // border-left: 4px solid var(--vscode-textLink-foreground);
   // font-style: italic;
    position: relative;

    &:before {
      content: "";
      position: absolute;
      left: 8px;
      top: 6px;
      font-size: 16px;
      color: var(--vscode-textLink-foreground);
      opacity: 0.6;
    }

    &:after {
      content: "";
      position: absolute;
      right: 8px;
      bottom: 6px;
      font-size: 16px;
      color: var(--vscode-textLink-foreground);
      opacity: 0.6;
    }
  }

  .tool-schema {
    margin-top: 12px;
  }

  .schema-title {
    font-size: 13px;
    font-weight: 600;
    color: var(--vscode-foreground);
    margin-bottom: 8px;
    display: flex;
    align-items: center;

    // &:before {
    //   content: "⚙️";
    //   margin-right: 6px;
    // }
  }

  .schema-content {
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 4px;
    padding: 8px;
  }

  .schema-empty {
    color: var(--vscode-descriptionForeground);
    font-style: italic;
    font-size: 12px;
  }

  .schema-properties {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .schema-property {
    padding: 12px;
    background-color: var(--vscode-editor-background);
    border-radius: 6px;
    border: 1px solid var(--vscode-widget-border);
    position: relative;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--vscode-focusBorder);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &:before {
      content: "•";
      position: absolute;
      left: 4px;
      top: 12px;
      color: var(--vscode-textLink-foreground);
      font-weight: bold;
    }
  }

  .property-header {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 4px;
  }

  .property-name {
    font-family: var(--vscode-editor-font-family);
    font-weight: 600;
    color: var(--vscode-symbolIcon-variableForeground);
    font-size: 13px;
  }

  .property-required {
    color: var(--vscode-errorForeground);
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 16px;
    font-weight: 500;
  }

  .property-type {
    color: var(--vscode-symbolIcon-keywordForeground);
    font-size: 11px;
    font-style: italic;
  }

  .property-description {
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    line-height: 1.4;
    margin-bottom: 4px;
  }

  .property-default,
  .property-enum,
  .property-examples {
    font-size: 11px;
    color: var(--vscode-descriptionForeground);
    margin-top: 4px;

    code {
      background-color: var(--vscode-textCodeBlock-background);
      padding: 1px 4px;
      border-radius: 2px;
      font-family: var(--vscode-editor-font-family);
      color: var(--vscode-textPreformat-foreground);
      margin: 0 1px;
    }
  }

  .property-enum {
    .enum-value {
      background-color: var(--vscode-badge-background);
      color: var(--vscode-badge-foreground);
      padding: 1px 4px;
      border-radius: 2px;
      font-size: 10px;
      margin: 0 2px;
    }
  }

  .property-examples {
    code {
      background-color: var(--vscode-inputValidation-infoBackground);
      color: var(--vscode-inputValidation-infoForeground);
      border: 1px solid var(--vscode-inputValidation-infoBorder);
    }
  }
  .joycoder-dark-switch-checked{
    background-color: var(--vscode-testing-iconPassed) !important;
  }
  .joycoder-light-switch-checked{
    background-color: var(--vscode-testing-iconPassed) !important;
  }
  .switch {
    position: relative;
    display: inline-block;
    width: 36px;
    height: 20px;
    margin-right: 20px;
    .joycoder-dark-switch-checked{
      background-color: var(--vscode-testing-iconPassed);
    }
    .joycoder-light-switch-checked{
      background-color: var(--vscode-testing-iconPassed);
    }
  }

  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    transition: all 0.3s ease;
    border-radius: 20px;

    &:before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 1px;
      bottom: 1px;
      background-color: var(--vscode-foreground);
      transition: all 0.3s ease;
      border-radius: 50%;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
  }

  input:checked + .slider {
    background-color: var(--vscode-testing-iconPassed);
    border-color: var(--vscode-testing-iconPassed);

    &:before {
      transform: translateX(16px);
      background-color: white;
    }
  }

  input:focus + .slider {
    box-shadow: 0 0 0 2px var(--vscode-testing-iconPassed);
  }

  .slider:hover {
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 24px;
    text-align: center;
    color: var(--vscode-descriptionForeground);

    .empty-icon {
      margin-bottom: 16px;
      opacity: 0.6;

      svg {
        color: var(--vscode-descriptionForeground);
      }
    }

    h3 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--vscode-foreground);
    }

    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.5;
      max-width: 400px;
    }
  }
}
