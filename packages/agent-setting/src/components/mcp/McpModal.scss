

.mcp-modal-view {

  &__modal {
    border-radius: 10px;

    .joycoder-dark-modal-content{
      background-color: var(--vscode-editor-background)!important;
      color: var(--vscode-foreground) !important;
      border-radius: 10px;

      .joycoder-dark-modal-body{
        background-color: var(--vscode-editor-background)!important;
        color: var(--vscode-foreground) !important;
        padding: 16px 24px;

        .joycoder-dark-radio-checked {
          .joycoder-dark-radio-inner {
            border-color: var(--vscode-input-foreground) !important;

            &::after {
              background-color: var(--vscode-input-foreground) !important;
            }
          }
        }
      }
      .joycoder-dark-modal-close {
        color: var(--vscode-foreground) !important;
      }
      .joycoder-dark-modal-header{
        background-color: var(--vscode-editor-background) !important;
        color: var(--vscode-foreground) !important;
        border-radius: 10px 10px 0 0;
      }
      .joycoder-dark-modal-footer{
        background-color: var(--vscode-editor-background) !important;
        color: var(--vscode-foreground) !important;
        border-radius: 0 0 10px 10px;
        .joycoder-dark-btn-default,.joycoder-dark-btn-default:hover, .joycoder-dark-btn-default:focus {
          background: var(--vscode-breadcrumb-background)!important;
          color: var(--vscode-breadcrumb-foreground)!important;
        }
        .joycoder-dark-btn-primary, .joycoder-dark-btn-primary:hover, .joycoder-dark-btn-primary:focus{
          background: var(--vscode-button-secondaryBackground)!important;
          color: var(--vscode-button-secondaryForeground)!important;
          border: none!important;
        }
      }
    }

    .joycoder-light-modal-content{
      background-color: var(--vscode-editor-background)!important;
      color: var(--vscode-foreground) !important;

      .joycoder-light-modal-body{
        background-color: var(--vscode-editor-background)!important;
        color: var(--vscode-foreground) !important;
      }
      .joycoder-light-modal-header{
        background-color: var(--vscode-editor-background) !important;
        color: var(--vscode-foreground) !important;
      }
      .joycoder-light-modal-footer{
        background-color: var(--vscode-editor-background) !important;
        color: var(--vscode-foreground) !important;
        .joycoder-light-btn-default,.joycoder-light-btn-default:hover, .joycoder-light-btn-default:focus {
          background: var(--vscode-button-secondaryBackground)!important;
          color: var(--vscode-button-secondaryForeground)!important;
        }
        .joycoder-light-btn-primary, .joycoder-light-btn-primary:hover, .joycoder-light-btn-primary:focus{
          background: var(--vscode-button-background)!important;
          color: var(--vscode-button-foreground)!important;
        }
      }
    }

    // 标题样式
    .mcp-modal-title {
      display: flex;
      gap: 8px;
      justify-content: flex-start;

      &-service {
        font-size: 16px;
        font-weight: 500;
        color: var(--vscode-foreground);
        line-height: 1.4;
      }

      &-badges {
        display: flex;
        align-items: center;
        border: 1px solid var(--vscode-input-border, rgba(90, 93, 94, .5));
        border-radius: 4px;
      }

      &-badge {
        display: inline-flex;
        align-items: center;
        padding: 3px 4px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
        line-height: 1.2;
        white-space: nowrap;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;

        &-deployment {
          display: flex;
          align-items: center;
          background-color: var(--vscode-button-secondaryBackground, rgba(90, 93, 94, 0.31));
          color: var(--vscode-button-secondaryForeground, #cccccc);
          border: 1px solid var(--vscode-input-border, rgba(90, 93, 94, 0.5));

          &.hosted {
            background-color: rgba(36, 127, 255, 0.1);
            color: #247FFF;
            border: 1px solid rgba(36, 127, 255, 0.3);
          }

          &.local {
            background-color: rgba(44, 179, 56, 0.1);
            color: rgba(44, 179, 56, 1);
            border: 1px solid rgba(44, 179, 56, 0.3);
          }

          .icon {
            font-size: 10px;
            margin-right: 4px;
          }
        }

        &-local {
          display: flex;
          align-items: center;

          .icon {
            font-size: 10px;
            margin-right: 4px;
          }
        }

        &-hosted {
          display: flex;
          align-items: center;

          .icon {
            font-size: 10px;
            margin-right: 4px;
          }
        }

        &-command {
          color: var(--vscode-foreground, #cccccc);
          font-size: 10px;
        }
      }
    }

    &-content {
     background-color: var(--vscode-input-background) !important;
     color: var(--vscode-input-foreground) !important;
     border-radius: 6px;
    }
    &-tips{
      font-size: 12px;
      color: var(--vscode-descriptionForeground);
      background-color: var(--vscode-editor-background);
      padding-bottom: 10px;
      font-family: PingFang SC;
      a{
        color: rgb(36, 127, 255);
      }
    }

    &-alert {
      margin-bottom: 16px;
    }

    &-loading {
      text-align: center;
      padding: 20px;
    }

    &-env {
      background: var(--vscode-editor-background);
      padding-bottom: 16px;
      h4 {
        color: var(--vscode-foreground);
      }

      &-item {
        margin-bottom: 12px;

        label {
          display: flex;
          margin-bottom: 4px;
          color: var(--vscode-foreground);
          font-size: 14px;
          font-weight: 500;
          justify-content: space-between;
        }

        &-link {
          display: flex;
          align-items: center;
          color: var(--vscode-input-foreground, #72747c);

          a {
            color: var(--vscode-tab-unfocusedInactiveForeground, #72747c);
            text-decoration: none;

            &:hover {
              color: var(--vscode-input-foreground, #72747c);

              i {
                color: var(--vscode-input-foreground, #72747c);
              }
            }
          }

          &-icon {
            margin-left: 4px;
            color: var(--vscode-tab-unfocusedInactiveForeground, #72747c);
          }
        }
      }

      &-error {
        color: #ff4d4f;
        font-size: 12px;
        line-height: 1.5;
        margin-top: 4px;
        margin-bottom: 0;
      }

      &-description {
        font-size: 12px;
        color: var(--vscode-descriptionForeground);
        margin-bottom: 4px;
        line-height: 1.4;

        a {
          color: var(--vscode-textLink-foreground);
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      &-description {
        font-size: 12px;
        color: var(--vscode-descriptionForeground);
        margin-bottom: 4px;
        line-height: 1.4;

        a {
          color: var(--vscode-textLink-foreground);
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }
      &-input {
        color: var(--vscode-input-foreground);
      }
    }

    &-json-container {
      position: relative;
      height: 240px;
      //border-radius: 6px;
      overflow: hidden;
    }

    &-json-textarea {
      width: 100%;
      height: 100%;
      background: transparent;
      border: none;
      color: var(--vscode-input-foreground);
      font-family: monospace;
      padding: 10px;
      outline: 0px solid var(--vscode-input-foreground) !important;
      outline-offset: -1px !important;
      border-radius: 6px;
      resize : none
    }

    &-advanced-options {
      margin-top: 16px;

      &-item {
        font-size: 12px;
        display: flex;
        align-items: center;
        color: var(--vscode-input-foreground);

        .joycoder-dark-radio-wrapper{
          font-size: 12px !important;
          color: var(--vscode-input-foreground);
          align-items: center;

          .joycoder-dark-radio {
            top: 0!important;

            &-inner {
              width: 12px;
              height: 12px;
            }
            &-inner::after {
              width: 10px;
              height: 10px;
              margin-top: -5px;
              margin-left: -5px;
            }
          }
        }
      }
    }
  }
}
