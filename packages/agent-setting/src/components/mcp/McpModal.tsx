import React, { useState, useEffect, memo } from 'react';
import { Modal, Alert, Spin, Input, Radio, Tooltip } from 'antd';
import './McpModal.scss';
import { McpSettingsSchema } from '@joycoder/shared/src/mcp/McpSettingsSchema';
import { mcpPostMessage } from './McpSendMsgUtils';
import {
  McpDeploymentType,
  McpNodeService,
  McpSendMsgType,
  McpCommonSendMsgType,
  getDeploymentTypeStr,
} from '@joycoder/shared/src/mcp/McpTypes';
import { useHandleMessage } from '../../hooks/useHandleMessage';

// 环境变量配置项类型定义
interface EnvConfigItem {
  title: string;
  placeholder: string;
  required: boolean;
  key: string;
  url?: string;
  type: string;
  description?: string;
  isPassword?: boolean;
  default?: string;
  argsIndex?: number; // 用于 args 类型的索引
}

// 支持两种数据格式的环境变量配置类型
type EnvDataType = Record<string, string> | EnvConfigItem[];

interface McpModalProps {
  isModalVisible?: boolean;
  isManualConfigModalVisible: boolean;
  selectedService: McpNodeService | null;
  onClose: () => void;
  onInstalledSuccess: (installedServers: string[]) => void;
  advancedConfig?: boolean;
}

export const McpModal: React.FC<McpModalProps> = memo(
  ({
    isModalVisible,
    isManualConfigModalVisible,
    selectedService,
    onClose,
    onInstalledSuccess,
    advancedConfig = true,
  }) => {
    const [editedServerParam, setEditedServerParam] = useState('');
    const [isModalConfirmLoading, setIsModalConfirmLoading] = useState<boolean>(false);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const [envData, setEnvData] = useState<EnvDataType>({});
    const [isInstallLoading, setIsInstallLoading] = useState<boolean>(false);
    const [successMessage, setSuccessMessage] = useState<string | null>(null);
    const [scope, setScope] = useState<string>('global');
    const [isCollapseOpen, setIsCollapseOpen] = useState<boolean>(false);
    const [isUpdatingFromEnv, setIsUpdatingFromEnv] = useState<boolean>(false);
    const [envValidationErrors, setEnvValidationErrors] = useState<Record<string, string>>({});

    useHandleMessage(({ type, data }) => {
      if (type === McpCommonSendMsgType.SETTING_MCP && data.type === McpSendMsgType.GET_MCP_INSTALL_PARAM) {
        const installParam = data.mcpInstallParam || {};
        if (installParam.success && installParam.data.code === 0) {
          const param = installParam.data.data;

          const paramStr = JSON.stringify(param, null, 2);
          const adjustedServerParam = adjustServerParam(selectedService, paramStr || '{}');
          setEditedServerParam(adjustedServerParam);
          parseEnvData(paramStr);
        } else {
          setEditedServerParam('MCP 获取安装参数失败:');
        }
        setIsInstallLoading(false);
      }
      if (type === McpCommonSendMsgType.SETTING_MCP && data.type === McpSendMsgType.UPDATE_OR_INSERT_MCP_SERVER) {
        if (data.fileContent.error) {
          setErrorMessage(data.fileContent.error);
          setIsModalConfirmLoading(false);
        } else {
          const mcpServers = data.fileContent?.mcpServers;
          if (mcpServers) {
            const serverNames = Object.keys(mcpServers);
            onInstalledSuccess(serverNames);
          }
          setTimeout(() => {
            setSuccessMessage('安装成功');
          }, 200);
          // 延迟2秒后关闭模态框
          setTimeout(() => {
            cleanModelParam();
          }, 500);
          //安装成功后 刷新mcp服务
          mcpPostMessage(McpSendMsgType.REFRESH_MCP_SERVICE);
        }
      }
    });
    useEffect(() => {
      onOpenModal();
    }, [isModalVisible, isManualConfigModalVisible]); // 空依赖数组，确保只在挂载时执行一次

    // 合并原始环境变量配置和用户已输入的值
    const mergeEnvDataWithUserInput = (originalEnvConfig: EnvConfigItem[], serverParam: string): EnvConfigItem[] => {
      try {
        const parsedParam = JSON.parse(serverParam);
        let userEnvValues: Record<string, string> = {};
        let userArgsValues: string[] = [];

        if (parsedParam?.mcpServers) {
          const serverName = Object.keys(parsedParam.mcpServers)[0];
          const serverConfig = parsedParam.mcpServers[serverName];
          if (serverConfig?.env) {
            userEnvValues = serverConfig.env;
          }
          if (serverConfig?.args && Array.isArray(serverConfig.args)) {
            userArgsValues = serverConfig.args;
          }
        } else if (parsedParam?.env) {
          userEnvValues = parsedParam.env;
        }

        // 合并原始配置和用户输入的值
        return originalEnvConfig.map((item) => {
          if (item.type === 'env') {
            // env 类型：从 userEnvValues 中获取值
            return {
              ...item,
              default: item.default ?? userEnvValues[item.key] ?? '',
            };
          } else if (item.type === 'args' && typeof item.argsIndex === 'number') {
            // args 类型：从 userArgsValues 数组中根据 argsIndex 获取值
            const argsValue = userArgsValues[item.argsIndex] ?? '';
            return {
              ...item,
              default: item.default ?? argsValue,
            };
          } else {
            // 其他类型：保持原样
            return {
              ...item,
              default: item.default ?? '',
            };
          }
        });
      } catch (error) {
        // 如果解析失败，返回原始配置
        return originalEnvConfig;
      }
    };

    // 解析 serverParam 中的 env 数据
    const parseEnvData = (serverParam: string) => {
      // 如果正在从环境变量更新，跳过解析以避免循环
      if (isUpdatingFromEnv) {
        return;
      }

      try {
        const parsedParam = JSON.parse(serverParam);
        let envConfig: EnvDataType = {};

        if (parsedParam?.mcpServers) {
          const serverName = Object.keys(parsedParam.mcpServers)[0];
          const serverConfig = parsedParam.mcpServers[serverName];
          if (serverConfig?.env) {
            envConfig = serverConfig.env;
          }
        } else if (parsedParam?.env) {
          envConfig = parsedParam.env;
        }

        // 判断是否为数组格式（包含详细配置信息）
        if (Array.isArray(envConfig)) {
          setEnvData(envConfig);
        } else if (envConfig && typeof envConfig === 'object') {
          setEnvData(envConfig as Record<string, string>);
        } else {
          setEnvData({});
        }
      } catch (error) {
        setEnvData({});
      }
    };

    const onOpenModal = () => {
      setIsCollapseOpen(false); // 重置 Collapse 的状态为关闭
      if (selectedService) {
        if (selectedService.isProject) {
          setScope('project');
        }
        if (selectedService.depType === McpDeploymentType.HOSTED && selectedService.isPluginMcp) {
          setEditedServerParam('');
          mcpPostMessage(McpSendMsgType.GET_MCP_INSTALL_PARAM, {
            serviceId: selectedService.serviceId,
            version: selectedService.version,
          });
        } else {
          const jsonAdjustedServerParam = jsonAdjustServerParam(selectedService.allCommands || '{}');
          const adjustedServerParam = adjustServerParam(selectedService, selectedService.serverParam || '{}');
          // 传递json数据
          setEditedServerParam(adjustedServerParam);
          // 解析出环境变量 传递 env 数据
          // 优先使用 allCommands 中的环境变量配置（数组格式），其次使用 serverParam 中的配置
          if (Array.isArray(jsonAdjustedServerParam) && jsonAdjustedServerParam.length > 0) {
            // 合并原始配置和用户已输入的值
            const mergedEnvData = mergeEnvDataWithUserInput(jsonAdjustedServerParam, adjustedServerParam);
            setEnvData(mergedEnvData);
          } else if (!Array.isArray(jsonAdjustedServerParam) && Object.keys(jsonAdjustedServerParam).length > 0) {
            setEnvData(jsonAdjustedServerParam);
          } else {
            parseEnvData(adjustedServerParam);
          }
          setIsInstallLoading(false);
        }
      }
      if (isManualConfigModalVisible) {
        setEditedServerParam(`//{
//  "mcpServers": {
//    "fetch": {
//      "command": "uvx",
//      "args": [
//        "mcp-server-fetch"
//      ]
//    }
//  }
//}`);
      }
    };

    // 验证单个环境变量字段
    const validateEnvField = (value: string, required: boolean): string => {
      if (required && !value.trim()) {
        return '此字段为必填项';
      }
      return '';
    };

    // 验证所有必填环境变量
    const validateAllRequiredEnvFields = (): boolean => {
      if (!Array.isArray(envData)) {
        return true; // 对于对象格式，跳过前端验证
      }

      const errors: Record<string, string> = {};
      let hasErrors = false;

      envData.forEach((item) => {
        if (item.required) {
          const currentValue = item.default || '';
          const error = validateEnvField(currentValue, item.required);
          if (error) {
            errors[item.key] = error;
            hasErrors = true;
          }
        }
      });

      setEnvValidationErrors(errors);
      return !hasErrors;
    };

    const handleModalOk = () => {
      setIsModalConfirmLoading(true);
      setErrorMessage(null);

      // 首先验证必填环境变量
      const isValid = validateAllRequiredEnvFields();
      if (!isValid) {
        setIsModalConfirmLoading(false);
        return;
      }

      const updatedServerParam = editedServerParam;
      try {
        if (!updatedServerParam || updatedServerParam === '' || updatedServerParam === '{}') {
          throw new Error('MCP 配置文件格式错误，配置文件不能为空');
        }
        const serverParam = JSON.parse(updatedServerParam);
        if (!serverParam || !serverParam.mcpServers || Object.keys(serverParam.mcpServers).length === 0) {
          throw new Error('MCP 配置文件格式错误，缺少 mcpServers 字段');
        }

        const parsedConfig = McpSettingsSchema.safeParse(serverParam);
        if (!parsedConfig.success) {
          throw new Error('MCP 配置文件格式错误，请检查输入内容格式是否正确。');
        }
        mcpPostMessage(McpSendMsgType.UPDATE_OR_INSERT_MCP_SERVER, {
          serverParam: updatedServerParam,
          projectType: scope,
        });
        // 成功消息和窗口关闭逻辑已移至 useHandleMessage 钩子中
      } catch (error) {
        setErrorMessage(error instanceof Error ? error.message : '更新配置失败，请检查输入内容格式是否正确。');
        setIsModalConfirmLoading(false);
      }
    };

    const handleModalCancel = () => {
      cleanModelParam();
    };
    const cleanModelParam = () => {
      // 清除安装状态
      setEditedServerParam('');
      setEnvData({});
      setIsInstallLoading(false);
      setIsModalConfirmLoading(false);
      setErrorMessage(null);
      setSuccessMessage(null);
      setScope('global');
      setEnvValidationErrors({});
      onClose();
    };
    // 检查是否有 env 或 args 类型的配置项
    const hasEnvOrArgsConfig = (): boolean => {
      if (Array.isArray(envData)) {
        return envData.some((item) => item.type === 'env' || item.type === 'args');
      }
      return false;
    };

    const handleServerParamChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      setEditedServerParam(newValue);
      // 重置标志，允许从 JSON 文本区域更新环境变量
      setIsUpdatingFromEnv(false);
      parseEnvData(newValue);
    };

    const handleEnvChange = (key: string, value: string) => {
      // 清除该字段的校验错误
      setEnvValidationErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[key];
        return newErrors;
      });

      // 设置标志，表示正在从环境变量更新
      setIsUpdatingFromEnv(true);

      // 先更新 envData
      setEnvData((prev) => {
        const newEnvData: EnvDataType = Array.isArray(prev)
          ? // 数组格式：更新对应项的值，但保持数组结构
            prev.map((item) => (item.key === key ? { ...item, default: value } : item))
          : // 对象格式：直接更新键值对
            { ...prev, [key]: value };

        // 更新 editedServerParam
        setEditedServerParam((prevParam) => {
          try {
            const updatedServerParam = JSON.parse(prevParam);
            const serverName = Object.keys(updatedServerParam.mcpServers)[0];
            const serverConfig = updatedServerParam.mcpServers[serverName];

            // 根据数据格式更新配置
            if (Array.isArray(newEnvData)) {
              // 分别处理 env 和 args 类型的配置项
              const envObject: Record<string, string> = {};

              newEnvData.forEach((item) => {
                const itemValue = item.default !== undefined ? item.default : item.placeholder || '';

                if (item.type === 'env') {
                  // env 类型：保持现有逻辑，更新 env 对象
                  envObject[item.key] = itemValue;
                } else if (item.type === 'args' && typeof item.argsIndex === 'number') {
                  // args 类型：更新 args 数组中对应索引的元素
                  if (!serverConfig.args) {
                    serverConfig.args = [];
                  }

                  // 确保 args 数组有足够的长度
                  while (serverConfig.args.length <= item.argsIndex) {
                    serverConfig.args.push('');
                  }

                  // 更新对应索引的值
                  serverConfig.args[item.argsIndex] = itemValue;
                }
              });

              // 只有当存在 env 类型的配置项时才更新 env 对象
              const hasEnvType = newEnvData.some((item) => item.type === 'env');
              if (hasEnvType) {
                serverConfig.env = envObject;
              }
            } else {
              // 对象格式：直接更新 env
              serverConfig.env = newEnvData;
            }

            // 重置标志
            setTimeout(() => setIsUpdatingFromEnv(false), 0);

            return JSON.stringify(updatedServerParam, null, 2);
          } catch (error) {
            // 重置标志
            setTimeout(() => setIsUpdatingFromEnv(false), 0);
            return prevParam;
          }
        });

        return newEnvData;
      });
    };

    // 从 editedServerParam 中提取 command
    const getCommandFromServerParam = (serverParam: string): string => {
      try {
        const parsedParam = JSON.parse(serverParam);
        if (parsedParam?.mcpServers) {
          const serverName = Object.keys(parsedParam.mcpServers)[0];
          const serverConfig = parsedParam.mcpServers[serverName];
          return serverConfig?.command || '';
        }
        return '';
      } catch (error) {
        return '';
      }
    };

    // 根据命令类型获取环境要求提示文本
    const getEnvironmentRequirement = (command: string): string => {
      switch (command) {
        case 'node':
        case 'npm':
        case 'npx':
        case 'np':
          return '需本地安装Node.js环境';
        case 'python3':
          return '需本地安装Python3环境';
        case 'python':
        case 'uv':
        case 'uvx':
          return '需本地安装Python环境';
        default:
          return '';
      }
    };

    // 渲染部署类型标签
    const renderDeploymentBadge = (depType?: McpDeploymentType) => {
      if (!depType) return null;

      const isHosted = depType === McpDeploymentType.HOSTED;
      const deploymentText = getDeploymentTypeStr(depType);

      return (
        <span
          className={`mcp-modal-title-badge ${
            isHosted ? 'mcp-modal-title-badge-hosted' : 'mcp-modal-title-badge-local'
          }`}
        >
          {isHosted ? (
            <span className="icon iconfont icon-juli" style={{ color: '#247FFF', marginRight: '4px' }} />
          ) : (
            <span
              className="icon iconfont icon-bendibushu"
              style={{ color: 'rgba(44,179,56,1)', marginRight: '4px' }}
            />
          )}
          {deploymentText}
        </span>
      );
    };

    // 渲染标题组件
    const renderModalTitle = () => {
      if (isManualConfigModalVisible) {
        return (
          <div className="mcp-modal-title">
            <span className="mcp-modal-title-service">手动配置</span>
          </div>
        );
      } else {
        const serviceName = selectedService?.displayName ?? selectedService?.name ?? '';
        const command = getCommandFromServerParam(editedServerParam);
        const environmentRequirement = getEnvironmentRequirement(command);
        return (
          <div className="mcp-modal-title">
            <span className="mcp-modal-title-service">添加 「{serviceName}」</span>
            <Tooltip
              placement="right"
              title={environmentRequirement}
              color="var(--vscode-button-secondaryBackground, #72747C)"
            >
              <div className="mcp-modal-title-badges">
                {renderDeploymentBadge(selectedService?.depType)}
                {command && <span className="mcp-modal-title-badge mcp-modal-title-badge-command">{command}</span>}
              </div>
            </Tooltip>
          </div>
        );
      }
    };

    return (
      <Modal
        title={renderModalTitle()}
        open={isModalVisible || isManualConfigModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        okText="下一步"
        cancelText="取消"
        className="mcp-modal-view__modal"
        confirmLoading={isModalConfirmLoading}
        width={480}
      >
        <div className="mcp-modal-view__modal-content">
          {isManualConfigModalVisible ? (
            <div className="mcp-modal-view__modal-tips">
              请从 MCP Server 的介绍页面获取 JSON 配置信息（推荐使用 npx 或 uvx），并粘贴到输入框中。 注：可能需要调整
              args 和 env 设置
              <br />
            </div>
          ) : selectedService?.depType !== McpDeploymentType.HOSTED && selectedService?.repository ? (
            <div className="mcp-modal-view__modal-tips">
              请从 <a href={selectedService?.repository}>介绍页面</a> 获取 JSON 配置信息（推荐使用 npx 或
              uvx），并粘贴到输入框中。 注：可能需要调整 args 和 env 设置
            </div>
          ) : null}
          <AlertMessage errorMessage={errorMessage} successMessage={successMessage} />
          <ModalContent
            isInstallLoading={isInstallLoading}
            isModalConfirmLoading={isModalConfirmLoading}
            envData={envData}
            editedServerParam={editedServerParam}
            handleEnvChange={handleEnvChange}
            handleServerParamChange={handleServerParamChange}
            envValidationErrors={envValidationErrors}
            hasEnvOrArgsConfig={hasEnvOrArgsConfig()}
          />
        </div>
        {advancedConfig && (
          <div className="mcp-modal-view__modal-advanced-options">
            <AdvancedOptions scope={scope} setScope={setScope} />
          </div>
        )}
      </Modal>
    );
  }
);

const AlertMessage: React.FC<{ errorMessage: string | null; successMessage: string | null }> = memo(
  ({ errorMessage, successMessage }) => {
    if (!errorMessage && !successMessage) return null;
    return (
      <Alert
        message={errorMessage ? '错误' : '成功'}
        description={errorMessage || successMessage}
        type={errorMessage ? 'error' : 'success'}
        showIcon
        className="mcp-modal-view__modal-alert"
      />
    );
  }
);

const ModalContent: React.FC<{
  isInstallLoading: boolean;
  isModalConfirmLoading: boolean;
  envData: EnvDataType;
  editedServerParam: string;
  handleEnvChange: (key: string, value: string) => void;
  handleServerParamChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  envValidationErrors: Record<string, string>;
  hasEnvOrArgsConfig: boolean;
}> = memo(
  ({
    isInstallLoading,
    isModalConfirmLoading,
    envData,
    editedServerParam,
    handleEnvChange,
    handleServerParamChange,
    envValidationErrors,
    hasEnvOrArgsConfig,
  }) => {
    if (isInstallLoading || isModalConfirmLoading) {
      return (
        <div className="mcp-modal-view__modal-loading">
          <Spin />
          <p>{isInstallLoading ? '正在获取安装参数...' : '正在处理...'}</p>
        </div>
      );
    }

    return (
      <div>
        <EnvDataSection envData={envData} handleEnvChange={handleEnvChange} envValidationErrors={envValidationErrors} />
        <div className="mcp-modal-view__modal-json-container">
          <textarea
            value={editedServerParam}
            onChange={handleServerParamChange}
            className="mcp-modal-view__modal-json-textarea"
            disabled={hasEnvOrArgsConfig}
          />
        </div>
      </div>
    );
  }
);
const EnvDataSection: React.FC<{
  envData: EnvDataType;
  handleEnvChange: (key: string, value: string) => void;
  envValidationErrors: Record<string, string>;
}> = memo(({ envData, handleEnvChange, envValidationErrors }) => {
  // 处理数组格式的环境变量配置
  const renderArrayEnvData = (envArray: EnvConfigItem[]) => {
    if (envArray.length === 0) return null;

    return envArray
      .filter((item) => {
        // 只显示 env 类型或者 args 类型且有 argsIndex 属性的配置项
        return item.type === 'env' || (item.type === 'args' && typeof item.argsIndex === 'number');
      })
      .map((item) => {
        const { key, title, placeholder, required, url } = item;
        // 获取当前值：优先从 envData 中获取实际值
        let currentValue = '';
        if (Array.isArray(envData)) {
          const foundItem = envData.find((envItem) => envItem.key === key);
          currentValue = foundItem?.default ?? '';
        } else {
          currentValue = envData[key] ?? '';
        }

        const hasError = envValidationErrors[key];

        return (
          <div key={key} className="mcp-modal-view__modal-env-item">
            <label htmlFor={`env-${key}`}>
              <span>
                {required && <span style={{ color: 'red', marginRight: '4px' }}>*</span>}
                {title}
              </span>
              {url && (
                <span className="mcp-modal-view__modal-env-item-link">
                  <a href={url} target="_blank" rel="noopener noreferrer">
                    <span>获取</span>
                    <i className="mcp-modal-view__modal-env-item-link-icon icon iconfont icon-tiaozhuan" />
                  </a>
                </span>
              )}
            </label>
            {/* {description && (
              <div
                className="mcp-modal-view__modal-env-description"
                style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}
              >
                {description}
                {url && (
                  <a href={url} target="_blank" rel="noopener noreferrer" style={{ marginLeft: '8px' }}>
                    查看详情
                  </a>
                )}
              </div>
            )} */}
            <Input
              id={`env-${key}`}
              className="mcp-modal-view__modal-env-input"
              // type={isPassword ? 'password' : 'text'}
              placeholder={placeholder}
              value={currentValue}
              onChange={(e) => handleEnvChange(key, e.target.value)}
              required={required}
              status={hasError ? 'error' : undefined}
            />
            {hasError && <div className="mcp-modal-view__modal-env-error">{hasError}</div>}
          </div>
        );
      });
  };

  // 处理对象格式的环境变量配置
  const renderObjectEnvData = (envObject: Record<string, string>) => {
    if (Object.keys(envObject).length === 0) return null;

    return Object.entries(envObject).map(([key, value]) => (
      <div key={key} className="mcp-modal-view__modal-env-item">
        <label htmlFor={`env-${key}`}>{key}</label>
        <Input
          id={`env-${key}`}
          className="mcp-modal-view__modal-env-input"
          value={value}
          onChange={(e) => handleEnvChange(key, e.target.value)}
        />
      </div>
    ));
  };

  // 判断数据格式并渲染相应内容
  const renderEnvItems = () => {
    if (Array.isArray(envData)) {
      return renderArrayEnvData(envData);
    } else {
      return renderObjectEnvData(envData);
    }
  };

  const envItems = renderEnvItems();
  if (!envItems || (Array.isArray(envItems) && envItems.length === 0)) {
    return null;
  }

  return (
    <div className="mcp-modal-view__modal-env">
      <h4>环境变量</h4>
      {envItems}
    </div>
  );
});

export function adjustServerParam(service: McpNodeService | null, serverParam: string): string {
  try {
    if (serverParam === '' || serverParam === '{}') {
      return JSON.stringify(
        {
          mcpServers: {},
        },
        null,
        2
      );
    }

    const newServerName = service && service.name ? service.name : '';
    const parsedParam = JSON.parse(serverParam);
    const serverParamObj = parsedParam.mcpServers ? parsedParam.mcpServers : parsedParam;
    if (serverParamObj) {
      delete serverParamObj.isProject;
    }

    // 处理 configs 中 type 为 env 的默认值
    const processEnvDefaults = (configObj: Record<string, unknown>) => {
      if (service?.allCommands) {
        try {
          const allCommands = JSON.parse(service.allCommands);
          const configs = allCommands?.universal?.run?.[0]?.configs;

          if (Array.isArray(configs)) {
            // 确保 env 对象存在
            if (!configObj.env) {
              configObj.env = {};
            }

            // 遍历 configs，处理 type 为 env 的项
            configs.forEach((config: EnvConfigItem) => {
              if (config.type === 'env' && config.title && config.default !== undefined) {
                (configObj.env as Record<string, string>)[config.title] = config.default;
              }
            });
          }
        } catch (error) {
          console.warn('Failed to process env defaults from allCommands:', error);
        }
      }
    };

    const type = serverParamObj.url || serverParamObj.command;

    if (type && newServerName) {
      processEnvDefaults(serverParamObj);
      return JSON.stringify(
        {
          mcpServers: {
            [newServerName]: serverParamObj,
          },
        },
        null,
        2
      );
    }

    if (!type && newServerName) {
      const outServerName = Object.keys(serverParamObj)[0];
      const server = serverParamObj[outServerName];
      if (server) {
        delete server.isProject;
        processEnvDefaults(server);
      }
      return JSON.stringify(
        {
          mcpServers: {
            [newServerName]: serverParamObj[outServerName],
          },
        },
        null,
        2
      );
    }

    if (!newServerName && !type) {
      // 对所有服务器配置处理环境变量默认值
      Object.values(serverParamObj).forEach((serverConfig: unknown) => {
        if (serverConfig && typeof serverConfig === 'object') {
          processEnvDefaults(serverConfig as Record<string, unknown>);
        }
      });

      return JSON.stringify(
        {
          mcpServers: {
            ...serverParamObj,
          },
        },
        null,
        2
      );
    }

    return JSON.stringify(serverParamObj, null, 2);
  } catch (error) {
    return serverParam;
  }
}

export function jsonAdjustServerParam(serverParam: string): EnvConfigItem[] | Record<string, string> {
  try {
    // 如果传入的是空对象字符串，直接返回空对象
    if (serverParam === '{}') {
      return {};
    }

    const newObjectJson = JSON.parse(serverParam);

    // 如果解析后是空对象，返回空对象
    if (!newObjectJson || Object.keys(newObjectJson).length === 0) {
      return {};
    }

    // 从 universal.run[0].configs 中提取环境变量配置
    const runConfig = newObjectJson?.universal?.run?.[0];
    if (runConfig?.configs && Array.isArray(runConfig.configs)) {
      return runConfig.configs;
    }

    // 如果没有 configs，返回空对象
    return {};
  } catch (error) {
    return {};
  }
}

const AdvancedOptions: React.FC<{ scope: string; setScope: (scope: string) => void }> = ({ scope, setScope }) => {
  return (
    <div className="mcp-modal-view__modal-advanced-options-content">
      <div className="mcp-modal-view__modal-advanced-options-item">
        <span style={{ marginRight: '3px' }}>作用范围 : </span>
        <Radio.Group value={scope} onChange={(e) => setScope(e.target.value)}>
          <Radio value="global">全局可用</Radio>
          <Radio value="project">当前项目</Radio>
        </Radio.Group>
      </div>
    </div>
  );
};

export default McpModal;
