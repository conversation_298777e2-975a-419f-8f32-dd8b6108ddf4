.mcp-container {
  // height: 824px;
  display: flex;
  flex-direction: column;
  background-color: var(--vscode-editor-background);
  color: var(--vscode-foreground);
  // min-width: 350px;

}

.mcp-content {
  // padding: 24px;
  // padding-bottom: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.mcp-header {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mcp-title {
  margin: 0;
  font-size: 24px;
  font-family: var(--vscode-font-family);
  font-weight: 600;
  text-align: left;
  color: var(--vscode-foreground);
}

.mcp-tutorial {
  height: 18px;
  font-size: 12px;
  font-family: var(--vscode-font-family);
  font-weight: normal;
  color: var(--vscode-foreground);
  opacity: 0.5;
}

.mcp-tutorial-icon {
  margin-right: 4px;
}

.mcp-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.mcp-tab-buttons {
  display: flex;
  height: 32px;
  margin-bottom: 12px;
  align-items: center;
}

.mcp-tab-content {
  flex: 1;
}

.add-mcp-config-content {
  display: flex;
  margin-left: auto;
}

.installed-content {
  display: flex;
  flex-direction: column;
  padding-top: 12px;

  .service-item {
    display: flex;
    align-items: flex-start;
    overflow: hidden;
    margin-bottom: 20px;

    .service-icon {
      width: 40px;
      height: 40px;
      background-color: var(--vscode-button-background);
      color: var(--vscode-button-foreground);
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      margin-right: 15px;
      font-size: 18px;
      font-weight: bold;
      flex-shrink: 0;
    }

    .service-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .service-name {
        margin: 0;
        height: 20px;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .service-description {
        margin: 5px 0;
        font-size: 12px;
        color: var(--vscode-foreground);
        height: 36px;
        line-height: 18px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .service-creator {
        font-size: 12px;
        color: var(--vscode-foreground);
        height: 18px;
        line-height: 18px;
      }
    }

    .configure-button {
      align-self: center;
      flex-shrink: 0;
    }
  }
}
