export const colors: any = {
  'SR-1': '#B68554',
  'SR-2': '#AFA15A',
  'SR-3': '#899D6C',
  'SR-4': '#6C9D7C',
  'SR-5': '#4EA29B',
  'SR-6': '#4293AE',
  'SR-7': '#4179C8',
  'SR-8': '#555FCE',
  'SR-9': '#6D5BC8',
  'SR-10': '#765AAF',
  'SR-11': '#9160A9',
  'SR-12': '#A3668A',
  'SR-13': '#A3668A',
  'SR-14': '#BD656D',
};

export const colorKeys = Object.keys(colors);

// 根据文字生成固定的颜色
export const generateColorForText = (text = '') => {
  // 从localStorage中获取已保存的颜色，如果不存在则返回空对象
  const savedColors = JSON.parse(localStorage.getItem('avatarColors') || '{}');

  // 如果该文本已有对应的颜色，直接返回
  if (savedColors[text]) {
    return savedColors[text];
  }

  // 使用文字的 charCode 来生成一个固定的索引
  let sum = 0;
  for (let i = 0; i < text.length; i++) {
    // 累加每个字符的Unicode编码值
    sum += text.charCodeAt(i);
  }
  // 使用累加和对颜色数组长度取模，确保索引在有效范围内
  const colorIndex = sum % colorKeys.length;
  // 根据计算出的索引选择对应的颜色
  const selectedColor = colorKeys[colorIndex];

  // 将新生成的颜色保存到localStorage中
  savedColors[text] = selectedColor;
  localStorage.setItem('avatarColors', JSON.stringify(savedColors));

  // 返回选择的颜色
  return selectedColor;
};
