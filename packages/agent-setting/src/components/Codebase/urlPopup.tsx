import React, { useRef, useCallback, useEffect } from 'react';
import { Modal, Form, Input, message } from 'antd';
import layout from 'antd/lib/layout';
import { FormInstance } from 'antd/es/form';

interface URLPopupProps {
  open: boolean;
  title?: string;
  baseUrl?: string;
  datasetId?: string;
  setUrlPopup: (open: boolean) => void;
  getNormalUrlData: (args: any) => any;
}

interface ParamsProps {
  docUrl?: string;
  docName?: string;
  name?: string;
  datasetId?: string;
  source_type?: string;
  type?: string;
  location?: string;
}

export default function URLPopup({ open = false, title, setUrlPopup, datasetId, getNormalUrlData }: URLPopupProps) {
  const formRef = useRef<FormInstance>(null);
  const isMountedRef = useRef(true);

  // 组件卸载时标记为未挂载
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const handleOk = useCallback(async () => {
    try {
      // 检查组件是否仍然挂载
      if (!isMountedRef.current || !formRef.current) return;
      const values = await formRef.current.validateFields();
      if (!values) return;
      const docUrl = values.docUrl;
      let urlParams: ParamsProps = {
        docUrl,
        docName: values.docName,
        datasetId,
      };
      try {
        if (docUrl.includes('joyspace.jd.com')) {
          // joyspace处理逻辑
          urlParams = {
            docUrl,
            docName: values.docName,
            type: 'joyspace',
            datasetId,
          };
        }
        const res = await getNormalUrlData(urlParams);
        if (res.code === 0) {
          setUrlPopup(false);
          if (formRef.current) {
            formRef.current.resetFields();
          }
          return message.success('文档上传成功！');
        } else {
          return message.error('文档上传失败！');
        }
      } catch (error) {
        console.log('%c [ error ]-70', 'font-size:13px; background:pink; color:#bf2c9f;', error);
        return message.error('获取文档数据失败');
      }
    } catch (error) {
      console.error('%c [ error ]-29', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  }, [datasetId, getNormalUrlData, setUrlPopup]);

  const handleCancel = useCallback(() => {
    // Handle Cancel action
    setUrlPopup(false);
    if (formRef.current) {
      formRef.current.resetFields();
    }
  }, [setUrlPopup]);
  return (
    <Modal
      title={title || '编辑文档集'}
      open={open}
      okText="确定"
      cancelText="取消"
      onOk={handleOk}
      onCancel={handleCancel}
    >
      <Form {...layout} ref={formRef} name="control-ref">
        <Form.Item
          name="docName"
          validateTrigger={['onBlur']}
          label="文档名称"
          rules={[{ required: true, message: '请输入文档集名称' }]}
        >
          <Input placeholder="请输入文档集名称" allowClear />
        </Form.Item>
        <Form.Item
          name="docUrl"
          validateTrigger={['onBlur']}
          label="文档URL"
          rules={[{ required: true, message: '请输入文档URL' }]}
        >
          <Input placeholder="请输入文档URL" allowClear />
        </Form.Item>
      </Form>
    </Modal>
  );
}
