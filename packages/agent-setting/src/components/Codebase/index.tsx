import React, { useEffect, useState } from 'react';
import { Button, Progress, Popconfirm, Dropdown, Collapse, Tag } from 'antd';
import { CodebaseIndexingStatus } from '@joycoder/shared/src/types/codebase';
import { ExportOutlined, EditOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { useHandleMessage } from '../../hooks/useHandleMessage';
import DocList from './docList';

interface KnowledgeBase {
  name: string;
  id: string;
  permission?: string;
  update_date?: string;
  tenant_id?: string;
  status?: string;
  description?: string;
  update_time: number;
  create_time?: number;
  token_num?: number;
}
interface DocInfo {
  name: string;
  id: string;
  dataset_id?: string;
  knowledgebase_id?: string;
  permission?: string;
  update_date?: string;
  tenant_id?: string;
  status?: string;
  description?: string;
  update_time: number;
  create_time?: number;
  token_num?: number;
  source_type?: string;
  progress?: number;
  progress_msg?: string;
  type?: string;
  run?: string;
}
interface CodebaseTabProps {
  isIDE: boolean;
  hasWorkspaceFolder: boolean;
  codebaseIndexingProgress: number;
  codebaseIndexingStatus: CodebaseIndexingStatus;
  codebaseIndexingButtonDisabled: boolean;
  onCodebaseIndexing: (action: string) => () => void;
  isRemoteEnvironment: boolean;
  openLocalPopup: (args: any) => void;
  openUrlPopup: (args: any) => void;
  editKnowledgeBase: (args: any) => void;
  delKnowledgeBase: (args: any) => void;
  onCollapseChange: (args: string | string[], page?: number) => void;
  reChunkDoc: (args: DocInfo, datasetId: string, page: number) => void;
  delDocItem: (args: DocInfo, datasetId: string, page: number) => void;
  stopChunk: (args: DocInfo, datasetId: string, page: number) => void;
  knowledgeBaseList: KnowledgeBase[];
  docList: DocInfo[];
  tenant?: string;
  docTotal?: number;
}

export default function CodebaseTab({
  hasWorkspaceFolder,
  codebaseIndexingProgress,
  codebaseIndexingStatus,
  codebaseIndexingButtonDisabled,
  onCodebaseIndexing,
  isRemoteEnvironment,
  openLocalPopup,
  openUrlPopup,
  editKnowledgeBase,
  delKnowledgeBase,
  onCollapseChange,
  knowledgeBaseList,
  isIDE,
  docList,
  tenant,
  docTotal,
  reChunkDoc,
  delDocItem,
  stopChunk,
}: CodebaseTabProps) {
  const [memoryContent, setMemoryContent] = useState('');
  const [editState, setEditState] = useState('');
  // 防抖定时
  const debounceTimer = React.useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    getMemoryContent();
  }, []);

  const getMemoryContent = () => {
    vscode.postMessage({
      type: 'COMMON',
      payload: {
        type: 'get-memory-content',
        data: {},
      },
    });
  };

  const handleOpenMemoryMarkdown = () => {
    vscode.postMessage({
      type: 'COMMON',
      payload: {
        type: 'open-memory-markdown',
        data: {},
      },
    });
  };

  const handleEditMemory = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setMemoryContent(value);
    setEditState('已编辑');

    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    debounceTimer.current = setTimeout(() => {
      vscode.postMessage({
        type: 'COMMON',
        payload: {
          type: 'set-memory-content',
          data: value,
        },
      });
      setEditState('已保存');
    }, 1200);
  };

  // 接收md数据
  useHandleMessage((payload: any) => {
    if (payload.type === 'memory-markdown-response') {
      const { data } = payload;
      setMemoryContent(data || '');
      setEditState('');
    }
  });

  const ragEditListData = (knowledgeBase: KnowledgeBase) => {
    return [
      {
        key: 'url',
        label: (
          <span
            onClick={(e) => {
              e.stopPropagation();
              openUrlPopup(knowledgeBase);
            }}
          >
            通过URL添加
          </span>
        ),
      },
      {
        key: 'local',
        label: (
          <span
            onClick={(e) => {
              e.stopPropagation();
              openLocalPopup(knowledgeBase);
            }}
          >
            通过本地文件添加
          </span>
        ),
      },
    ];
  };

  /**
   * 编辑RAG数据
   * @param params - 编辑参数
   */
  const editRagData = (event: React.MouseEvent<HTMLSpanElement, MouseEvent>, params: any) => {
    event.stopPropagation();
    editKnowledgeBase(params);
  };

  // 删除知识库
  const delRagData = (event: React.MouseEvent<HTMLSpanElement, MouseEvent>, params: any) => {
    event.stopPropagation();
    delKnowledgeBase(params);
  };

  const actionListData = (params: any) => {
    return [
      {
        key: 'edit',
        label: <span onClick={(e) => editRagData(e, params)}>编辑</span>,
      },
      {
        key: 'del',
        label: <span onClick={(e) => delRagData(e, params)}>删除</span>,
      },
    ];
  };

  const handleCollapseChange = (key: string | string[]) => {
    onCollapseChange(key);
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date?.toLocaleString('zh-CN', {
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  return (
    <div className="setting-tabs-item">
      <div className="setting-tabs-item-title">上下文</div>
      <div className="joycoder-setting-box" style={{ paddingBottom: '24px' }}>
        <div className="joycoder-setting-box-title">代码库索引</div>
        <div className="joycoder-setting-box-text l-20 mt-10">
          构建代码库索引，可以增强智能体对代码库的理解，提升生成效果。构建索引成功后，智能体将自主获取代码库索引；必要时您也可以通过
          @Codebase 的方式要求智能体使用它。
        </div>
        {hasWorkspaceFolder && (
          <>
            {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXING && (
              <div className="joycoder-loading-dots mt-16">索引中({codebaseIndexingProgress}%)</div>
            )}
            {codebaseIndexingStatus === CodebaseIndexingStatus.UNINDEXED && (
              <div className="joycoder-setting-box-msg mt-16">
                <i className="icon iconfont icon-tanhao mr-4"></i>
                代码库索引未构建
              </div>
            )}
            {codebaseIndexingStatus === CodebaseIndexingStatus.PREPARING && (
              <div className="joycoder-loading-dots mt-16">Codebase服务准备中</div>
            )}
            {codebaseIndexingStatus === CodebaseIndexingStatus.NO_WORKSPACE && (
              <div className="joycoder-setting-box-msg mt-16">
                <i className="icon iconfont icon-tanhao mr-4"></i>
                未检测到工作目录，请打开文件夹后重试
              </div>
            )}
            {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED && <div className="mt-16">索引完成</div>}
            {(codebaseIndexingStatus === CodebaseIndexingStatus.INDEXING ||
              codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED) && (
              <Progress
                percent={codebaseIndexingProgress}
                type="line"
                status={
                  codebaseIndexingProgress === 100 && codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED
                    ? 'success'
                    : 'active'
                }
                showInfo={false}
              />
            )}

            {codebaseIndexingStatus === CodebaseIndexingStatus.UNINDEXED && (
              <Button
                type="default"
                style={{
                  backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                  borderColor: 'var(--vscode-input-border, #72747c)',
                  color: 'var(--vscode-button-secondaryForeground, #72747c)',
                  width: '72px',
                  height: '28px',
                  marginTop: '12px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  padding: '0',
                  marginRight: '12px',
                }}
                disabled={codebaseIndexingButtonDisabled}
                onClick={onCodebaseIndexing('start')}
              >
                开始索引
              </Button>
            )}
            {(codebaseIndexingStatus === CodebaseIndexingStatus.INDEXING ||
              codebaseIndexingStatus === CodebaseIndexingStatus.PREPARING) && (
              <Button
                type="default"
                style={{
                  backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                  borderColor: 'var(--vscode-input-border, #72747c)',
                  color: 'var(--vscode-button-secondaryForeground, #72747c)',
                  width: '72px',
                  height: '28px',
                  marginTop: '12px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  padding: '0',
                  marginRight: '12px',
                }}
                onClick={onCodebaseIndexing('cancel')}
                disabled={codebaseIndexingButtonDisabled}
              >
                取消索引
              </Button>
            )}

            {/* 重新索引 */}
            {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED && (
              <Button
                type="default"
                onClick={onCodebaseIndexing('start')}
                style={{
                  backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                  borderColor: 'var(--vscode-input-border, #72747c)',
                  color: 'var(--vscode-button-secondaryForeground, #72747c)',
                  width: '72px',
                  height: '28px',
                  marginTop: '12px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  padding: '0',
                  marginRight: '12px',
                }}
                disabled={codebaseIndexingButtonDisabled}
              >
                重新索引
              </Button>
            )}

            {/* 删除索引 */}
            {codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED && (
              <Popconfirm
                title="确定清除索引吗？"
                onConfirm={onCodebaseIndexing('remove')}
                okText="删除"
                cancelText="取消"
              >
                <Button
                  type="default"
                  style={{
                    backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                    borderColor: 'var(--vscode-input-border, #72747c)',
                    color: 'var(--vscode-button-secondaryForeground, #72747c)',
                    width: '72px',
                    height: '28px',
                    marginTop: '12px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    padding: '0',
                  }}
                >
                  删除索引
                </Button>
              </Popconfirm>
            )}
          </>
        )}
        {!hasWorkspaceFolder && (
          <div className="joycoder-setting-box-msg mt-16">
            <i className="icon iconfont icon-tanhao mr-4"></i>
            未检测到工作目录，请打开文件夹后重试
          </div>
        )}
      </div>
      {isIDE && tenant === 'JD' && (
        <div className="joycoder-setting-box mt-20 mt-10">
          <div className="joycoder-setting-box-title joycoder-rag-title">
            <span>
              知识库
              <Tag color="#faad14" style={{ fontSize: 12, color: '#fff', transform: 'scale(0.6)' }}>
                beta
              </Tag>
            </span>
            <Button
              type="default"
              onClick={editKnowledgeBase}
              style={{
                backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                borderColor: 'var(--vscode-input-border, #72747c)',
                color: 'var(--vscode-button-secondaryForeground, #72747c)',
                borderRadius: '4px',
                fontSize: '12px',
                padding: '6px 12px',
              }}
            >
              添加知识库
            </Button>
          </div>
          <div className="joycoder-setting-box-text l-20 mt-10">
            通过URL、本地上传等方式添加知识库，构建成功后可增强智能体的知识理解能力。您可以通过 @docs
            主动调用知识库内容，智能体也会在需要时自主获取相关信息。
          </div>
          <div className="joycoder-setting-box-text mt-10">
            <div className="joycoder-rag-list">
              <Collapse accordion onChange={handleCollapseChange}>
                {knowledgeBaseList?.map((knowledgeBase: KnowledgeBase) => {
                  return (
                    <Collapse.Panel
                      header={
                        <div className="joycoder-rag-list-item">
                          <div className="joycoder-rag-list-item-name">
                            <div className="item-title">{knowledgeBase.name}</div>
                            <div>更新于{formatDate(knowledgeBase.update_time)}</div>
                          </div>
                          <div className="joycoder-rag-list-item-action">
                            <Dropdown placement="bottom" menu={{ items: ragEditListData(knowledgeBase) }}>
                              <i className="icon iconfont icon-shangchuan"></i>
                            </Dropdown>
                            {/* <i className="icon iconfont icon-chakanshili" onClick={() => openDocList(knowledgeBase)}></i> */}
                            <Dropdown placement="bottom" menu={{ items: actionListData(knowledgeBase) }}>
                              <i className="icon iconfont icon-gengduo"></i>
                            </Dropdown>
                          </div>
                        </div>
                      }
                      key={knowledgeBase.id}
                    >
                      <DocList
                        docList={docList}
                        datasetId={knowledgeBase.id}
                        total={docTotal ?? 0}
                        onCollapseChange={onCollapseChange}
                        reChunkDoc={reChunkDoc}
                        delDocItem={delDocItem}
                        stopChunk={stopChunk}
                      />
                    </Collapse.Panel>
                  );
                })}
              </Collapse>
            </div>
            {knowledgeBaseList?.length === 0 && (
              <div className="joycoder-rag-no-data">
                <div>
                  <i className="icon iconfont icon-shangxiawen"></i>
                  <div className="no-data-text">暂无知识库文档</div>
                  <div>
                    点击 <span className="link-text">添加知识库</span> 进行添加
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      {!isRemoteEnvironment && (
        <div>
          <div className="joycoder-setting-box mb-18 mt-20">
            <div className="joycoder-setting-box-title">记忆</div>
            <div className="joycoder-setting-box-text l-20" style={{ margin: '10px 0' }}>
              对话中被记住的内容会存储在此处，您也可以直接在下方直接编辑记忆内容，更改会自动存储并应用。
            </div>
            <Button
              type="primary"
              icon={<ExportOutlined />}
              onClick={handleOpenMemoryMarkdown}
              style={{
                backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
                borderColor: 'var(--vscode-input-border, #72747c)',
                minWidth: '32px',
                height: '24px',
                borderRadius: '4px',
                color: 'var(--vscode-button-secondaryForeground, #72747c)',
                fontSize: '12px',
                float: 'left',
                margin: '0 auto 4px 0',
                padding: '4px 6px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              打开 memory.md
            </Button>

            {editState === '已编辑' ? (
              <EditOutlined
                style={{
                  marginLeft: '10px',
                  marginRight: '2px',
                  verticalAlign: 'middle',
                }}
              />
            ) : editState === '已保存' ? (
              <CheckCircleOutlined
                style={{
                  marginLeft: '10px',
                  marginRight: '2px',
                  verticalAlign: 'middle',
                }}
              />
            ) : null}

            <span
              style={{
                padding: '2px 8px 2px 0',
                background: 'transparent',
                color: 'var(--vscode-descriptionForeground, #72747c)',
                fontSize: 12,
                verticalAlign: 'middle',
                display: 'inline-block',
                height: '20px',
                lineHeight: '16px',
              }}
            >
              {editState}
            </span>
            <textarea
              placeholder="请输入你的记忆..."
              value={memoryContent}
              onChange={handleEditMemory}
              style={{
                display: 'block',
                width: '100%',
                height: '84px',
                background: 'transparent',
                color: 'var(--vscode-input-foreground, #72747C)',
                border: '1px solid var(--vscode-input-border, #72747C)',
                borderRadius: '4px',
                padding: '4px 8px',
                fontSize: 12,
                resize: 'vertical',
                boxSizing: 'border-box',
                outline: 'none',
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}
