import React, { useEffect, useState } from 'react';
import { Space, Tag } from 'antd';
import Table, { ColumnsType, TablePaginationConfig } from 'antd/lib/table';
import { FilterValue } from 'antd/lib/table/interface';

interface DocProps {
  docList: DocInfo[];
  total: number;
  onCollapseChange: (args: string | string[], page?: number) => void;
  reChunkDoc: (args: DocInfo, datasetId: string, page: number) => void;
  delDocItem: (args: DocInfo, datasetId: string, page: number) => void;
  stopChunk: (args: DocInfo, datasetId: string, page: number) => void;
  datasetId: string;
}
interface TableParams {
  pagination?: TablePaginationConfig;
  sortField?: string;
  sortOrder?: string;
  filters?: Record<string, FilterValue | null>;
}

interface DocInfo {
  name: string;
  id: string;
  dataset_id?: string;
  knowledgebase_id?: string;
  permission?: string;
  update_date?: string;
  tenant_id?: string;
  status?: string;
  description?: string;
  update_time: number;
  create_time?: number;
  token_num?: number;
  source_type?: string;
  progress?: number;
  progress_msg?: string;
  type?: string;
  run?: string;
}

export default function DocListPopup({
  docList,
  total,
  onCollapseChange,
  datasetId,
  reChunkDoc,
  delDocItem,
  stopChunk,
}: DocProps) {
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date?.toLocaleString('zh-CN', {
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };
  const [docs, setDocs] = useState<DocInfo[]>(docList);
  const [tableParams, setTableParams] = useState<TableParams>({
    pagination: {
      current: 1,
      pageSize: 20,
      total,
    },
  });
  useEffect(() => {
    setTableParams({
      pagination: {
        current: 1,
        pageSize: 20,
        total,
      },
    });
  }, [total]);
  useEffect(() => {
    setDocs(docList);
  }, [docList]);
  const reChunks = (record: DocInfo) => {
    reChunkDoc(record, datasetId, tableParams.pagination?.current ?? 1);
  };
  const stopChunks = (record: DocInfo) => {
    stopChunk(record, datasetId, tableParams.pagination?.current ?? 1);
  };
  const delDoc = (record: DocInfo) => {
    delDocItem(record, datasetId, tableParams.pagination?.current ?? 1);
  };
  const columns: ColumnsType<DocInfo> = [
    {
      title: '文档名称',
      dataIndex: 'name',
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
      // sorter: {
      //   compare: (a, b) => a.update_time - b.update_time,
      // },
      render: (_, record) => {
        return formatDate(record.update_time);
      },
    },
    {
      title: '解析状态',
      dataIndex: 'status',
      render: (_, record) => {
        if (record.run === 'CANCEL') {
          return <Tag color="warning">取消</Tag>;
        } else if (record.run === 'FAIL') {
          return <Tag color="error">失败</Tag>;
        } else if (record.progress && +record.progress < 1) {
          return <Tag color="blue">解析中 {(+record.progress * 100).toFixed(2)}% </Tag>;
        } else if (record.progress && +record.progress === 1) {
          return <Tag color="success">成功</Tag>;
        }
        return <Tag color="default">未解析</Tag>;
      },
    },
    {
      title: '启用状态',
      dataIndex: 'run',
      render: (_, record) => {
        const run_mapping: Record<string | number, string> = {
          UNSTART: '未启动',
          RUNNING: '启动中',
          CANCEL: '已取消',
          DONE: '已启动',
          FAIL: '启动失败',
        };
        return (
          record.run && (
            <Tag color={record.run === 'FAIL' ? 'error' : record.run === 'DONE' ? 'success' : 'default'}>
              {run_mapping[record.run]}
            </Tag>
          )
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          {record.run !== 'RUNNING' && (
            <>
              <a className="joycode-doc-list-rechunks" onClick={() => reChunks(record)}>
                {record.progress == 0 ? (
                  <i className="icon iconfont icon-kaishi"></i>
                ) : (
                  <i className="icon iconfont icon-shuaxin"></i>
                )}
              </a>
              <a className="joycode-doc-list-del" onClick={() => delDoc(record)}>
                <i className="icon iconfont icon-shanchu"></i>
              </a>
            </>
          )}
          {record.run === 'RUNNING' && (
            <a className="joycode-doc-list-rechunks" onClick={() => stopChunks(record)}>
              <i className="icon iconfont icon-zanting"></i>
            </a>
          )}
        </Space>
      ),
    },
  ];
  const handleTableChange = (pagination: TablePaginationConfig) => {
    onCollapseChange(datasetId, pagination.current);
    setTableParams({
      pagination,
    });
  };
  return (
    <Table
      rowKey={(record) => record?.id}
      columns={columns}
      dataSource={docs}
      pagination={tableParams.pagination}
      onChange={handleTableChange}
      className="joycode-doc-list"
    />
  );
}
