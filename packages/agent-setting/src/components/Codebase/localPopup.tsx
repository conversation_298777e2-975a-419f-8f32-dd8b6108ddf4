import React, { useState } from 'react';
import { Button, Form, message, Modal, Upload } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import type { FormInstance } from 'antd/es/form';
import { RcFile } from 'antd/lib/upload';

interface LocalPopupProps {
  open: boolean;
  title?: string;
  baseUrl?: string;
  datasetId?: string;
  ptKey?: string;
  setLocalPopup: (open: boolean) => void;
  getNormalUrlData: (args: any) => any;
}

export default function LoaclPopup({
  open = false,
  title,
  setLocalPopup,
  getNormalUrlData,
  datasetId,
}: LocalPopupProps) {
  const formRef = React.createRef<FormInstance>();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);

  const handleOk = async () => {
    // Handle OK action
    try {
      if (uploading) return message.success('文档保存中，请稍后~');
      // 将文件转换为 Base64 格式
      const fileToBase64 = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = (error) => reject(error);
        });
      };

      // 将文件转换为文本内容（适用于 .md 和 .txt 文件）
      const fileToText = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.readAsText(file, 'UTF-8');
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = (error) => reject(error);
        });
      };
      const filesData = await Promise.all(
        fileList.map(async (file) => {
          const rcFile = file as RcFile;
          try {
            // 对于文本文件，我们可以直接读取文本内容，更高效
            const content = await fileToText(rcFile);
            const base64 = await fileToBase64(rcFile);
            return {
              fileName: rcFile.name,
              size: rcFile.size,
              type: rcFile.type,
              lastModified: rcFile.lastModified,
              fileContent: content, // 文本内容
              base64: base64, // Base64 编码（如果需要的话）
            };
          } catch (error) {
            console.error('文件读取失败:', error);
            return {};
          }
        })
      );
      setUploading(true);

      const res = await getNormalUrlData({ filesData, type: 'local', datasetId });
      setUploading(false);
      if (res?.code === 0) {
        setFileList([]);
        message.success('文档添加成功！');
        setLocalPopup(false);
        formRef.current?.resetFields();
        return;
      }
      message.error(res?.msg || '文档添加失败！');
    } catch (error) {
      console.error('%c [ error ]-29', 'font-size:13px; background:pink; color:#bf2c9f;', error);
      setUploading(false);
    }
  };
  const handleCancel = () => {
    // Handle Cancel action
    setLocalPopup(false);
    setUploading(false);
    formRef.current?.resetFields();
  };
  const layout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 19 },
  };

  const props: UploadProps = {
    name: 'file', //发到后台的文件参数名
    multiple: true,
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    fileList,
    headers: {
      authorization: 'authorization-text',
    },
    beforeUpload: (file) => {
      const isMdOrTxt =
        file.type === 'text/markdown' ||
        file.type === 'text/plain' ||
        file.name.endsWith('.md') ||
        file.name.endsWith('.txt');
      if (!isMdOrTxt) {
        // 只能上传 MD 或 TXT 格式的文件!
        message.error(`只能上传 MD 或 TXT 格式的文件!`);
        return false; // 阻止上传
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        // console.error('文件必须小于 10MB!');
        message.error(`文件必须小于 10MB!`);
        return false;
      }
      setFileList([...fileList, file]);

      return false;
    },
  };

  return (
    <Modal
      title={title || '编辑文档'}
      open={open}
      okText={uploading ? '保存中···' : '确定'}
      cancelText="取消"
      onOk={handleOk}
      onCancel={handleCancel}
      // okButtonProps={{ disabled: okButtonDisabled }}
      // cancelButtonProps={{ disabled: cancelButtonDisabled }}
    >
      <Form {...layout} ref={formRef} name="control-ref">
        {/* <Form.Item
          name="knowledgeName"
          validateTrigger={['onBlur']}
          label="文档名称"
          rules={[{ required: true, message: '请输入文档名称' }]}
        >
          <Input placeholder="请输入文档名称" allowClear />
        </Form.Item> */}
        <Form.Item
          name="files"
          label="本地文件"
          rules={[
            {
              required: true,
              message: '请上传MD或者TXT格式的文件',
              validator: (_, value) => {
                // 检查文件格式
                const invalidFiles = value.filter((file: { type: string; name: string; size: number }) => {
                  const isMdOrTxt =
                    file.type === 'text/markdown' ||
                    file.type === 'text/plain' ||
                    file.name.endsWith('.md') ||
                    file.name.endsWith('.txt');
                  const isLt10M = file.size / 1024 / 1024 < 10;
                  return !isMdOrTxt || !isLt10M;
                });
                if (invalidFiles.length > 0) {
                  return Promise.reject(new Error('支持MD或TXT格式的文件,并且单个文件必须小于 10MB'));
                }
                return Promise.resolve();
              },
            },
          ]}
          valuePropName="fileList"
          className="joycode-popup-local-box"
        >
          <div className="joycoder-setting-box-text l-20 mb-18">
            支持.md/.txt 两种格式，单个文件最大 10 MB，文档集最大50MB，最多添加 100 个文件
          </div>
          <Upload {...props}>
            <Button icon={<UploadOutlined />}>添加文件</Button>
          </Upload>
        </Form.Item>
      </Form>
    </Modal>
  );
}
