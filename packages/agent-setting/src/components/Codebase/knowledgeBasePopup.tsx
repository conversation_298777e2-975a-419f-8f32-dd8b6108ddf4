import React, { useEffect, useState, useRef } from 'react';
import { Modal, Form, Input } from 'antd';
import layout from 'antd/lib/layout';
import { FormInstance } from 'antd/es/form';
import { CommonMessage } from '../../messages/messageTypes';

interface URLPopupProps {
  open: boolean;
  title?: string;
  setBasePopup: (open: boolean) => void;
  knowledgeBaseInfo?: any;
}

export default function BasePopup({ knowledgeBaseInfo, open = false, title, setBasePopup }: URLPopupProps) {
  const formRef = useRef<FormInstance>(null);

  // 每次弹窗打开时或知识库信息变化时更新表单值
  useEffect(() => {
    if (open && formRef.current) {
      // 先重置表单，确保清除旧值
      formRef.current.resetFields();
      // 如果有知识库信息，则设置表单值
      if (knowledgeBaseInfo) {
        // 使用 setTimeout 确保在下一个事件循环中设置值，避免竞态条件
        setTimeout(() => {
          formRef.current?.setFieldsValue(knowledgeBaseInfo);
        }, 0);
      }
    }
  }, [open, knowledgeBaseInfo]); // 同时依赖 open 和 knowledgeBaseInfo，确保数据变化时也会更新

  const handleOk = async () => {
    // Handle OK action
    try {
      const values = await formRef.current?.validateFields();
      vscode.postMessage<CommonMessage>({
        type: 'COMMON',
        payload: {
          type: 'getKnowledgeBaseInfo',
          dataType: knowledgeBaseInfo?.dataset_id || knowledgeBaseInfo?.id ? 'update' : 'add',
          data: values,
        },
      });
      formRef.current?.resetFields();
    } catch (error) {
      console.error('%c [ error ]-29', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  };
  const handleCancel = () => {
    // Handle Cancel action
    setBasePopup(false);
    formRef.current?.resetFields();
  };
  return (
    <Modal
      title={title || '编辑知识库'}
      open={open}
      okText="确定"
      cancelText="取消"
      onOk={handleOk}
      onCancel={handleCancel}
    >
      <Form {...layout} ref={formRef} name="control-ref">
        <Form.Item
          name="name"
          validateTrigger={['onBlur']}
          label="知识库名称"
          rules={[{ required: true, message: '请输入知识库名称' }]}
        >
          <Input placeholder="请输入知识库名称" allowClear />
        </Form.Item>
        {knowledgeBaseInfo?.dataset_id ||
          (knowledgeBaseInfo?.id && (
            <>
              <Form.Item name="dataset_id" hidden>
                <Input />
              </Form.Item>
              <Form.Item name="id" hidden>
                <Input />
              </Form.Item>
            </>
          ))}
      </Form>
    </Modal>
  );
}
