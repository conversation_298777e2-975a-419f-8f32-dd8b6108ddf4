// @desperated 该类型定义与packages/agent-driven/web-agent/src/utils/modes.ts重复，不再使用
// export interface AgentMode {
//   agentId: string;
//   name: string;
//   agentDefinition: string;
//   whenToUse?: string;
//   customInstructions?: string;
//   groups: string[];
//   source: 'global' | 'project';
//   headImg?: string;
//   isActive: boolean;
// }
export interface ModesInfo{
    customModes: ModeConfig[];
    defaultModes: ModeConfig[];
}
