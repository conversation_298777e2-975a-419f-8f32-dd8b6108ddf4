import { <PERSON>lapse, Col, Row, Switch, Select, Input, Tooltip, <PERSON>con<PERSON>rm, Divide<PERSON>, But<PERSON> } from 'antd';
import { CheckCircleOutlined, DeleteOutlined, QuestionCircleOutlined, PlusOutlined } from '@ant-design/icons';
import React, { useState, useEffect, useRef, ReactNode } from 'react';
import type { UploadFile } from 'antd/es/upload/interface';
import { AgentToolType, AgentSceneType } from '@joycoder/plugin-base-ai/src/langchain/tools/shared/toolType';
import {
  deleteUserVetorKnowledgeDocs,
  getVetorKnowledgeDocs,
  getVetorKnowledgeIds,
  KnowledgeDocType,
  KnowledgeType,
} from '@joycoder/plugin-base-ai/src/knowledge/common';
import { isDebug } from '@joycoder/shared/src/debug';
import Upload from '../Upload';
import './index.scss';
import to from 'await-to-js';
import { useStateRef } from '../../../../../hooks/useStateRef';
import { useHandleMessage } from '../../../../../hooks/useHandleMessage';

const { Panel } = Collapse;

interface IProps {
  onToolBoxChange: (ToolItem: ToolItem | null) => void;
  onCollapseChange: (key: string | string[]) => void;
  disabled: boolean;
}

export interface ToolItem {
  scene: AgentSceneType;
  beta?: boolean;
  icon: string;
  name: string;
  tip: string;
  fileType?: 'knowledge' | 'image' | 'image_remote';
  fileAcceptType: string[];
  systemMessage: string;
  customPrompt: string;
  customPlaceHolder?: string;
  model?: string;
  requireModelFeatures: string[];
  needUploadFile?: boolean;
  onlineSearch?: boolean; //联网搜索，内容可能会发送到搜索引擎
  fileInfo?: UploadFile | null;
  autoReset?: boolean;
  agentTools?: Array<string | [string, Record<string, any>]>;
  sessionId?: string;
  extraView?: (props: Pic2codeExtraProps) => JSX.Element;
}

interface Pic2codeExtraProps {
  toolBoxInfo: ToolItem;
  onUpdateBoxInfo: (ToolItem: ToolItem | null) => void;
  disabled: boolean;
}

// icon来源：https://icon-icons.com/
const ToolList: ToolItem[] = [
  {
    scene: AgentSceneType.CustomHelper,
    icon: 'https://img20.360buyimg.com/img/jfs/t1/220801/1/37602/8728/65e95cfbFde821a03/10aa2b7f3d104105.png',
    name: '个性助手',
    tip: '特定场景下的专家助手',
    beta: false,
    fileAcceptType: [],
    systemMessage: ``,
    customPrompt: isDebug() ? '匹配字符串中的数字' : '',
    customPlaceHolder: '输入您希望助手帮您做的事情，如正则助手下可输入：匹配字符串中的数字',
    model: 'GPT-4-Turbo',
    requireModelFeatures: [],
    needUploadFile: false,
    fileInfo: null,
    autoReset: false,
    onlineSearch: true,

    agentTools: [],
    extraView: (props: Pic2codeExtraProps) => {
      const customHelperList = window.joyCoderVscodeConfig.customHelper;
      const selectedType = useRef(localStorage.getItem('joycoder-customhelper') || customHelperList[0].name);
      const getHelperByName = (name = selectedType.current) => {
        return customHelperList.filter((item: any) => item.name == name)[0] || customHelperList[0];
      };
      const [selectedInfo, setSelectedInfo] = useState(getHelperByName());

      useEffect(() => {
        updateInfo(selectedInfo);
      }, []);

      const updateInfo = (value: any) => {
        if (!value) return;
        const info = {
          ...props.toolBoxInfo,
          systemMessage: value.profile,
        };
        props.onUpdateBoxInfo(info);
      };

      const onSelectChange = (value: any) => {
        const info = getHelperByName(value);
        setSelectedInfo(info);
        updateInfo(info);
        localStorage.setItem('joycoder-customhelper', value);
      };

      return (
        <div className="search2llm-extra">
          <div className="search2llm-extra-title" style={{ opacity: props.disabled ? 0.6 : 1 }}>
            选择助手:
          </div>
          <Row wrap={true} align={'middle'}>
            <Col flex="120px">
              <Select
                disabled={props.disabled}
                defaultValue={selectedType.current}
                size="small"
                style={{ width: 120 }}
                onChange={onSelectChange}
                popupClassName="search2llm-extra-select"
                options={customHelperList.map((item: any) => {
                  return {
                    label: item.name,
                    value: item.name,
                  };
                })}
                dropdownRender={(menu) => (
                  <>
                    {menu}
                    <Divider style={{ margin: '8px auto' }} />
                    <div
                      className="customhelper-addbtn"
                      onClick={() => {
                        vscode.postMessage({
                          type: 'COMMON',
                          payload: {
                            type: 'chatgpt-set-customhelper',
                          },
                        });
                      }}
                    >
                      <PlusOutlined />
                      &nbsp; 新增助手
                    </div>
                  </>
                )}
              />
            </Col>
            <Col flex="2px"></Col>
            <Col flex="auto">
              <Input
                addonAfter={
                  <Tooltip title="当前助手的描述">
                    <QuestionCircleOutlined />
                  </Tooltip>
                }
                disabled
                value={selectedInfo.profile}
              />
            </Col>
          </Row>
        </div>
      );
    },
  },
  {
    scene: AgentSceneType.Pic2code,
    icon: 'https://img12.360buyimg.com/img/jfs/t1/190177/18/41214/2190/6555d618Ff1683289/1091999a58eb8330.png',
    name: '图转代码',
    tip: '输入UI草图后，根据指定的UI框架输出相应代码，适用于一些简单的CMS页面',
    fileAcceptType: ['.png', '.jpg', '.jpeg'],
    fileType: 'image_remote',
    systemMessage: `
你是一位专业的前端工程师，能够熟练使用HTML、JS、CSS以及Ant Design、Tailwind、Element等UI框架。
你的任务是接收UI草图或网页截图并将其准确转换为功能性强、响应式良好、结构清晰的前端代码。
你能理解各种设计元素，并遵循前端开发的最佳实践，以确保生成的代码清晰、可维护。

在转换过程中，请注意以下几点：
1、请编写完整的代码，确保应用程序的外观与输入的图片完全一致，不要有任何遗漏；
2、特别关注背景颜色、文本颜色、字体大小、字体系列、内边距、外边距、边框等，确保颜色和尺寸完全匹配；
3、使用截图中的确切文本；
4、不要在代码中添加注释，比如在代码中写"<!-- ... -->"、"// ..."等；
5、如有需要，请重复元素以匹配截图。例如，如果有 15 个项目，代码应该有 15 个项目。不要留下 "<!-- 为每个新闻项目重复 -->" 这样的注释，否则可能会出现问题；
6、对于图像，请使用来自 https://placehold.co 的占位图，并在 alt 文本中包含图像的详细描述，以便图像生成 AI 以后能够生成图像；
7、仅专注于UI草图转代码的任务，不需提供其他类型的服务；
8、如用户未指定特定UI框架，则默认采用Ant Design进行开发；
9、生成的代码尽可能集中在同一个代码块中，方便用户复制。

只需返回完整代码块，不要在开头或结尾输出代码之外的其他文字。`,
    customPrompt: '将此UI草图还原为前端代码，UI框架使用 Ant Design，输出 TSX 文件。',
    model: 'GPT-4-OMNI',
    requireModelFeatures: ['vision'],
    needUploadFile: true,
    fileInfo: null,
    autoReset: true,
    extraView: (props: Pic2codeExtraProps) => {
      const customPrompts: any = {
        antdesign: '将此UI草图还原为前端代码，UI框架使用 Ant Design，输出 TSX 文件。',
        tailwind: '将此UI草图还原为前端代码，UI框架使用 Tailwind，输出 HTML 文件。',
        element: '将此UI草图还原为前端代码，UI框架使用 Element，输出 VUE 文件。',
      };

      const onChange = (value: string) => {
        const customPrompt = customPrompts[value];
        const info = {
          ...props.toolBoxInfo,
          customPrompt,
        };
        props.onUpdateBoxInfo(info);
      };

      return (
        <div className="pic2code-extra">
          <div className="pic2code-extra-title" style={{ opacity: props.disabled ? 0.6 : 1 }}>
            UI框架:
          </div>
          <Select
            disabled={props.disabled}
            defaultValue="antdesign"
            size="small"
            style={{ width: 100 }}
            onChange={onChange}
            popupClassName="pic2code-extra-select"
            options={[
              {
                value: 'antdesign',
                label: 'Ant Design',
              },
              {
                value: 'tailwind',
                label: 'Tailwind',
              },
              {
                value: 'element',
                label: 'Element',
              },
            ]}
          />
        </div>
      );
    },
  },
  {
    scene: AgentSceneType.D2C,
    icon: 'https://img10.360buyimg.com/img/jfs/t1/185870/31/46660/4119/6655a9afFf975e626/7692c85a867bb199.png',
    name: 'D2C',
    tip: '结合Relay等D2C工具，使用AI优化其产物，如语义化、组件化等',
    fileAcceptType: [],
    beta: false,
    systemMessage: `你是一个擅长代码优化的智能助手，精通HTML、JavaScript、CSS以及React、Vue、Taro等框架。你的任务是接收D2C工具转换的代码，并根据用户要求进行优化。如果发现输入中有违反编码规范、逻辑错误或可读性差的问题，需要进行纠正。请多次检查自己生成的内容，确保尽可能准确地回答问题。`,
    customPrompt: '',
    agentTools: [AgentToolType.D2C],
    model: 'GPT-4-Turbo',
    needUploadFile: false,
    fileInfo: null,
    autoReset: false,
    requireModelFeatures: ['agent'],
    extraView: (props: Pic2codeExtraProps) => {
      const [d2cType, setD2cType] = useState('relay');
      const [promptType, setPromptType] = useState('semantization');

      const modularizationSystemMessage = `
用户要求你组件化代码时，我希望你根据以下方式进行更新:
1. 拆分组件分析：依据代码中的 DOM 结构进行分析，确认可以拆分成哪些组件（必须确保分割后的组件组合后能够完全覆盖到原有的DOM节点）。
2. 组件化拆分：将提供的代码进行组件化分割，具体来说按照分析对代码针对DOM结构进行分割，目标是将长代码组件化后提高代码的可读性（必须确保分割后的组件组合后能够完全覆盖到原有的DOM节点）。

请注意：
- 将DOM节点进行组件化分割后，所有组件组合后应该可以100%覆盖到原有的 DOM 结构。
- 组件化后可归为容器组件和展示组件，而展示组件的Dom节点数应优先控制在10到50个节点之间（极端情况可在2到60个节点），不应该过多或过少，这个很重要。
- 组件化后，不应该存在只有一个DOM节点的组件，即组件内包含的DOM节点应该大于一个，这个很重要。
- 组件化后，组件的名称应该具有语义化，即具有明确含义，如：不要使用类似 \`cnt\` 这样的名称。
- 分割后的所有组件组合后能够完全覆盖到原有的DOM节点。
- 在建立父子组件关系时，请确保子组件的DOM节点包含在父组件的DOM节点内。
- 组件化后，组件的层级结构应该与DOM结构保持一致。
- 组件化后，组件的样式应该与DOM结构保持一致。
- 请一个组件一个组件的输出，确保每个组件都是完整且合理的，组件间不存在耦合。
- 拆分组件时对应的CSS/SCSS/LESS代码也要拆分，每个组件都对应一个CSS/SCSS/LESS文件，组件共用的CSS可以独立为一个CSS，并在页面中引用它。`;
      const confInfo = {
        defaultValue: 'semantization',
        prompts: {
          semantization: {
            appendSystemMessage: '',
            customPrompt: `请优化代码中的类名，使其更具语义化。`,
          },
          modularization: {
            appendSystemMessage: modularizationSystemMessage,
            customPrompt: `请将代码合理拆分为多个组件，使其更易于维护。`,
          },
          semantizationAndmodularization: {
            appendSystemMessage: modularizationSystemMessage,
            customPrompt: `请优化代码中的类名，使其更具语义化，并将代码合理拆分为多个组件，使其更易于维护。`,
          },
        },
        options: [
          {
            value: 'semantization',
            label: '语义化',
          },
          {
            value: 'modularization',
            label: '组件化',
          },
          {
            value: 'semantizationAndmodularization',
            label: '语义化+组件化',
          },
        ],
      };
      const customPromptsInfo: any = {
        relay: confInfo,
        HuaMei: confInfo,
      };

      const handelUpdate = (config: any) => {
        const { customPrompt, appendSystemMessage } = config;
        const info = {
          ...props.toolBoxInfo,
          customPrompt,
        };
        const currentTool = ToolList.filter((item) => item.scene == AgentSceneType.D2C)[0];
        info.systemMessage = currentTool.systemMessage + (appendSystemMessage ? '\n' + appendSystemMessage : '');
        props.onUpdateBoxInfo(info);
      };

      useEffect(() => {
        handelUpdate(customPromptsInfo.relay.prompts.semantization);
      }, []);

      const onD2cTypeChange = (value: string) => {
        const defaultValue = customPromptsInfo[value].defaultValue;

        setD2cType(value);
        setPromptType(defaultValue);

        handelUpdate(customPromptsInfo[value].prompts[defaultValue]);
      };

      const onPromptChange = (value: string) => {
        setPromptType(value);

        handelUpdate(customPromptsInfo[d2cType].prompts[value]);
      };

      return (
        <div className={`pic2code-extra ${d2cType == 'relay' && 'pic2code-extra-noai'}`}>
          <div className="pic2code-extra-title" style={{ opacity: props.disabled ? 0.6 : 1 }}>
            生成方式:
          </div>
          <Row wrap={true} align={'middle'}>
            <Col flex="65px">
              <Select
                disabled={props.disabled}
                value={d2cType}
                size="small"
                style={{ width: 65 }}
                popupClassName="pic2code-extra-select"
                options={[
                  {
                    value: 'relay',
                    label: 'Relay',
                  },
                  {
                    value: 'HuaMei',
                    label: '画眉',
                  },
                ]}
                onChange={onD2cTypeChange}
              />
            </Col>
            <Col flex="2px"></Col>
            <Col flex="95px">
              <Select
                disabled={props.disabled}
                value={promptType}
                size="small"
                style={{ width: 95 }}
                onChange={onPromptChange}
                popupClassName="pic2code-extra-select"
                options={customPromptsInfo[d2cType].options}
              />
            </Col>
            {(d2cType == 'relay' || d2cType == 'HuaMei') && (
              <>
                <Col flex="7px"></Col>
                <Col flex="38px">
                  <Button
                    disabled={props.disabled}
                    size="small"
                    className="pic2code-extra-btn"
                    onClick={() => {
                      vscode.postMessage({
                        type: 'OPEN_IN_BROWSER',
                        payload: d2cType == 'relay' ? 'https://ling.jd.com/files' : 'https://huamei.jd.com/Design',
                      });
                    }}
                  >
                    去生成
                  </Button>
                </Col>
              </>
            )}
          </Row>
        </div>
      );
    },
  },
  {
    scene: AgentSceneType.Search,
    icon: 'https://img10.360buyimg.com/img/jfs/t1/246996/40/2755/14567/659cefddF8d13c412/66ffb83b12640bae.png',
    name: '联网搜索',
    tip: '查询搜索引擎以获取最新信息',
    beta: false,
    fileAcceptType: [],
    systemMessage: `你是一个乐于助人的助手，善于结合各种搜索工具来增强你的知识，并使用中文来回答问题。如果输入内容中存在网页链接并且输出内容中没引用这些链接，则可以在最后增加"相关链接："标题，并在标题下使用MarkDown语法罗列最多5个相关的网页链接以便用户进一步了解详情`,
    customPrompt: isDebug() ? '深圳明天天气怎么样？' : '',
    customPlaceHolder: '示例：TaroJS最新版本是多少？',
    model: 'GPT-4-Turbo',
    requireModelFeatures: ['function_call'],
    needUploadFile: false,
    fileInfo: null,
    autoReset: false,
    agentTools: [localStorage.getItem('joycoder-llmSearchEngine') || AgentToolType.GoogleSearch],
    extraView: (props: Pic2codeExtraProps) => {
      const [selectedEngine, setSelectedEngine] = useState<AgentToolType>(
        (localStorage.getItem('joycoder-llmSearchEngine') as AgentToolType) || AgentToolType.GoogleSearch
      );
      const needKeySearchList = [
        AgentToolType.SerpAPIBaiDu,
        AgentToolType.SerpAPIDuckDuckGo,
        AgentToolType.SerpAPIGoogle,
        AgentToolType.GoogleAPI,
        AgentToolType.TavilyAPI,
      ];
      const [isShowInput, setIsShowInput] = useState(needKeySearchList.includes(selectedEngine));

      const onSelectChange = (value: AgentToolType) => {
        const info = {
          ...props.toolBoxInfo,
          agentTools: [value],
        };
        props.onUpdateBoxInfo(info);
        setIsShowInput(needKeySearchList.includes(value));
        localStorage.setItem('joycoder-llmSearchEngine', value);
        setSelectedEngine(value);
      };

      const onInputChange = (data: any) => {
        vscode.postMessage({
          type: 'COMMON',
          payload: {
            type: 'chatgpt-set-searchapikey',
            data,
          },
        });
        window.joyCoderVscodeConfig.searchApiKey = {
          ...window.joyCoderVscodeConfig.searchApiKey,
          ...data,
        };
      };

      const { serpApiKey, googleApiKey, googleCSEId, tavilyApiKey } = window.joyCoderVscodeConfig.searchApiKey;

      return (
        <div className="search2llm-extra">
          <div className="search2llm-extra-title" style={{ opacity: props.disabled ? 0.6 : 1 }}>
            搜索引擎:
          </div>
          <Row wrap={true} align={'middle'}>
            <Col flex="154px">
              <Select
                disabled={props.disabled}
                defaultValue={selectedEngine}
                size="small"
                style={{ width: 154 }}
                onChange={onSelectChange}
                popupClassName="search2llm-extra-select"
                options={[
                  {
                    value: AgentToolType.GoogleSearch,
                    label: 'Google',
                  },
                  {
                    value: AgentToolType.BaiDuSearch,
                    label: '百度',
                  },
                  // 公司网无法访问，暂时屏蔽入口
                  // {
                  //   value: AgentToolType.DuckDuckGoSearch,
                  //   label: 'DuckDuckGo',
                  // },
                  {
                    value: AgentToolType.GoogleAPI,
                    label: 'Google API(推荐)',
                  },
                  {
                    value: AgentToolType.SerpAPIGoogle,
                    label: 'SerpAPI-Google(推荐)',
                  },
                  {
                    value: AgentToolType.TavilyAPI,
                    label: 'Tavily API(推荐)',
                  },
                  // 选择过多，暂时屏蔽重要性低的搜索方式
                  // {
                  //   value: AgentToolType.SerpAPIBaiDu,
                  //   label: '百度API',
                  // },
                  // {
                  //   value: AgentToolType.SerpAPIDuckDuckGo,
                  //   label: 'DuckDuckGo API',
                  // },
                  {
                    value: AgentToolType.TaroSearch,
                    label: 'Taro Docs',
                  },
                ]}
              />
            </Col>
            <Col flex="2px"></Col>
            <Col flex="auto">
              {/* SerpAPI */}
              {isShowInput &&
                [AgentToolType.SerpAPIBaiDu, AgentToolType.SerpAPIDuckDuckGo, AgentToolType.SerpAPIGoogle].includes(
                  selectedEngine
                ) && (
                  <Input
                    addonAfter={
                      <Tooltip title="点击注册获取API KEY(100次/月)">
                        <QuestionCircleOutlined
                          onClick={() => {
                            vscode.postMessage({
                              type: 'OPEN_IN_BROWSER',
                              payload: 'https://serpapi.com/manage-api-key',
                            });
                          }}
                        />
                      </Tooltip>
                    }
                    placeholder="输入SerpAPI KEY"
                    defaultValue={serpApiKey}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      onInputChange({ serpApiKey: e.target.value });
                    }}
                  />
                )}
              {/* 输入TavilyAPI */}
              {isShowInput && selectedEngine == AgentToolType.TavilyAPI && (
                <Input
                  addonAfter={
                    <Tooltip title="点击注册获取API KEY(1000次/月)">
                      <QuestionCircleOutlined
                        onClick={() => {
                          vscode.postMessage({
                            type: 'OPEN_IN_BROWSER',
                            payload: 'https://app.tavily.com/home',
                          });
                        }}
                      />
                    </Tooltip>
                  }
                  placeholder="输入TavilyAPI KEY"
                  defaultValue={tavilyApiKey}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    onInputChange({ tavilyApiKey: e.target.value });
                  }}
                />
              )}
            </Col>
          </Row>
          {/* googleAPI */}
          {isShowInput && selectedEngine == AgentToolType.GoogleAPI && (
            <Row wrap={false} align={'middle'} gutter={4} style={{ marginTop: '6px' }}>
              <Col span={12}>
                <Input
                  addonAfter={
                    <Tooltip title="点击注册获取API KEY(100次/月)">
                      <QuestionCircleOutlined
                        onClick={() => {
                          vscode.postMessage({
                            type: 'OPEN_IN_BROWSER',
                            payload: 'https://console.cloud.google.com/apis/credentials',
                          });
                        }}
                      />
                    </Tooltip>
                  }
                  placeholder="输入Google API KEY"
                  defaultValue={googleApiKey}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    onInputChange({ googleApiKey: e.target.value });
                  }}
                />
              </Col>
              <Col span={12}>
                <Input
                  addonAfter={
                    <Tooltip title="点击获取googleCSEId">
                      <QuestionCircleOutlined
                        onClick={() => {
                          vscode.postMessage({
                            type: 'OPEN_IN_BROWSER',
                            payload: 'https://programmablesearchengine.google.com/controlpanel/create',
                          });
                        }}
                      />
                    </Tooltip>
                  }
                  placeholder="输入googleCSEId"
                  defaultValue={googleCSEId}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    onInputChange({ googleCSEId: e.target.value });
                  }}
                />
              </Col>
            </Row>
          )}
        </div>
      );
    },
  },
  {
    scene: AgentSceneType.Webcrawler,
    icon: 'https://img14.360buyimg.com/img/jfs/t1/94916/3/39750/5682/65af7432Fe97ab230/b0a975354220b736.png',
    name: '网站爬虫',
    tip: '从网页链接中提取内容',
    beta: false,
    fileAcceptType: [],
    systemMessage: `你是一个乐于助人的助手，善于通过网络爬虫来增强你的知识。`,
    customPrompt: isDebug() ? '总结一下这个网页的内容：https://taro-docs.jd.com/docs/version' : '',
    customPlaceHolder: '示例：这个网页中的useId是做什么用的，https://react.dev/reference/react/useId',
    model: 'GPT-4-Turbo',
    requireModelFeatures: ['function_call'],
    needUploadFile: false,
    fileInfo: null,
    autoReset: false,
    agentTools: [localStorage.getItem('joycoder-crawlertype') || AgentToolType.WebBrowser],
    extraView: (props: Pic2codeExtraProps) => {
      const selectedType = useRef(localStorage.getItem('joycoder-crawlertype') || AgentToolType.PlaywrightWebBrowser);

      const onSelectChange = (value: string) => {
        const info = {
          ...props.toolBoxInfo,
          agentTools: [value],
        };
        props.onUpdateBoxInfo(info);
        localStorage.setItem('joycoder-crawlertype', value);
      };

      return (
        <div className="search2llm-extra">
          <div className="search2llm-extra-title" style={{ opacity: props.disabled ? 0.6 : 1 }}>
            爬虫方式:
          </div>
          <Row wrap={false} align={'middle'}>
            <Col flex="154px">
              <Select
                disabled={props.disabled}
                defaultValue={selectedType.current}
                size="small"
                style={{ width: 154 }}
                onChange={onSelectChange}
                popupClassName="search2llm-extra-select"
                options={[
                  {
                    value: AgentToolType.PlaywrightWebBrowser,
                    label: 'Playwright(适合所有页面)',
                  },
                  {
                    value: AgentToolType.WebBrowser,
                    label: 'Axios(更快，适合直出页面)',
                  },
                ]}
              />
            </Col>
          </Row>
        </div>
      );
    },
  },
  {
    scene: AgentSceneType.Knowledge,
    icon: 'https://img10.360buyimg.com/img/jfs/t1/248687/5/4593/6013/65d1e7e6Ff3328862/83fa90d1372c5a2e.png',
    name: '知识库',
    tip: '上传文档到个人知识库，依据知识库回答问题',
    beta: false,
    fileType: 'knowledge',
    fileAcceptType: ['.txt', '.md'],
    systemMessage: `你是一位非常热情的公司技术支持人员，名字叫JoyCode，热衷于帮助人们！阅读并理解提供的知识点，忽略问题无关的知识，回答用户问题，并尽可能以Markdown格式输出。如果没有相关知识点，则忽略知识点直接回答！！输出内容和tool_calls入参的语言要和输入保持一致！`,
    customPrompt: isDebug() ? 'JoyCode最新版本是多少？' : '',
    customPlaceHolder: '',
    model: 'GPT-4-Turbo',
    requireModelFeatures: ['function_call'],
    needUploadFile: true,
    fileInfo: {
      uid: '',
      url: KnowledgeType.user,
      name: sessionStorage.getItem('joycoder-knowledgeDoc') || KnowledgeDocType.all,
    },
    agentTools: [[AgentToolType.Knowledge, { doc: KnowledgeDocType.all, type: KnowledgeType.user }]],
    extraView: (props: Pic2codeExtraProps) => {
      const [knowledgeList, setKnowledgeList] = useState<Array<{ value: string; label: ReactNode }>>([
        {
          value: KnowledgeType.user,
          label: '私人知识库',
        },
      ]);
      const [selectedKnowledge, setSelectedKnowledge, selectedKnowledgeRef] = useStateRef<string>(
        (localStorage.getItem('joycoder-knowledge') as KnowledgeType) || KnowledgeType.user
      );
      const [docList, setDocList] = useState<Array<{ value: string; label: ReactNode }>>([
        {
          value: KnowledgeDocType.all,
          label: '全部文档',
        },
      ]);
      const [selectedDoc, setSelectedDoc] = useState(props.toolBoxInfo.fileInfo?.name);

      // 仅支持私人知识库删除
      const deleteDoc = async (toDeleteDoc: string) => {
        const [err, res] = await to(deleteUserVetorKnowledgeDocs({ doc_name: toDeleteDoc }));
        if (!err && res?.code === 0) {
          refreshDocList({
            newSelectedDoc: KnowledgeDocType.all,
            queryServer: true,
          });
        }
      };

      // 刷新文档列表和选中文档
      const refreshDocList = async (options: {
        newSelectedDoc: string;
        newSelectedKnowledge?: string;
        queryServer: boolean;
      }) => {
        const newSelectedKnowledge = options.newSelectedKnowledge || selectedKnowledge;
        let newSelectedDoc = options.newSelectedDoc;
        let _originVetorKnowledgeDocs = docList
          .filter((item) => item.value !== KnowledgeDocType.all)
          .map((item) => ({ source: item.value }));
        if (options.queryServer) {
          const [err, res] = await to(
            getVetorKnowledgeDocs(newSelectedKnowledge !== KnowledgeType.user ? newSelectedKnowledge : '')
          );
          if (!err && res?.data?.length) {
            _originVetorKnowledgeDocs = res.data;
          }
        }
        const _docList = [
          {
            value: KnowledgeDocType.all,
            label: '全部文档',
          },
          ..._originVetorKnowledgeDocs.map((item) => {
            const result = {
              value: item.source,
              label: (
                <div className="pic2code-extra-select-custom">
                  {item.source}
                  {item.source !== newSelectedDoc && newSelectedKnowledge === KnowledgeType.user ? (
                    <div
                      onClick={(e) => {
                        e?.preventDefault();
                        e?.stopPropagation();
                      }}
                    >
                      <Popconfirm
                        title={`确定删除 《${item.source}》?`}
                        okButtonProps={{ size: 'small', danger: true }}
                        cancelButtonProps={{ size: 'small' }}
                        onConfirm={() => deleteDoc(item.source)}
                        trigger={['hover']}
                        zIndex={99999}
                      >
                        <DeleteOutlined
                          className="pic2code-extra-select-custom-icon"
                          onClick={(e) => {
                            e?.preventDefault();
                            e?.stopPropagation();
                          }}
                        />
                      </Popconfirm>
                    </div>
                  ) : (
                    <></>
                  )}
                </div>
              ),
            };
            return result;
          }),
        ];
        setDocList(_docList);
        // 判断是否需要更新选中的文档
        if (newSelectedDoc) {
          const isIncludedDoc = _docList?.find((doc) => doc.value === newSelectedDoc);
          if (!isIncludedDoc) {
            newSelectedDoc = KnowledgeDocType.all;
            onSelectDocChange(newSelectedDoc);
          }
          props.onUpdateBoxInfo({
            ...props.toolBoxInfo,
            needUploadFile: newSelectedKnowledge === KnowledgeType.user,
            agentTools: [[AgentToolType.Knowledge, { doc: newSelectedDoc, type: newSelectedKnowledge }]],
          });
          sessionStorage.setItem('joycoder-knowledgeDoc', newSelectedDoc);
          setTimeout(() => setSelectedDoc(newSelectedDoc));
        }
        return _docList;
      };

      // 刷新知识库列表
      const refreshKnowledgeList = async () => {
        let _knowledgeList = knowledgeList;
        const [err, res] = await to(getVetorKnowledgeIds());
        if (!err && res?.data?.length) {
          _knowledgeList = [
            {
              value: KnowledgeType.user,
              label: '私人知识库',
            },
            ...res.data.map((item) => ({ value: item.name, label: item.desc })),
          ];
        }
        setKnowledgeList(_knowledgeList);
      };

      // 用户切换文档
      const onSelectDocChange = (value: string) => {
        refreshDocList({
          newSelectedDoc: value,
          queryServer: false,
        });
      };

      // 用户切换知识库
      const onSelectKnowledgeChange = (value: string) => {
        setSelectedKnowledge(value);
        props.onUpdateBoxInfo({
          ...props.toolBoxInfo,
          needUploadFile: selectedKnowledgeRef.current === KnowledgeType.user,
          agentTools: [[AgentToolType.Knowledge, { doc: KnowledgeDocType.all, type: value }]],
        });
        setTimeout(() => {
          refreshDocList({
            newSelectedKnowledge: value,
            newSelectedDoc: KnowledgeDocType.all,
            queryServer: true,
          });
        });
        localStorage.setItem('joycoder-knowledge', value);
      };

      useEffect(() => {
        refreshDocList({
          newSelectedDoc: props.toolBoxInfo.fileInfo?.name || '',
          queryServer: true,
        });
      }, [props.toolBoxInfo.fileInfo?.name]);

      useEffect(() => {
        refreshKnowledgeList();
      }, []);

      return (
        <div className={selectedKnowledgeRef.current === KnowledgeType.user ? 'pic2code-extra' : 'search2llm-extra'}>
          <div className="pic2code-extra-title" style={{ opacity: props.disabled ? 0.6 : 1 }}>
            知识库类型:
          </div>
          <Row wrap={true} align={'middle'}>
            <Col flex="108px">
              <Select
                disabled={props.disabled}
                value={selectedKnowledge}
                size="small"
                style={{ width: 108 }}
                popupClassName="pic2code-extra-select"
                options={knowledgeList}
                onChange={onSelectKnowledgeChange}
                // open
              />
            </Col>
            <Col flex="2px"></Col>
            <Col flex="auto">
              <Select
                disabled={props.disabled}
                value={selectedDoc}
                size="small"
                style={{ width: '100%' }}
                popupClassName="pic2code-extra-select"
                options={docList}
                onChange={onSelectDocChange}
              />
            </Col>
          </Row>
        </div>
      );
    },
  },
  {
    scene: AgentSceneType.Text2chart,
    icon: 'https://storage.360buyimg.com/dist-dev/joycoder/chatIcon/flowchat.png',
    name: '文生图表',
    tip: '通过描述生成Mermaid时序图、流程图',
    beta: false,
    fileAcceptType: [],
    systemMessage: `你是一个Mermaid脚本画图专家，善于通过用户描述，运用金字塔原理等合适的方法论，选择合适的图表类型，生成Mermaid画图脚本，使用中文。`,
    customPrompt: isDebug()
      ? '按照下面要求，使用Mermaid画一个时序图：帮我画一个客户端浏览器一次http请求过程简易描述图'
      : '',
    customPlaceHolder: '示例：按照下面要求，使用Mermaid画一个时序图：帮我画一个客户端浏览器一次http请求过程简易描述图',
    model: 'GPT-4-Turbo',
    requireModelFeatures: ['function_call'],
    needUploadFile: false,
    fileInfo: null,
    autoReset: false,
    extraView: (props: Pic2codeExtraProps) => {
      const customPrompts: Record<string, string> = {
        sequenceDiagram: '按照下面要求，使用Mermaid画一个时序图：',
        flowchat: '按照下面要求，使用Mermaid画一个流程图：',
        blockDiagram: '按照下面要求，使用Mermaid画一个块状图：',
        ganttChart: '按照下面要求，使用Mermaid画一个甘特图：',
        classDiagram: '按照下面要求，使用Mermaid画一个类图：',
        stateDiagram: '按照下面要求，使用Mermaid画一个状态图：',
        entityRelationshipDiagram: '按照下面要求，使用Mermaid画一个实体关系图：',
        userJourneyMap: '按照下面要求，使用Mermaid画一个用户旅程图：',
        pieChart: '按照下面要求，使用Mermaid画一个饼图：',
        gitGraph: '按照下面要求，使用Mermaid画一个Git图：',
        jsMind: '按照下面要求，使用Mermaid画一个思维导图：',
      };
      const selectObject: Record<string, string> = {
        sequenceDiagram: '时序图',
        flowchat: '流程图',
        jsMind: '思维导图',
        blockDiagram: '块状图',
        ganttChart: '甘特图',
        classDiagram: '类图',
        stateDiagram: '状态图',
        entityRelationshipDiagram: '实体关系图',
        userJourneyMap: '用户旅程图',
        pieChart: '饼图',
        gitGraph: 'Git图',
      };
      let chartOptions = [];
      //根据customPrompts信息，拼装选择框
      chartOptions = Object.keys(selectObject).map((chartOptionsKey) => {
        return {
          value: chartOptionsKey,
          label: `${selectObject[chartOptionsKey]}`,
        };
      });

      const onChange = (value: string) => {
        const customPrompt = customPrompts[value];
        const info = {
          ...props.toolBoxInfo,
          customPrompt,
          graphType: value,
        };
        props.onUpdateBoxInfo(info);
      };

      return (
        <div className="text2chart-extra">
          <div className="text2chart-extra-title" style={{ opacity: props.disabled ? 0.6 : 1 }}>
            图表类型:
          </div>
          <Select
            disabled={props.disabled}
            defaultValue="sequenceDiagram"
            size="small"
            style={{ width: 100 }}
            onChange={onChange}
            popupClassName="text2chart-extra-select"
            options={chartOptions}
          />
        </div>
      );
    },
  },
  {
    scene: AgentSceneType.MultiAgent,
    icon: 'https://img12.360buyimg.com/imagetools/jfs/t1/10320/19/23386/151780/6695e39fFa1014a8d/3884cb8dfd3973ea.png',
    name: '智能助手',
    tip: '爱思考的多Agent智能助手，解决复杂任务',
    beta: true,
    fileAcceptType: [],
    systemMessage: `你是一个爱思考的多Agent智能助手，如果发现输入中有违背常识的问题，必要时进行纠正，多反思检查自己生成的内容，尽可能准确地回答问题`,
    customPrompt: '',
    customPlaceHolder: '',
    agentTools: [AgentToolType.MultiAgent],
    model: 'GPT-4-Turbo',
    requireModelFeatures: ['agent'],
    needUploadFile: false,
    onlineSearch: false, //默认关闭
    fileInfo: null,
    autoReset: false,
    extraView: (props: Pic2codeExtraProps) => {
      //默认关闭
      const [checked, setChecked] = useState(false);
      //处理选择
      const handleChange = (checked: boolean) => {
        const info = {
          ...props.toolBoxInfo,
          onlineSearch: checked,
        };
        setChecked(checked);
        props.onUpdateBoxInfo(info);
      };

      return (
        <div className="text2chart-extra">
          <div className="text2chart-extra-title" style={{ opacity: props.disabled ? 0.6 : 1 }}>
            联网搜索:
          </div>
          <div>
            <Switch checked={checked} onChange={handleChange} size="small" />
            <span style={{ fontSize: ' 11px' }}> {checked ? '开' : '关'}</span>
          </div>
        </div>
      );
    },
  },
];

/**
 * 工具箱
 * @param props
 * @returns
 */
export default function ToolBox(props: IProps) {
  const [activeIdx, setActiveIdx] = useState(-1);
  const [toolBoxInfo, setToolBoxInfo] = useState<ToolItem | null>(null);
  const isOpen = useRef(localStorage.getItem('joycoder-toolboxisopen') || 'true');

  useEffect(() => {
    // disabled表示对话框中的信息已经发送中
    if (props.disabled && toolBoxInfo?.autoReset) {
      setActiveIdx(-1);
      setToolBoxInfo(null);
    }
  }, [props.disabled]);

  useHandleMessage(({ type, data }) => {
    switch (type) {
      case 'updateSelectTool':
        const index = ToolList.findIndex((item) => item.scene == data.scene);
        if (index >= 0 && activeIdx !== index) {
          onSelectTool(index);

          if (isOpen.current == 'false') {
            // mock触发打开面板
            const headerDiv =
              document.querySelector('.toolbox-collapse .joycoder-dark-collapse-header') ||
              document.querySelector('.toolbox-collapse .joycoder-light-collapse-header');
            (headerDiv as HTMLElement)?.click();
          }
        }
        break;
      default:
        break;
    }
  });

  /**
   * 折叠面板右侧内容，展示选择状态
   * @returns
   */
  const genExtra = () => {
    if (activeIdx == -1) return null;

    return (
      <div
        title="点击停用"
        onClick={(event) => {
          onSelectTool(activeIdx);
          event.stopPropagation();
        }}
        style={{ marginRight: '20px' }}
      >
        {toolBoxInfo?.name}
        <CheckCircleOutlined style={{ marginLeft: '2px', color: '#43c07b', height: '12px' }} />
      </div>
    );
  };

  /**
   * 选择切换工具
   * @param index
   * @returns
   */
  const onSelectTool = (index: number) => {
    if (props.disabled) return;
    const active = activeIdx == index ? -1 : index;
    setActiveIdx(active);
    const toolItem = ToolList[active] || null;
    setToolBoxInfo(toolItem);
    props?.onToolBoxChange(toolItem);
  };

  /**
   * 上传状态变化事件
   * @param file
   * @param fileList
   */
  const onUploadSuccess = (file: UploadFile) => {
    const info = {
      ...toolBoxInfo,
      fileInfo: file,
    } as ToolItem;
    setToolBoxInfo(info);
    props?.onToolBoxChange(info);
  };

  /**
   * 更新boxinfo
   * @param info
   */
  const onUpdateBoxInfo = (info: ToolItem | null) => {
    setToolBoxInfo(info);
    props?.onToolBoxChange(info);
  };

  const ExtraViewComponent = toolBoxInfo?.extraView;
  const isIDE = window?.joyCoderVersion === 'ide';

  return (
    <>
      {/* 上传区域 */}
      {(toolBoxInfo?.needUploadFile || ExtraViewComponent) && (
        <Row wrap={false} align={'middle'}>
          {toolBoxInfo?.needUploadFile && (
            <Col flex="45px">
              <Upload
                fileType={toolBoxInfo?.fileType}
                fileAcceptType={toolBoxInfo?.fileAcceptType}
                listenPaste={toolBoxInfo.fileType === 'image'}
                onUploadSuccess={onUploadSuccess}
                disabled={props.disabled}
              ></Upload>
            </Col>
          )}

          {ExtraViewComponent && (
            <Col flex="auto">
              <ExtraViewComponent
                disabled={props.disabled}
                toolBoxInfo={toolBoxInfo}
                onUpdateBoxInfo={onUpdateBoxInfo}
              />
            </Col>
          )}
        </Row>
      )}
      {!isIDE && (
        <Collapse
          defaultActiveKey={isOpen.current == 'true' ? ['toolbox'] : []}
          className="joycoder-collapse toolbox-collapse"
          expandIconPosition="end"
          collapsible={props.disabled ? 'disabled' : undefined}
          onChange={(e) => {
            const flag = e.length ? 'true' : 'false';
            isOpen.current = flag;
            localStorage.setItem('joycoder-toolboxisopen', flag);
            props.onCollapseChange(e);
          }}
        >
          <Panel
            header="自定义GPT"
            key="toolbox"
            extra={genExtra()}
            collapsible={props.disabled ? 'disabled' : undefined}
          >
            {/* 工具箱列表*/}
            <div className="joycoder-toolbox">
              {ToolList.map((item, index) => {
                return (
                  <div
                    className={`joycoder-toolbox-item joycoder-toolbox-item-${index} ${
                      index == activeIdx ? 'active' : ''
                    } ${props.disabled ? 'disabled' : ''}`}
                    title={item.tip}
                    key={item.scene}
                    onClick={() => {
                      onSelectTool(index);
                    }}
                  >
                    <img src={item.icon} alt="" className="joycoder-toolbox-item-img" />
                    <div className="joycoder-toolbox-item-name">{item.name}</div>
                    {item.beta && (
                      <img
                        src="https://img14.360buyimg.com/img/jfs/t1/246639/39/4222/733/65bc63b0F66623f11/e2e678e8b75b89b5.png"
                        className="joycoder-toolbox-item-beta"
                      />
                    )}
                  </div>
                );
              })}
            </div>
          </Panel>
        </Collapse>
      )}
    </>
  );
}
