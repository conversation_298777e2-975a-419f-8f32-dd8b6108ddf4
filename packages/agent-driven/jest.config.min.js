// @ts-nocheck
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  transform: {
    '^.+\\.tsx?$': [
      'ts-jest',
      {
        tsconfig: { module: 'CommonJS', moduleResolution: 'node', esModuleInterop: !0, allowJs: !0 },
        diagnostics: !1,
        isolatedModules: !0,
      },
    ],
  },
  testMatch: ['**/__tests__/**/*.test.ts'],
  moduleNameMapper: { '^vscode$': '<rootDir>/src/__mocks__/vscode.js' },
  transformIgnorePatterns: [
    'node_modules/(?!(@modelcontextprotocol|delay|p-wait-for|serialize-error|strip-ansi|default-shell|os-name|strip-bom)/)',
  ],
  roots: ['<rootDir>/src'],
  modulePathIgnorePatterns: ['.vscode-test'],
};
