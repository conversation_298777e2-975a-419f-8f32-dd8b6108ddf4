import { HTMLAttributes, useCallback, useEffect, useMemo, useState } from 'react';
import { useEvent } from 'react-use';
import { Virtuoso } from 'react-virtuoso';
import { But<PERSON>, Divider } from 'antd';
import { DownOutlined, StopOutlined } from '@ant-design/icons';
import { safeJsonParse } from '../../utils/safeJsonParse';
import { vscode } from '../../utils/vscode';
import CodeBlock from '../common/CodeBlock';
import { COMMAND_OUTPUT_STRING, commandExecutionStatusSchema } from '../../utils/schema';

interface CommandExecutionProps {
  executionId: string;
  text?: string;
}

export const CommandExecution = ({ executionId, text }: CommandExecutionProps) => {
  // 如果我们没有为此命令打开 VSCode 终端，则默认展开命令执行输出。
  const [isExpanded, setIsExpanded] = useState(false);

  const [status, setStatus] = useState<any | null>(null);
  const [output, setOutput] = useState('');
  const [command, setCommand] = useState(text);

  const lines = useMemo(
    () => [`$ ${command}`, ...output.split('\n').filter((line) => line.trim() !== '')],
    [output, command],
  );

  const onMessage = useCallback(
    (event: MessageEvent) => {
      const message: any = event.data;

      if (message.type === 'commandExecutionStatus') {
        const result = commandExecutionStatusSchema.safeParse(safeJsonParse(message.text, {}));

        if (result.success) {
          const data = result.data;

          if (data.executionId !== executionId) {
            return;
          }

          switch (data.status) {
            case 'started':
              setCommand(data.command);
              setStatus(data);
              break;
            case 'output':
              setOutput((output) => output + data.output);
              break;
            case 'fallback':
              setIsExpanded(true);
              break;
            default:
              setStatus(data);
              break;
          }
        }
      }
    },
    [executionId],
  );

  useEvent('message', onMessage);

  useEffect(() => {
    if (!status && text) {
      const index = text.indexOf(COMMAND_OUTPUT_STRING);

      if (index === -1) {
        setCommand(text);
      } else {
        setCommand(text.slice(0, index));
        setOutput(text.slice(index + COMMAND_OUTPUT_STRING.length));
      }
    }
  }, [status, text]);

  return (
    <div className="w-full bg-vscode-editor-background border border-vscode-border rounded p-2">
      <CodeBlock source={command} />
      <div className="flex flex-row items-center justify-between gap-2 px-1">
        <div className="flex flex-row items-center gap-1">
          {status?.status === 'started' && (
            <div className="flex flex-row items-center gap-2 font-mono text-xs">
              <div className="rounded-full w-1.5 h-1.5 bg-green-400" />
              <div>运行中</div>
              {status.pid && <div className="whitespace-nowrap">(进程ID: {status.pid})</div>}
              <Button
                type="text"
                size="small"
                icon={<StopOutlined />}
                onClick={() => vscode.postMessage({ type: 'terminalOperation', terminalOperation: 'abort' })}
              />
            </div>
          )}
          {status?.status === 'exited' && (
            <div className="flex flex-row items-center gap-2 font-mono text-xs">
              <div className={`rounded-full w-1.5 h-1.5 ${status.exitCode === 0 ? 'bg-green-400' : 'bg-red-400'}`} />
              <div className="whitespace-nowrap">已退出 (退出码: {status.exitCode})</div>
            </div>
          )}
          {lines.length > 0 && (
            <Button
              type="text"
              size="small"
              icon={<DownOutlined className={`transition-transform duration-300 ${isExpanded ? 'rotate-180' : ''}`} />}
              onClick={() => setIsExpanded(!isExpanded)}
            />
          )}
        </div>
      </div>
      {isExpanded && lines.length > 0 && (
        <>
          <Divider className="my-1" />
          <div style={{ height: Math.min((lines.length + 1) * 16, 200) }}>
            <Virtuoso
              className="h-full"
              totalCount={lines.length}
              itemContent={(i) => <Line className="text-sm">{lines[i]}</Line>}
              followOutput="auto"
            />
          </div>
        </>
      )}
    </div>
  );
};

type LineProps = HTMLAttributes<HTMLDivElement>;

const Line = ({ className, ...props }: LineProps) => {
  return (
    <div
      className={`font-mono text-vscode-editor-foreground whitespace-pre-wrap break-words ${className}`}
      {...props}
    />
  );
};

CommandExecution.displayName = 'CommandExecution';
