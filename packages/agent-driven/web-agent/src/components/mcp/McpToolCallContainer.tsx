import React, { useState } from 'react';
import { VSCodeCheckbox } from '@vscode/webview-ui-toolkit/react';
import { JoyCoderAskUseMcpServer } from '../../../../src/shared/ExtensionMessage';
import { McpServer } from '@joycoder/shared/src/mcp/mcp';
import { vscode } from '../../utils/vscode';
import CodeAccordianAdaptor from '../../adaptor/components/CodeAccordianAdaptor';

interface McpToolCallContainerProps {
  useMcpServer: JoyCoderAskUseMcpServer;
  server?: McpServer;
  responseMessage?: { text: string } | null;
  onToggleExpand: () => void;
}

const McpToolCallContainer: React.FC<McpToolCallContainerProps> = ({
  useMcpServer,
  server,
  responseMessage,
  onToggleExpand,
}) => {
  // 根据自动确认状态决定默认折叠状态
  const [isCollapsed, setIsCollapsed] = useState(() => {
    const tool = server?.tools?.find((tool) => tool.name === useMcpServer.toolName);
    // return tool?.autoApprove || false;
    return true; // 不管是不是自动执行调用mcp工具默认都折叠
  });

  const tool = server?.tools?.find((tool) => tool.name === useMcpServer.toolName);

  return (
    <div
      style={{
        border: '1px solid var(--vscode-editorGroup-border)',
        borderRadius: '3px',
        overflow: 'hidden',
      }}
    >
      {/* 可折叠的头部 */}
      <div
        onClick={() => setIsCollapsed(!isCollapsed)}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '4px 0',
          cursor: 'pointer',
          backgroundColor: 'var(--vscode-button-secondaryBackground)',
          color: 'var(--vscode-button-secondaryForeground, #72747C)',
          borderBottom: isCollapsed ? 'none' : '1px solid var(--vscode-editorGroup-border)',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
          <span className={`codicon codicon-chevron-${isCollapsed ? 'right' : 'down'}`}></span>
          <span className="codicon codicon-symbol-method"></span>
          <span style={{ fontWeight: 500 }}>{useMcpServer.toolName || ''}</span>
        </div>
        {tool && (
          <VSCodeCheckbox
            checked={tool.autoApprove || false}
            onChange={() => {
              if (useMcpServer.serverName) {
                vscode.postMessage({
                  type: 'toggleToolAutoApprove',
                  serverName: useMcpServer.serverName,
                  toolName: useMcpServer.toolName,
                  autoApprove: !tool.autoApprove,
                });
              }
            }}
            onClick={(e) => e.stopPropagation()}
            className='auto-implement'
          >
            自动执行
          </VSCodeCheckbox>
        )}
      </div>
      
      {/* 可折叠的内容 */}
      {!isCollapsed && (
        <div style={{ padding: '8px 10px', backgroundColor: 'var(--vscode-editor-background)' }}>
          <div onClick={(e) => e.stopPropagation()}>
            {/* 显示工具描述 */}
            {tool?.description && (
              <div
                style={{
                  marginTop: '4px',
                  opacity: 0.8,
                  fontSize: '12px',
                }}
              >
                {tool.description}
              </div>
            )}
            
            {/* 显示工具参数信息 */}
            {tool?.inputSchema &&
              'properties' in tool.inputSchema &&
              Object.keys((tool.inputSchema as any).properties || {}).length > 0 && (
                <div
                  style={{
                    marginTop: '8px',
                    fontSize: '12px',
                    border: '1px solid color-mix(in srgb, var(--vscode-descriptionForeground) 30%, transparent)',
                    borderRadius: '3px',
                    padding: '8px',
                  }}
                >
                  <div
                    style={{
                      marginBottom: '4px',
                      opacity: 0.8,
                      fontSize: '11px',
                      textTransform: 'uppercase',
                    }}
                  >
                    参数定义
                  </div>
                  {Object.entries((tool.inputSchema as any).properties || {}).map(([paramName, schema]: [string, any]) => {
                    const isRequired =
                      tool.inputSchema &&
                      'required' in tool.inputSchema &&
                      Array.isArray((tool.inputSchema as any).required) &&
                      (tool.inputSchema as any).required.includes(paramName);

                    return (
                      <div
                        key={paramName}
                        style={{
                          display: 'flex',
                          alignItems: 'baseline',
                          marginTop: '4px',
                        }}
                      >
                        <code
                          style={{
                            color: 'var(--vscode-textPreformat-foreground)',
                            marginRight: '8px',
                          }}
                        >
                          {paramName}
                          {isRequired && (
                            <span
                              style={{
                                color: 'var(--vscode-errorForeground)',
                              }}
                            >
                              *
                            </span>
                          )}
                        </code>
                        <span
                          style={{
                            opacity: 0.8,
                            overflowWrap: 'break-word',
                            wordBreak: 'break-word',
                          }}
                        >
                          {schema.description || '没有描述信息'}
                        </span>
                      </div>
                    );
                  })}
                </div>
              )}
          </div>
          
          {/* 显示实际参数 */}
          {useMcpServer.arguments && useMcpServer.arguments !== '{}' && (
            <div style={{ marginTop: '8px' }}>
              <div
                style={{
                  marginBottom: '4px',
                  opacity: 0.8,
                  fontSize: '12px',
                  textTransform: 'uppercase',
                }}
              >
                参数
              </div>
              <CodeAccordianAdaptor
                code={useMcpServer.arguments}
                language="json"
                isExpanded={true}
                onToggleExpand={onToggleExpand}
              />
            </div>
          )}
          
          {/* 显示对应的响应 */}
          {responseMessage && (
            <div style={{ marginTop: '8px' }}>
              <div
                style={{
                  marginBottom: '4px',
                  opacity: 0.8,
                  fontSize: '12px',
                  textTransform: 'uppercase',
                }}
              >
                响应
              </div>
              <CodeAccordianAdaptor
                code={responseMessage.text}
                language="json"
                isExpanded={true}
                onToggleExpand={onToggleExpand}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default McpToolCallContainer;
