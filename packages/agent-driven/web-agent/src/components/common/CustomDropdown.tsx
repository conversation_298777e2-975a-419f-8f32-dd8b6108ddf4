import React, { useState, useRef, useEffect, ReactNode, useCallback } from 'react';
import './CustomDropdown.scss';

export interface DropdownMenuItem {
  key: string | number;
  label: ReactNode;
  type?: 'group' | 'item';
  children?: DropdownMenuItem[];
  onClick?: () => void;
}

export interface CustomDropdownProps {
  children: ReactNode;
  menu: {
    items: DropdownMenuItem[];
  };
  trigger?: ('click' | 'hover')[];
  placement?: 'top' | 'bottom' | 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';
  disabled?: boolean;
  className?: string;
  getPopupContainer?: () => HTMLElement;
  onOpenChange?: (open: boolean) => void;
  open?: boolean;
}

export const CustomDropdown: React.FC<CustomDropdownProps> = ({
  children,
  menu,
  trigger = ['click'],
  placement = 'bottom',
  disabled = false,
  className = '',
  getPopupContainer,
  onOpenChange,
  open: controlledOpen,
}) => {
  const [internalOpen, setInternalOpen] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [maxHeight, setMaxHeight] = useState(356); // 默认最大高度
  const triggerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const isControlled = controlledOpen !== undefined;
  const isOpen = isControlled ? controlledOpen : internalOpen;

  const handleToggle = () => {
    if (disabled) return;

    const newOpen = !isOpen;
    if (!isControlled) {
      setInternalOpen(newOpen);
    }
    onOpenChange?.(newOpen);
  };

  const handleClose = useCallback(() => {
    if (!isControlled) {
      setInternalOpen(false);
    }
    onOpenChange?.(false);
  }, [isControlled, onOpenChange]);

  const calculateMaxHeight = useCallback(() => {
    // 获取窗口高度，并设置最大高度为窗口高度的60%，但不超过356px
    const windowHeight = window.innerHeight;
    const calculatedMaxHeight = Math.min(windowHeight * 0.5, 356);
    setMaxHeight(calculatedMaxHeight);
  }, []);

  const calculatePosition = useCallback(() => {
    if (!triggerRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const container = getPopupContainer?.() || document.body;
    const containerRect = container.getBoundingClientRect();

    let top = 0;
    let left = 0;

    // 对于 top 位置，需要考虑下拉菜单的高度
    switch (placement) {
      case 'top':
        top = triggerRect.top - containerRect.top;
        left = triggerRect.left - containerRect.left;
        break;
      case 'topLeft':
        top = triggerRect.top - containerRect.top;
        left = triggerRect.left - containerRect.left;
        break;
      case 'topRight':
        top = triggerRect.top - containerRect.top;
        left = triggerRect.right - containerRect.left;
        break;
      case 'bottom':
        top = triggerRect.bottom - containerRect.top;
        left = triggerRect.left - containerRect.left;
        break;
      case 'bottomLeft':
        top = triggerRect.bottom - containerRect.top;
        left = triggerRect.left - containerRect.left;
        break;
      case 'bottomRight':
        top = triggerRect.bottom - containerRect.top;
        left = triggerRect.right - containerRect.left;
        break;
    }

    setPosition({ top, left });
  }, [placement, getPopupContainer]);

  useEffect(() => {
    if (isOpen) {
      calculatePosition();
    }
  }, [isOpen, placement, calculatePosition]);

  useEffect(() => {
    // 初始化时计算最大高度
    calculateMaxHeight();
  }, [calculateMaxHeight]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        triggerRef.current &&
        dropdownRef.current &&
        !triggerRef.current.contains(event.target as Node) &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        handleClose();
      }
    };

    const handleResize = () => {
      if (isOpen) {
        calculatePosition();
      }
      // 窗口大小改变时重新计算最大高度
      calculateMaxHeight();
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleResize);
    };
  }, [isOpen, calculateMaxHeight, calculatePosition, handleClose]);

  const renderMenuItem = (item: DropdownMenuItem, index: number) => {
    if (item.type === 'group') {
      return (
        <div key={item.key} className="custom-dropdown-menu-item-group">
          <div className="custom-dropdown-menu-item-group-title">{item.label}</div>
          <div className="custom-dropdown-menu-item-group-list">
            {item.children?.map((child, childIndex) => renderMenuItem(child, childIndex))}
          </div>
        </div>
      );
    }

    return (
      <div
        key={item.key}
        className="custom-dropdown-menu-item"
        onClick={() => {
          item.onClick?.();
          handleClose();
        }}
      >
        {item.label}
      </div>
    );
  };

  const triggerProps: any = {};

  if (trigger.includes('click')) {
    triggerProps.onClick = handleToggle;
  }

  if (trigger.includes('hover')) {
    triggerProps.onMouseEnter = () => {
      if (!disabled && !isControlled) {
        setInternalOpen(true);
        onOpenChange?.(true);
      }
    };
    triggerProps.onMouseLeave = () => {
      if (!disabled && !isControlled) {
        setInternalOpen(false);
        onOpenChange?.(false);
      }
    };
  }

  const container = getPopupContainer?.() || document.body;

  return (
    <>
      <div
        ref={triggerRef}
        // className={`custom-dropdown-trigger ${disabled ? 'disabled' : ''}`}
        {...triggerProps}
      >
        {children}
      </div>

      {isOpen && (
        <div
          ref={dropdownRef}
          className={`custom-dropdown-menu custom-dropdown-placement-${placement} ${className}`}
          style={{
            position: container === document.body ? 'fixed' : 'absolute',
            top: position.top,
            left: position.left,
            zIndex: 1200,
            maxHeight: `${maxHeight}px`,
          }}
        >
          {menu.items.map((item, index) => renderMenuItem(item, index))}
        </div>
      )}
    </>
  );
};

export default CustomDropdown;
