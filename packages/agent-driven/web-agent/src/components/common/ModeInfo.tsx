import React, { useState, useCallback } from 'react';
import styled from 'styled-components';
import Avatar, { AvatarProps } from './Avatar';
import CustomTooltip from './CustomTooltip';

export interface ModeInfoProps {
  /** 模式名称 */
  name: string;
  /** 头像大小，默认为20px */
  avatarSize?: number;
  /** 是否显示头像，默认为true */
  showAvatar?: boolean;
  /** 自定义className */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 头像的额外属性 */
  avatarProps?: Partial<AvatarProps>;
  /** 智能体ID，用于显示对应的图标 */
  agentId?: string;
}

const ModeInfoContainer = styled.div`
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
  height: 20px;

  @media (max-width: 600px) {
    gap: 8px;
    margin-bottom: 6px;
  }
`;

const ModeName = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: var(--vscode-foreground);
  line-height: 16px;
  height: 16px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  @media (max-width: 600px) {
    font-size: 12px;
  }
`;

/**
 * ModeInfo组件 - 显示模式信息（头像+名称）
 * 用于聊天界面中显示AI模式或用户信息
 */
export const ModeInfo: React.FC<ModeInfoProps> = ({
  name,
  avatarSize = 20,
  showAvatar = true,
  className,
  style,
  avatarProps = {},
  agentId
}) => {
  const [isOverflowing, setIsOverflowing] = useState(false);

  // 使用回调 ref 来获取元素引用并检测溢出
  const nameRefCallback = useCallback((element: HTMLDivElement | null) => {
    if (element) {
      const checkOverflow = () => {
        const isTextOverflowing = element.scrollWidth > element.clientWidth;
        setIsOverflowing(isTextOverflowing);
      };

      // 立即检查一次
      checkOverflow();

      // 使用 ResizeObserver 监听元素大小变化
      const resizeObserver = new ResizeObserver(checkOverflow);
      resizeObserver.observe(element);

      // 监听窗口大小变化
      window.addEventListener('resize', checkOverflow);

      // 清理函数
      return () => {
        resizeObserver.disconnect();
        window.removeEventListener('resize', checkOverflow);
      };
    }
  }, []);

  return (
    <ModeInfoContainer className={className} style={style}>
      {showAvatar && (
        <Avatar
          alt={name}
          size={avatarSize}
          agentId={agentId}
          {...avatarProps}
        />
      )}
      <CustomTooltip
        title={name}
        placement="top"
        hidden={!isOverflowing}
      >
        <ModeName ref={nameRefCallback}>{name}</ModeName>
      </CustomTooltip>
    </ModeInfoContainer>
  );
};

export default ModeInfo;
