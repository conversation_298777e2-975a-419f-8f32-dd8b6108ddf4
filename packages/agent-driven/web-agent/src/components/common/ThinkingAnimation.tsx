import React from 'react';

export interface ThinkingAnimationProps {
  /** 动画宽度，默认为 '84px' */
  width?: string | number;
  /** 动画高度，默认为自动 */
  height?: string | number;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
  /** alt 文本，默认为 'thinking' */
  alt?: string;
  /** 自定义动画 URL，默认使用内置的思考动画 */
  src?: string;
}

/**
 * 思考动画组件
 * 用于显示 AI 正在思考的状态
 */
const ThinkingAnimation: React.FC<ThinkingAnimationProps> = ({
  width = '84px',
  height,
  style,
  className,
  alt = 'thinking',
  src = 'https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/thinking.gif',
}) => {
  // 合并样式
  const mergedStyle: React.CSSProperties = {
    width,
    ...(height && { height }),
    ...style,
  };

  return (
    <img
      src={src}
      alt={alt}
      style={mergedStyle}
      className={className}
    />
  );
};

export default ThinkingAnimation;
