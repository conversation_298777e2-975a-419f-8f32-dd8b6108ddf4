import React, { useCallback, useEffect, useState } from 'react';

interface ThinkingProp {
  isExpanded?: boolean;
  isReasoningCompleted?: boolean;
  messageTs?: number;
  now?: number;
  props: any;
}

export default function Thinking({ isExpanded, isReasoningCompleted, props, messageTs }: ThinkingProp) {
  const [expandedRows, setExpandedRows] = useState<Record<number, boolean>>({});
  const [expandedRowTime, setExpandedRowTime] = useState<Record<number, any>>({});

  const onToggleExpand = useCallback((ts?: number) => {
    if (ts) {
      setExpandedRows((prev) => ({
        ...prev,
        [ts]: !prev[ts],
      }));
    }
  }, []);

  useEffect(() => {
    if (messageTs !== undefined) {
      const diffTime = Math.floor((new Date().getTime() - messageTs) / 1000);
      setExpandedRows((prev) => ({
        ...prev,
        [messageTs]: !!isExpanded,
      }));
      setExpandedRowTime((prev) => ({
        ...prev,
        [messageTs]: !isReasoningCompleted ? 0 : diffTime > 10 ? 0 : diffTime,
      }));
    }
  }, [messageTs, isExpanded, isReasoningCompleted]);

  return (
    <div
      onClick={(e) => onToggleExpand(messageTs)} // 添加点击事件处理器到最外层div
      style={{
        overflow: 'hidden',
        cursor: 'pointer', // 添加指针样式，提示可点击
        lineHeight: 'normal', // 重置行高
      }}
    >
      {/* 明确区分三种状态的渲染逻辑 */}
      {expandedRows[messageTs ?? -1] ? (
        /* 展开状态显示完整的reasoning内容 */
        <div>
          <div
            style={{
              cursor: 'pointer',
              width: '100%', // 确保点击区域足够大
              display: 'flex',
            }}
          >
            {/* 根据reasoning是否完成显示不同的头部 */}
            {isReasoningCompleted && (
              <span
                style={{
                  maxWidth: '95px',
                  height: '20px',
                  backgroundColor: 'var(--vscode-button-secondaryBackground, #72747C)',
                  borderRadius: '4px',
                  display: 'flex',
                  alignItems: 'center',
                  color: 'var(--vscode-button-secondaryForeground, #72747C)',
                  cursor: 'pointer',
                  margin: '7px 0 5px',
                  opacity: 0.5,
                }}
              >
                <i
                  className="icon iconfont icon-shendusikao"
                  style={{ marginLeft: '5px', fontSize: '11px', height: '11px' }}
                />
                <span style={{ margin: '0 4px', fontSize: '11px', height: '11px', lineHeight: '11px' }}>
                  {expandedRowTime[messageTs ?? -1] > 0 ? `思考${expandedRowTime[messageTs ?? -1]}秒` : '已思考'}
                </span>
                <i className="icon iconfont icon-xiajiantou" style={{ fontSize: '14px', height: '15px' }} />
              </span>
            )}
          </div>
          <span
            style={{
              color: 'var(--vscode-editor-foreground, #72747C)',
              fontStyle: 'italic',
              fontSize: '12px',
              lineHeight: '18px',
              display: 'block',
              paddingLeft: '12px',
              margin: `${isReasoningCompleted ? '0 0 8px 8px' : '0 0 8px 8px'}`,
              borderLeft: '1px solid var(--vscode-button-secondaryBackground, #72747C)',
              opacity: 0.3,
            }}
          >
            {props.children}
          </span>
        </div>
      ) : isReasoningCompleted ? (
        /* 如果reasoning已完成且未展开，显示"已深度思考"样式 */
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
            width: '100%', // 确保点击区域足够大
          }}
        >
          <span
            style={{
              maxWidth: '95px',
              height: '20px',
              backgroundColor: 'var(--vscode-button-secondaryBackground, #72747C)',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              color: 'var(--vscode-button-secondaryForeground, #72747C)',
              cursor: 'pointer',
              margin: '7px 0 5px',
              opacity: 0.5,
            }}
          >
            <i
              className="icon iconfont icon-shendusikao"
              style={{ marginLeft: '5px', fontSize: '11px', height: '11px' }}
            />
            <span style={{ margin: '0 4px', fontSize: '11px', height: '11px', lineHeight: '11px' }}>
              {expandedRowTime[messageTs ?? -1] > 0 ? `思考${expandedRowTime[messageTs ?? -1]}秒` : '已思考'}
            </span>
            <i className="icon iconfont icon-youjiantou" style={{ fontSize: '14px', height: '15px' }} />
          </span>
        </div>
      ) : (
        /* 如果reasoning正在进行中且未展开，显示思考动画 */
        <div
          style={{
            cursor: 'pointer',
            width: '100%', // 确保点击区域足够大
          }}
        >
          <span
            style={{
              color: 'var(--vscode-editor-foreground, #72747C)',
              fontStyle: 'italic',
              fontSize: '12px',
              lineHeight: '18px',
              display: 'block',
              paddingLeft: '12px',
              margin: '0 0 8px 8px',
              borderLeft: '1px solid var(--vscode-button-secondaryBackground, #72747C)',
              opacity: 0.3,
            }}
          >
            {props.children}
          </span>
        </div>
      )}
    </div>
  );
}
