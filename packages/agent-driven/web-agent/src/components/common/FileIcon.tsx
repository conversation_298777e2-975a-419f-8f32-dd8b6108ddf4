import React from 'react';

// 文件图标相关的样式和配置
const ICON_CONTAINER_STYLE = {
  display: 'flex',
  width: '14px',
  height: '14px',
  border: '1px solid var(--vscode-editorGroup-border)',
  borderRadius: '2px',
  alignItems: 'center',
  justifyContent: 'center',
  marginRight: '4px'
} as const;

const ICON_STYLE = {
  fontSize: '9px'
} as const;

// 获取文件扩展名
const getFileExtension = (path: string | undefined): string => {
  if (!path) return '';
  return path.match(/\.([^.]+)$/)?.[1] || 'daima';
};

// 文件图标组件
export interface FileIconProps {
  path?: string;
  fallbackIcon?: React.ReactNode;
}

const FileIcon: React.FC<FileIconProps> = ({ path, fallbackIcon }) => {
  if (!path) return <>{fallbackIcon}</>;

  const extension = getFileExtension(path);

  // daima 扩展名使用特殊样式
  if (extension === 'daima') {
    return (
      <i
        style={{ fontSize: '14px' }}
        className={`icon iconfont icon-${extension}`}
      />
    );
  }

  // 其他扩展名使用容器包装
  const getIconClass = (ext: string): string => {
    switch (ext) {
      case 'js':
        return 'icon-js';
      case 'html':
        return 'icon-html';
      case 'css':
        return 'icon-a-';
      default:
        return 'icon-daima';
    }
  };

  return (
    <span style={ICON_CONTAINER_STYLE}>
      <i
        style={ICON_STYLE}
        className={`icon iconfont ${getIconClass(extension)}`}
      />
    </span>
  );
};

export default FileIcon;
