.joycoder-prompt {
  z-index: 11;
  background: var(--background);
  font-family: var(--font-family);
  color: var(--vscode-vscode-foreground);
  overflow-y: auto;
  overflow-x: hidden;
  .text-vscode-foreground {
    color: var(--color-vscode-foreground);
  }
  .mode-selector-tip{
    color: var(--vscode-vscode-foreground);
    background: var(--background);
    border: 0px;
    margin: 0 0 10px;
    padding: 5px !important;
    div{
      font-size: 12px;
    }
  }
  .mode-edit-btn{
    padding:4.4px 0px !important;
  }
}

.mode-edit-item{
  font-size: 10px;
}

// 自定义 antd Select 组件样式
.ant-select-selector {
  border-color: var(--vscode-editorWidget-border, rgba(127, 127, 127, 0.3)) !important;
  background-color: var(--vscode-input-background) !important;
  color: var(--vscode-input-foreground) !important;
}

.ant-select-dropdown {
  background-color: var(--vscode-dropdown-background) !important;
  border-color: var(--vscode-dropdown-border) !important;
}

.ant-select-item {
  color: var(--vscode-dropdown-foreground) !important;

  &:hover {
    background-color: var(--vscode-list-hoverBackground, rgba(127, 127, 127, 0.1)) !important;
  }

  &.ant-select-item-option-selected {
    background-color: var(--vscode-list-activeSelectionBackground, #0060c0) !important;
    color: var(--vscode-list-activeSelectionForeground, #ffffff) !important;
  }
}

.option-label {
  display: flex;
  flex-direction: column;

  .option-label-zh {
    font-weight: bold;
  }

  .option-label-en {
    font-size: 12px;
    opacity: 0.8;
  }
}
/* 隐藏滚动条 */
::-webkit-scrollbar {
  width: 8px;  /* 对于垂直滚动条 */
  background: transparent; /* 透明 */
}

/* 设置滚动条滑块的样式 */
::-webkit-scrollbar-thumb {
  max-height: 50px;
  background: rgba(0, 0, 0, 0.2); /* 半透明 */
  border-radius: 10px; /* 圆角 */
}

/* 当鼠标悬停在滚动条滑块上时，设置其样式 */
::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5); /* 更深的半透明 */
}

/* 设置滚动条轨道的样式 */
::-webkit-scrollbar-track {
  background: transparent; /* 透明 */
}

body.joycoder-dark {
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.5);
  }
  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
  }
}
