import React, { useCallback, useState } from 'react';
import { <PERSON><PERSON>, Button, Select, Dropdown, MenuProps, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { ModeSelectorProps } from './types';
import { vscode } from '../../utils/vscode';
import { ModeConfig } from '../../utils/modes';

const { Option } = Select;

const ModeSelector: React.FC<ModeSelectorProps> = ({
  visualMode,
  setVisualMode,
  modes,
  customModes,
  switchMode,
  setIsToolsEditMode,
  updateCustomMode,
  handleModeSwitch,
  openCreateModeDialog,
}) => {
  // 查找模式函数
  // Helper function to find a mode by agentId
  const findModeBySlug = useCallback(
    (searchSlug: string, modes: readonly ModeConfig[] | undefined): ModeConfig | undefined => {
      if (!modes) return undefined;
      const isModeWithSlug = (mode: ModeConfig): mode is ModeConfig => mode.agentId === searchSlug;
      return modes.find(isModeWithSlug);
    },
    [],
  );
  const [searchValue, setSearchValue] = useState('');
  const menuItems: MenuProps['items'] = [
    {
      key: 'global',
      label: <span className="mode-edit-item">修改全局模式</span>,
      onClick: () => {
        vscode.postMessage({
          type: 'openCustomModesSettings',
        });
      },
    },
    {
      key: 'project',
      label: <span className="mode-edit-item">编辑项目模式 (mode.json)</span>,
      onClick: () => {
        vscode.postMessage({
          type: 'openFile',
          text: './.joycode/mode.json',
          values: {
            create: true,
            content: JSON.stringify({ customModes: [] }, null, 2),
          },
        });
      },
    },
  ];

  const filteredModes = modes.filter((modeConfig) =>
    searchValue ? modeConfig.name.toLowerCase().includes(searchValue.toLowerCase()) : true,
  );

  return (
    <div className="mode-selector">
      <Alert className="mode-selector-tip" description="点击 + 创建模式，或在对话时让AI创建一个新模式。" type="info" />
      <div className="flex justify-between items-center mb-3">
        <Select
          style={{ width: 'calc(100% - 84px)' }}
          value={visualMode}
          showSearch
          placeholder="选择模式"
          optionFilterProp="children"
          onChange={(value) => {
            const selectedMode = modes.find((m) => m.agentId === value);
            if (selectedMode) {
              handleModeSwitch(selectedMode);
            }
          }}
          onSearch={setSearchValue}
          filterOption={false}
          suffixIcon={<SearchOutlined />}
        >
          {filteredModes.map((modeConfig) => (
            <Option key={modeConfig.agentId} value={modeConfig.agentId}>
              <div className="flex items-center justify-between w-full">
                <span>{modeConfig.name}</span>
                <span className="text-foreground">{modeConfig.agentId}</span>
              </div>
            </Option>
          ))}
        </Select>
        <div className="flex gap-2">
          <Button
            className="mode-edit-btn"
            icon={<span className="codicon codicon-add" />}
            onClick={openCreateModeDialog}
            title="创建新模式"
          />
          <Dropdown menu={{ items: menuItems }} placement="bottom" trigger={['click']}>
            <Button
              icon={<span className="codicon codicon-json" />}
              className="mode-edit-btn"
              onClick={(e) => e.preventDefault()}
              title="编辑模式配置"
            />
          </Dropdown>
        </div>
      </div>

      {/* 显示当前模式的名称和删除按钮 */}
      {visualMode && findModeBySlug(visualMode, customModes) && (
        <div className="flex gap-3 mb-4">
          <div className="flex-1">
            <div className="font-bold mb-1">模式名称</div>
            <div className="flex gap-2">
              <Input
                type="text"
                value={findModeBySlug(visualMode, customModes)?.name || ''}
                onChange={(e) => {
                  const customMode = findModeBySlug(visualMode, customModes);
                  if (customMode) {
                    updateCustomMode(visualMode, {
                      ...customMode,
                      name: e.target.value,
                      source: customMode.source || 'global',
                    });
                  }
                }}
                className="w-full"
              />
              <Button
                icon={<span className="codicon codicon-trash" />}
                title="删除模式"
                onClick={() => {
                  vscode.postMessage({
                    type: 'deleteCustomMode',
                    agentId: visualMode,
                  });
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModeSelector;
