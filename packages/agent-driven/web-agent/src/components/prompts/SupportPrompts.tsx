import React, { useState, useEffect } from 'react';
import { Input, Button, Select } from 'antd';
import { SupportPromptsProps } from './types';
import { supportPrompt, SupportPromptType } from '../../utils/support-prompt';
import { vscode } from '../../utils/vscode';

const { TextArea } = Input;

const SupportPrompts: React.FC<SupportPromptsProps> = ({
  customSupportPrompts,
  updateSupportPrompt,
  handleSupportReset,
}) => {
  const [activeSupportOption, setActiveSupportOption] = useState<SupportPromptType>('ENHANCE');
  const [testPrompt, setTestPrompt] = useState('');
  const [isEnhancing, setIsEnhancing] = useState(false);

  const getSupportPromptValue = (type: SupportPromptType): string => {
    return supportPrompt.get(customSupportPrompts, type);
  };

  const [sysPrompt, setSysPrompt] = useState(getSupportPromptValue(activeSupportOption));
  useEffect(() => {
    setSysPrompt(getSupportPromptValue(activeSupportOption));
  }, [customSupportPrompts]);

  const handleTestEnhancement = () => {
    if (!testPrompt.trim()) return;
    setIsEnhancing(true);
    vscode.postMessage({
      type: 'enhancePrompt',
      text: testPrompt,
    });
  };

  useEffect(() => {
    const handler = (event: { data: any }) => {
      const message = event.data;
      if (message.type === 'enhancedPrompt') {
        if (message.text) {
          setTestPrompt(message.text);
        }
        setIsEnhancing(false);
      }
    };

    window.addEventListener('message', handler);
    return () => window.removeEventListener('message', handler);
  }, []);

  // 中英文标签对应关系
  const getSupportPromptTypeLabels = (type: string): { zh: string; en: string } => {
    const labels: { [key: string]: { zh: string; en: string } } = {
      ENHANCE: { zh: '增强提示词', en: 'Enhance' },
      EXPLAIN: { zh: '解释代码', en: 'Explain' },
      FIX: { zh: '修复问题', en: 'Fix' },
      IMPROVE: { zh: '改进代码', en: 'Improve' },
      ADD_TO_CONTEXT: { zh: '添加到上下文', en: 'Add to Context' },
      TERMINAL_ADD_TO_CONTEXT: { zh: '添加终端内容到上下文', en: 'Add Terminal to Context' },
      TERMINAL_FIX: { zh: '修复终端命令', en: 'Fix Terminal Command' },
      TERMINAL_EXPLAIN: { zh: '解释终端命令', en: 'Explain Terminal Command' },
      NEW_TASK: { zh: '新任务', en: 'New Task' },
      // 添加其他支持提示类型的中英文标签
    };
    return labels[type] || { zh: type, en: type };
  };

  // 保留原来的函数以兼容现有代码
  const getSupportPromptTypeLabel = (type: string): string => {
    return getSupportPromptTypeLabels(type).zh;
  };

  const getSupportPromptTypeDescription = (type: SupportPromptType): string => {
    const descriptions: { [key in SupportPromptType]?: string } = {
      ENHANCE: '优化提示获取更好回答（点击✨使用）',
      EXPLAIN:
        '解读代码逻辑（支持文件/片段），可在代码操作（编辑器中的灯泡图标）和编辑器上下文菜单（右键点击选中的代码）中使用。',
      FIX: '查找修复代码问题，可在代码操作（编辑器中的灯泡图标）和编辑器上下文菜单（右键点击选中的代码）中使用。',
      IMPROVE:
        '提供优化建议（保留原功能），可在代码操作（编辑器中的灯泡图标）和编辑器上下文菜单（右键点击选中的代码）中使用。',
      ADD_TO_CONTEXT:
        '添加额外信息到对话，可在代码操作（编辑器中的灯泡图标）和编辑器上下文菜单（右键点击选中的代码）中使用。',
      TERMINAL_ADD_TO_CONTEXT: '将终端输出内容加入对话。可在终端右键菜单（右键点击选中的终端内容）中使用。',
      TERMINAL_FIX: '修复终端命令问题。可在终端右键菜单（右键点击选中的终端内容）中使用。',
      TERMINAL_EXPLAIN: '获取对终端命令及其输出的详细解释。可在终端右键菜单（右键点击选中的终端内容）中使用。',
      NEW_TASK: '控制开始新任务时的用户提示词。可在首页对话框中使用。',
    };
    return descriptions[type] || '';
  };

  return (
    <div className="support-prompts">
      <Select
        value={activeSupportOption}
        onChange={(value) => setActiveSupportOption(value as SupportPromptType)}
        style={{ width: '100%' }}
        options={Object.keys(supportPrompt.default).map((type) => ({
          value: type,
          label: (
            <div className="option-label">
              <span className="option-label-zh">{getSupportPromptTypeLabels(type).zh}</span>
              <span className="option-label-en">{getSupportPromptTypeLabels(type).en}</span>
            </div>
          ),
        }))}
      />

      <div className="text-[13px] text-vscode-descriptionForeground my-2 mb-4">
        {getSupportPromptTypeDescription(activeSupportOption)}
      </div>

      <div>
        <div className="flex justify-between items-center mb-1">
          <div className="font-bold">提示词</div>
          <Button
            icon={<span className="codicon codicon-discard" />}
            onClick={() => handleSupportReset(activeSupportOption)}
            title={`重置${getSupportPromptTypeLabel(activeSupportOption)}提示`}
          />
        </div>

        <TextArea
          value={sysPrompt}
          onChange={(e) => {
            const trimmedValue = e.target.value.trim();
            setSysPrompt(trimmedValue);
          }}
          onBlur={() => updateSupportPrompt(activeSupportOption, sysPrompt || undefined)}
          rows={6}
          className="resize-y w-full"
        />

        {activeSupportOption === 'ENHANCE' && (
          <>
            <div>
              <div className="text-vscode-foreground text-[13px] mb-5 mt-1.5"></div>
            </div>

            <div className="mt-4">
              <TextArea
                value={testPrompt}
                onChange={(e) => setTestPrompt(e.target.value)}
                placeholder="输入提示词以测试增强效果"
                rows={3}
                className="w-full resize-y"
              />
              <div className="mt-2 flex justify-start items-center gap-2">
                <Button onClick={handleTestEnhancement} disabled={isEnhancing}>
                  测试增强效果
                </Button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default SupportPrompts;
