import React from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'antd';
import { SystemPromptDialogProps } from './types';

const SystemPromptDialog: React.FC<SystemPromptDialogProps> = ({
  isOpen,
  onClose,
  selectedPromptTitle,
  selectedPromptContent,
}) => {
  return (
    <Modal
      title={selectedPromptTitle || '系统提示词'}
      open={isOpen}
      onCancel={onClose}
      width="80%"
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
      ]}
    >
      <pre className="p-2 whitespace-pre-wrap break-words font-mono text-vscode-editor-font-size text-vscode-editor-foreground bg-vscode-editor-background border border-vscode-editor-lineHighlightBorder rounded overflow-y-auto">
        {selectedPromptContent}
      </pre>
    </Modal>
  );
};

export default SystemPromptDialog;
