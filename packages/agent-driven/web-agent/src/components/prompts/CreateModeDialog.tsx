import React, { useState, useEffect, useCallback } from 'react';
import { Modal, Input, Radio, Checkbox, Button, Row, Col } from 'antd';
import { CreateModeDialogProps, ModeSource } from './types';

const { TextArea } = Input;
const { Group: RadioGroup, Button: RadioButton } = Radio;

const CreateModeDialog: React.FC<CreateModeDialogProps> = ({ isOpen, onClose, onCreateMode, availableGroups, isRemoteEnvironment = false }) => {
  const [newModeName, setNewModeName] = useState('');
  const [newModeSlug, setNewModeSlug] = useState('');
  const [newModeRoleDefinition, setNewModeRoleDefinition] = useState('');
  const [newModeWhenToUse, setNewModeWhenToUse] = useState('');
  const [newModeCustomInstructions, setNewModeCustomInstructions] = useState('');
  const [newModeGroups, setNewModeGroups] = useState<string[]>([]);
  const [newModeSource, setNewModeSource] = useState<ModeSource>('global');

  const [nameError, setNameError] = useState<string>('');
  const [slugError, setSlugError] = useState<string>('');
  const [roleDefinitionError, setRoleDefinitionError] = useState<string>('');
  const [groupsError, setGroupsError] = useState<string>('');
  const [groupNames] = useState<any>({
    read: '读取文件',
    edit: '编辑文件',
    browser: '浏览器',
    command: '运行命令',
    mcp: 'MCP服务',
  });

  const resetForm = useCallback(() => {
    setNewModeName('');
    setNewModeSlug('');
    setNewModeRoleDefinition('');
    setNewModeWhenToUse('');
    setNewModeCustomInstructions('');
    setNewModeGroups([]);
    // 在远程环境下默认选择项目特定模式，否则选择全局模式
    setNewModeSource(isRemoteEnvironment ? 'project' : 'global');
    setNameError('');
    setSlugError('');
    setRoleDefinitionError('');
    setGroupsError('');
  }, [isRemoteEnvironment]);

  useEffect(() => {
    if (isOpen) {
      resetForm();
    }
  }, [isOpen, resetForm]);



  const handleNameChange = (name: string) => {
    setNewModeName(name);
    setNewModeSlug(generateSlug(name));
  };

  const generateSlug = (name: string): string => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  };

  const handleCreateMode = () => {
    // Clear previous errors
    setNameError('');
    setSlugError('');
    setRoleDefinitionError('');
    setGroupsError('');

    // Perform validation
    let isValid = true;

    if (!newModeName.trim()) {
      setNameError('请输入模式名称');
      isValid = false;
    }

    if (!newModeSlug.trim()) {
      setSlugError('请输入有效的 agentId');
      isValid = false;
    }

    if (!newModeRoleDefinition.trim()) {
      setRoleDefinitionError('请输入角色定义');
      isValid = false;
    }

    if (newModeGroups.length === 0) {
      setGroupsError('请至少选择一个工具');
      isValid = false;
    }

    if (isValid) {
      onCreateMode({
        name: newModeName.trim(),
        agentId: newModeSlug.trim(),
        agentDefinition: newModeRoleDefinition.trim(),
        whenToUse: newModeWhenToUse.trim() || undefined,
        customInstructions: newModeCustomInstructions.trim() || undefined,
        groups: newModeGroups,
        source: newModeSource,
      });
      onClose();
    }
  };

  return (
    <Modal
      title="创建新模式"
      open={isOpen}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button key="create" type="primary" onClick={handleCreateMode}>
          创建模式
        </Button>,
      ]}
    >
      <div className="mb-4">
        <div className="font-bold mb-1">模式名称</div>
        <Input value={newModeName} onChange={(e) => handleNameChange(e.target.value)} className="w-full" />
        {nameError && <div className="text-red-500 text-sm mt-1">{nameError}</div>}
      </div>

      <div className="mb-4">
        <div className="font-bold mb-1">标识符</div>
        <Input value={newModeSlug} onChange={(e) => setNewModeSlug(e.target.value)} className="w-full" />
        <div className="text-xs text-gray-500 mt-1">用于标识模式的唯一标识符，仅支持（小写字母+数字+短横线）</div>
        {slugError && <div className="text-red-500 text-sm mt-1">{slugError}</div>}
      </div>

      <div className="mb-4">
        <div className="font-bold mb-1">保存位置</div>
        <RadioGroup value={newModeSource} onChange={(e) => setNewModeSource(e.target.value)}>
          {!isRemoteEnvironment && <RadioButton value="global">全局</RadioButton>}
          <RadioButton value="project">项目特定 (mode.json)</RadioButton>
        </RadioGroup>
        <div className="flex text-xs justify-start text-gray-500 mt-1 font-400">
          {!isRemoteEnvironment && <span>全局可用</span>}
          {!isRemoteEnvironment && <span className="ml-2">仅当前项目有效</span>}
          {isRemoteEnvironment && <span>远程环境下仅支持项目特定模式</span>}
        </div>
      </div>

      <div className="mb-4">
        <div className="font-bold mb-1">角色定义</div>
        <div className="font-400 text-xs mb-1">设定专业方向</div>
        <TextArea
          value={newModeRoleDefinition}
          onChange={(e) => setNewModeRoleDefinition(e.target.value)}
          rows={4}
          className="w-full"
        />
        {roleDefinitionError && <div className="text-red-500 text-sm mt-1">{roleDefinitionError}</div>}
      </div>
      <div className="mb-4">
        <div className="font-bold mb-1">使用场景（可选）</div>
        <div className="text-[13px] text-vscode-descriptionForeground mb-2">清晰描述此模式最适合的场景和任务类型</div>
        <TextArea
          value={newModeWhenToUse}
          onChange={(e) => {
            setNewModeWhenToUse((e.target as HTMLTextAreaElement).value);
          }}
          rows={4}
          className="w-full resize-y"
        />
      </div>
      <div className="mb-4">
        <div className="font-bold mb-1">可用工具</div>
        <div className="font-400 text-xs mb-1">选择可用工具</div>
        <Checkbox.Group
          value={newModeGroups}
          onChange={(checkedValues) => setNewModeGroups(checkedValues as string[])}
          className="w-full"
        >
          <Row gutter={[16, 8]}>
            {availableGroups.map((group) => (
              <Col span={8} key={group}>
                <Checkbox value={group}>{groupNames[group]}</Checkbox>
              </Col>
            ))}
          </Row>
        </Checkbox.Group>
        {groupsError && <div className="text-red-500 text-sm mt-1">{groupsError}</div>}
      </div>

      <div className="mb-4">
        <div className="font-bold mb-1">自定义指令（可选）</div>
        <div className="font-400 text-xs mb-1">设置专属规则</div>
        <TextArea
          value={newModeCustomInstructions}
          onChange={(e) => setNewModeCustomInstructions(e.target.value)}
          rows={4}
          className="w-full"
        />
      </div>
    </Modal>
  );
};

export default CreateModeDialog;
