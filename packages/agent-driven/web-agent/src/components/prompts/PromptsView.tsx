import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Tabs, Input, Button } from 'antd';
import { useExtensionState } from '../../context/ExtensionStateContext';
import { getAllModes, getRoleDefinition, getWhenToUse, TOOL_GROUPS, ToolGroup } from '../../utils/modes';
import { vscode } from '../../utils/vscode';
import CustomInstructions from './CustomInstructions';
import ModeSelector from './ModeSelector';
import SupportPrompts from './SupportPrompts';
import ToolsManager from './ToolsManager';
import SystemPromptDialog from './SystemPromptDialog';
import CreateModeDialog from './CreateModeDialog';
import './index.scss';
import { VSCodeButton } from '@vscode/webview-ui-toolkit/react';

const { TabPane } = Tabs;
const { TextArea } = Input;

const PromptsView = ({ onDone }: any) => {
  const {
    customModePrompts,
    customSupportPrompts,
    listApiConfigMeta,
    mode,
    customInstructions,
    setCustomInstructions,
    customModes,
    isRemoteEnvironment,
  } = useExtensionState();

  const [visualMode, setVisualMode] = useState(mode);
  const [isCreateModeDialogOpen, setIsCreateModeDialogOpen] = useState(false);
  const [isSystemPromptDialogOpen, setIsSystemPromptDialogOpen] = useState(false);
  const [selectedPromptContent, setSelectedPromptContent] = useState('');
  const [selectedPromptTitle, setSelectedPromptTitle] = useState('');
  const [isToolsEditMode, setIsToolsEditMode] = useState(false);
  const [showConfigMenu, setShowConfigMenu] = useState(false);
  const [isSystemPromptDisclosureOpen, setIsSystemPromptDisclosureOpen] = useState(false);

  const availableGroups = (Object.keys(TOOL_GROUPS) as ToolGroup[]).filter(
    (group) => !TOOL_GROUPS[group].alwaysAvailable,
  );

  const modes = useMemo(() => getAllModes(customModes), [customModes]);

  const updateAgentPrompt = useCallback(
    (mode: string, promptData: any) => {
      const existingPrompt = customModePrompts?.[mode] || {};
      const updatedPrompt = { ...existingPrompt, ...promptData };

      if (updatedPrompt.agentDefinition === getRoleDefinition(mode)) {
        delete updatedPrompt.agentDefinition;
      }
      if (updatedPrompt.whenToUse === getWhenToUse(mode)) {
        delete updatedPrompt.whenToUse;
      }

      vscode.postMessage({
        type: 'updatePrompt',
        promptMode: mode,
        customPrompt: updatedPrompt,
      });
    },
    [customModePrompts],
  );

  const updateCustomMode = useCallback((agentId: any, modeConfig: { source: string }) => {
    const source = modeConfig.source || 'global';
    vscode.postMessage({
      type: 'updateCustomMode',
      agentId,
      modeConfig: {
        ...modeConfig,
        source,
      },
    });
  }, []);

  const findModeBySlug = useCallback(
    (searchSlug: any) => {
      return customModes?.find((m) => m.agentId === searchSlug) || modes.find((m) => m.agentId === searchSlug);
    },
    [customModes, modes],
  );

  const switchMode = useCallback((agentId: any) => {
    vscode.postMessage({
      type: 'joycoder-set-mode',
      text: agentId,
    });
  }, []);

  const handleModeSwitch = useCallback(
    (modeConfig: any) => {
      if (modeConfig.agentId === visualMode) return;
      setVisualMode(modeConfig.agentId);
      switchMode(modeConfig.agentId);
      setIsToolsEditMode(false);
    },
    [visualMode, switchMode],
  );

  const getCurrentMode = useCallback(() => {
    return findModeBySlug(visualMode);
  }, [visualMode, findModeBySlug]);

  const handleAgentReset = useCallback(
    (modeSlug: string, type: string) => {
      const existingPrompt = customModePrompts?.[modeSlug];
      const updatedPrompt: any = { ...existingPrompt };
      delete updatedPrompt[type];
      vscode.postMessage({
        type: 'updatePrompt',
        promptMode: modeSlug,
        customPrompt: updatedPrompt,
      });
    },
    [customModePrompts],
  );

  const updateSupportPrompt = useCallback((type: any, value: any) => {
    vscode.postMessage({
      type: 'updateSupportPrompt',
      values: {
        [type]: value,
      },
    });
  }, []);

  const handleSupportReset = useCallback((type: any) => {
    vscode.postMessage({
      type: 'resetSupportPrompt',
      text: type,
    });
  }, []);

  const generateSlug = useCallback((name: string, attempt = 0) => {
    const baseSlug = name
      .toLowerCase()
      .replace(/[^a-z0-9-]+/g, '-')
      .replace(/^-+|-+$/g, '');
    return attempt === 0 ? baseSlug : `${baseSlug}-${attempt}`;
  }, []);

  const isNameOrSlugTaken = useCallback(
    (name: any, agentId: any) => {
      return modes.some((m) => m.agentId === agentId || m.name === name);
    },
    [modes],
  );

  const openCreateModeDialog = useCallback(() => {
    const baseNamePrefix = '新建自定义模式';
    let attempt = 0;
    let name = baseNamePrefix;
    let agentId = generateSlug(name);
    while (isNameOrSlugTaken(name, agentId)) {
      attempt++;
      name = `${baseNamePrefix} ${attempt + 1}`;
      agentId = generateSlug(name);
    }
    setIsCreateModeDialogOpen(true);
  }, [generateSlug, isNameOrSlugTaken]);

  useEffect(() => {
    const handler = (event: { data: any }) => {
      const message = event.data;
      if (message.type === 'systemPrompt') {
        if (message.text) {
          setSelectedPromptContent(message.text);
          setSelectedPromptTitle(`系统提示词 (${message.mode} 模式)`);
          setIsSystemPromptDialogOpen(true);
        }
      }
      window.addEventListener('message', handler);
    };
    return () => window.removeEventListener('message', handler);
  }, []);

  useEffect(() => {
    const handleClickOutside = () => {
      if (showConfigMenu) {
        setShowConfigMenu(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [showConfigMenu]);

  return (
    <div className="prompts-view joycoder-prompt fixed inset-0 flex flex-col">
      <div className="header px-5 py-2.5 border-b border-[#ffffff1f] border-vscode-panel-border flex justify-between items-center">
        <h3 className="text-vscode-foreground m-0">模型提示词设置</h3>
        <VSCodeButton onClick={onDone}>完成</VSCodeButton>
      </div>
      <div className="mx-3 my-0">
        <Tabs defaultActiveKey="mode">
          <TabPane tab="模式配置" key="mode">
            <ModeSelector
              visualMode={visualMode}
              setVisualMode={setVisualMode}
              modes={modes}
              customModes={customModes}
              switchMode={switchMode}
              setIsToolsEditMode={setIsToolsEditMode}
              updateCustomMode={updateCustomMode}
              handleModeSwitch={handleModeSwitch}
              openCreateModeDialog={openCreateModeDialog}
            />

            <ToolsManager
              visualMode={visualMode}
              getCurrentMode={getCurrentMode}
              customModes={customModes}
              updateCustomMode={updateCustomMode}
              isToolsEditMode={isToolsEditMode}
              setIsToolsEditMode={setIsToolsEditMode}
            />
            <CustomInstructions
              visualMode={visualMode}
              customModes={customModes}
              updateAgentPrompt={updateAgentPrompt}
              updateCustomMode={updateCustomMode}
              handleAgentReset={handleAgentReset}
              getCurrentMode={getCurrentMode}
              isToolsEditMode={isToolsEditMode}
            />

            {/* 全局自定义指令 */}
            <div className="pb-5 mb-4 border-vscode-input-border mt-1">
              <h3 className="text-vscode-foreground mb-3">全局自定义指令</h3>
              <div className="text-sm text-vscode-descriptionForeground mb-2">
                全局自定义指令将应用于所有模式，可以用来设置您的偏好和要求。
              </div>
              <TextArea
                value={customInstructions || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  setCustomInstructions(value || undefined);
                  vscode.postMessage({
                    type: 'customInstructions',
                    text: value.trim() || undefined,
                  });
                }}
                rows={4}
                className="w-full resize-y"
              />
              <div className="text-xs text-vscode-descriptionForeground mt-1.5">
                您也可以通过创建文件{' '}
                <span
                  className="text-vscode-textLink-foreground cursor-pointer underline"
                  onClick={() =>
                    vscode.postMessage({
                      type: 'openFile',
                      text: './.joycode/rules/rules.mdc',
                      values: {
                        create: true,
                        content: '',
                      },
                    })
                  }
                >
                  ./.joycode/rules/rules.mdc
                </span>{' '}
                或者./.JoyCodeRules来加载自定义指令。
              </div>
            </div>
          </TabPane>
          {/* <TabPane tab="增强提示词" key="prompts">
            <SupportPrompts
              customSupportPrompts={customSupportPrompts}
              updateSupportPrompt={updateSupportPrompt}
              handleSupportReset={handleSupportReset}
              listApiConfigMeta={listApiConfigMeta}
            />
          </TabPane> */}
        </Tabs>
      </div>

      <CreateModeDialog
        isOpen={isCreateModeDialogOpen}
        onClose={() => setIsCreateModeDialogOpen(false)}
        availableGroups={availableGroups}
        isRemoteEnvironment={isRemoteEnvironment}
        onCreateMode={(newMode) => {
          updateCustomMode(newMode.agentId, newMode);
          switchMode(newMode.agentId);
          setIsCreateModeDialogOpen(false);
        }}
      />

      <SystemPromptDialog
        isOpen={isSystemPromptDialogOpen}
        onClose={() => setIsSystemPromptDialogOpen(false)}
        selectedPromptTitle={selectedPromptTitle}
        selectedPromptContent={selectedPromptContent}
      />
    </div>
  );
};

export default PromptsView;
