import { convertConflictToDiff } from './convertConflictToDiff';

/**
 * diff统计函数：对diff段进行增建行统计，对于SEARCH_REPLACE格式，转换为普通diff段
 * - 返回未被配对的新增（added）和删除（removed）行数
 * - 支持基于成功状态的过滤统计
 */
export function getDiffStats(diffText: string | undefined, progressStatus?: any): { added: number; removed: number } {
  if (typeof diffText !== 'string') return { added: 0, removed: 0 };

  // 检查是否有成功状态信息，如果有则只统计成功的部分
  if (progressStatus?.text && progressStatus.text.includes('/')) {
    const [successCount, totalCount] = progressStatus.text.split('/').map(Number);
    if (successCount < totalCount) {
      // 有失败的部分，需要过滤统计
      return { added: 0, removed: 0 };
    }
  }

  // 正常统计逻辑
  return getDiffStatsComplete(diffText);
}

/**
 * 完整的diff统计逻辑
 */
function getDiffStatsComplete(diffText: string): { added: number; removed: number } {
  // 若包含冲突标记，先转换为常规 diff 格式
  if (
    diffText
      .split('\n')
      .some((line) => line.startsWith('<<<<<<<') || line.startsWith('=======') || line.startsWith('>>>>>>>'))
  ) {
    diffText = convertConflictToDiff(diffText);
  }

  const lines = diffText.split('\n').filter((line) => line.trim() !== '');

  const addedLines: Array<{ index: number; content: string; paired: boolean }> = [];
  const removedLines: Array<{ index: number; content: string; paired: boolean }> = [];

  // 解析所有行，加入增减行数组
  let skipNextDashLine = false;
  let separator = '--------';

  for (let index = 0; index < lines.length; index++) {
    const line = lines[index];

    if (line.startsWith('+')) {
      addedLines.push({ index, content: line.substring(1), paired: false });
    } else if (line.startsWith('-')) {
      if (line.startsWith('-:start')) {
        const nextLine = index + 1 < lines.length ? lines[index + 1] : '';
        const nextNextLine = index + 2 < lines.length ? lines[index + 2] : '';
        // 处理diff段转换带来的分隔符
        if (nextLine === separator || nextNextLine === separator) {
          skipNextDashLine = true;
        }
      } else if (line === separator && skipNextDashLine) {
        skipNextDashLine = false;
      } else {
        removedLines.push({ index, content: line.substring(1), paired: false });
      }
    }
  }

  // diff配对逻辑
  const removedMap = new Map<string, Array<{ index: number; content: string; paired: boolean }>>();
  for (const removedLine of removedLines) {
    if (!removedLine.paired) {
      const key = removedLine.content.trim();
      if (!removedMap.has(key)) removedMap.set(key, []);
      removedMap.get(key)!.push(removedLine);
    }
  }

  for (const addedLine of addedLines) {
    if (addedLine.paired) continue;
    const candidates = removedMap.get(addedLine.content.trim());
    if (!candidates) continue;
    for (const removedLine of candidates) {
      if (removedLine.paired) continue;
      const startIndex = Math.min(addedLine.index, removedLine.index);
      const endIndex = Math.max(addedLine.index, removedLine.index);

      const canPair = lines
        .slice(startIndex + 1, endIndex)
        .every(
          (betweenLine) =>
            betweenLine.startsWith('-') ||
            betweenLine.startsWith('+') ||
            betweenLine.startsWith('/') ||
            betweenLine.startsWith('\\')
        );

      if (canPair) {
        addedLine.paired = true;
        removedLine.paired = true;
        break;
      }
    }
  }

  // 统计剩余的的行数
  const added = addedLines.filter((line) => !line.paired).length;
  const removed = removedLines.filter((line) => !line.paired).length;

  return { added, removed };
}
