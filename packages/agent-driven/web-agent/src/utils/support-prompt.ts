// Support prompts
type PromptParams = Record<string, string | any[]>;

const generateDiagnosticText = (diagnostics?: any[]) => {
  if (!diagnostics?.length) return '';
  return `\nCurrent problems detected:\n${diagnostics
    .map((d) => `- [${d.source || 'Error'}] ${d.message}${d.code ? ` (${d.code})` : ''}`)
    .join('\n')}`;
};

export const createPrompt = (template: string, params: PromptParams): string => {
  return template.replace(/\${(.*?)}/g, (_, key) => {
    if (key === 'diagnosticText') {
      return generateDiagnosticText(params['diagnostics'] as any[]);
    } else if (params.hasOwnProperty(key)) {
      // Ensure the value is treated as a string for replacement
      const value = params[key];
      if (typeof value === 'string') {
        return value;
      } else {
        // Convert non-string values to string for replacement
        return String(value);
      }
    } else {
      // If the placeholder key is not in params, replace with empty string
      return '';
    }
  });
};

interface SupportPromptConfig {
  template: string;
}

const supportPromptConfigs: Record<string, SupportPromptConfig> = {
  ENHANCE: {
    template: `You are a professional prompt optimization expert. Your task is to optimize the original prompt provided by the user to guide AI in generating more precise, useful, and expected responses.

Original prompt:
\${userInput}

When generating prompts, please follow these guidelines:

- Deeply understand the main objectives, requirements, constraints, and expected outputs of the task
- Ensure reasoning steps come before conclusions; conclusions or classifications should always appear last
- Use clear, specific language, avoiding redundant instructions or vague descriptions
- Add necessary contextual information to help AI better understand task requirements
- Use markdown formatting appropriately to enhance readability
- Clearly specify the most suitable output format, including length and structure
- Add high-quality examples when necessary, without being overly redundant
- For larger goals, analyze and break down into multiple detailed subtasks, using markdown tree-style TODO lists to record task planning and prevent deviation from objectives
- For complex computational logic, add appropriate formulas, pseudocode, or mermaid diagrams to assist analysis and understanding

Note:
Do not add any additional explanations or comments in your response, output only the complete prompt content in markdown format.
`,
  },
  EXPLAIN: {
    template: `Explain the following code from file path \${filePath}:\${startLine}-\${endLine}
\${userInput}

\`\`\`
\${selectedText}
\`\`\`

Please provide a clear and concise explanation of what this code does, including:
1. The purpose and functionality
2. Key components and their interactions
3. Important patterns or techniques used`,
  },
  FIX: {
    template: `Fix any issues in the following code from file path \${filePath}:\${startLine}-\${endLine}
\${diagnosticText}
\${userInput}

\`\`\`
\${selectedText}
\`\`\`

Please:
1. Address all detected problems listed above (if any)
2. Identify any other potential bugs or issues
3. Provide corrected code
4. Explain what was fixed and why`,
  },
  IMPROVE: {
    template: `Improve the following code from file path \${filePath}:\${startLine}-\${endLine}
\${userInput}

\`\`\`
\${selectedText}
\`\`\`

Please suggest improvements for:
1. Code readability and maintainability
2. Performance optimization
3. Best practices and patterns
4. Error handling and edge cases

Provide the improved code along with explanations for each enhancement.`,
  },
  ADD_TO_CONTEXT: {
    template: `\${filePath}:\${startLine}-\${endLine}
\`\`\`
\${selectedText}
\`\`\``,
  },
  TERMINAL_ADD_TO_CONTEXT: {
    template: `\${userInput}
Terminal output:
\`\`\`
\${terminalContent}
\`\`\``,
  },
  TERMINAL_FIX: {
    template: `\${userInput}
Fix this terminal command:
\`\`\`
\${terminalContent}
\`\`\`

Please:
1. Identify any issues in the command
2. Provide the corrected command
3. Explain what was fixed and why`,
  },
  TERMINAL_EXPLAIN: {
    template: `\${userInput}
Explain this terminal command:
\`\`\`
\${terminalContent}
\`\`\`

Please provide:
1. What the command does
2. Explanation of each part/flag
3. Expected output and behavior`,
  },
  NEW_TASK: {
    template: `\${userInput}`,
  },
} as const;

type SupportPromptType = keyof typeof supportPromptConfigs;

export const supportPrompt = {
  default: Object.fromEntries(Object.entries(supportPromptConfigs).map(([key, config]) => [key, config.template])),
  get: (customSupportPrompts: Record<string, any> | undefined, type: SupportPromptType): string => {
    return customSupportPrompts?.[type] ?? supportPromptConfigs[type].template;
  },
  create: (type: SupportPromptType, params: PromptParams, customSupportPrompts?: Record<string, any>): string => {
    const template = supportPrompt.get(customSupportPrompts, type);
    return createPrompt(template, params);
  },
} as const;

export type { SupportPromptType };

export type CustomSupportPrompts = {
  [key: string]: string | undefined;
};
