import { useState } from "react";
import { vscode } from "../../utils/vscode";
import CustomTooltip from '../../components/common/CustomTooltip';


//  AI 资源
const OpenAIResourcesIcon = () => {
    const [isShow, setIsShow] = useState(false);

    // 在点击事件或其他事件处理函数中
    const handleClick = () => {
        setIsShow(!isShow)
        vscode.postMessage({
            type: 'openAiResources',
            bool: !isShow
        });
    };

    return (
        <CustomTooltip
            color={'var(--vscode-button-secondaryBackground, #72747C)'}
            title={'AI 资源'}
            placement="top"
        >
            <i className="icon iconfont icon-ziyuanchajian" onClick={handleClick} />
        </CustomTooltip>
    )
}

export default OpenAIResourcesIcon;
