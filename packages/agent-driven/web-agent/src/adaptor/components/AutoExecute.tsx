import { Switch } from 'antd';
import { useExtensionState } from "../../context/ExtensionStateContext";
import CustomTooltip from '../../components/common/CustomTooltip';

//  自动执行组件
const AutoExecuteTool = () => {
    const { autoExecute, setAutoExecute } = useExtensionState();


    return (
        <CustomTooltip
            color={'var(--vscode-button-secondaryBackground, #72747C)'}
            title={!autoExecute ? 'Auto OFF: JoyCode will ask for approval for making changes.' : 'Auto ON: JoyCode will make changes on your behalf.'}
            placement="top"
        >
            <div className="joycoder-toolbar-switch-group">
                <Switch
                    size="small"
                    checkedChildren="Auto"
                    unCheckedChildren="Auto"
                    checked={autoExecute}
                    onChange={setAutoExecute}
                />
            </div>
        </CustomTooltip>
    )
}

export default AutoExecuteTool;
