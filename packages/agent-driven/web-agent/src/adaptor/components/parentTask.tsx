import { forwardRef, memo } from 'react';
import Loading from '../../components/common/loading';

const ParentTaskHeader = forwardRef(({ taskLabel }: any) => {
  return (
    <>
      {taskLabel && (
        <div className="joycoder-parent-task">
          <Loading />
          <div className="joycoder-parent-task-header">JoyCode正在执行：{taskLabel}</div>
        </div>
      )}
    </>
  );
});
export default memo(ParentTaskHeader);
