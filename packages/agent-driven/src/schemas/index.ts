// Updates to this file will automatically propgate to src/exports/types.ts
// via a pre-commit hook. If you want to update the types before committing you
// can run `npm run generate-types`.

import { JoyCoderAsks, JoyCoderSays } from '../shared/ExtensionMessage';
import { z } from 'zod';
import { Keys } from '../utils/type-fu';
import {
  apiConfigMetaSchema,
  customModePromptsSchema,
  customSupportPromptsSchema,
  modeConfigSchema,
  providerNamesSchema,
} from '../../web-agent/src/utils/modes';
import { experiments } from '../shared/experiments';

/**
 * Language
 */

export const languages = ['en', 'zh-CN'] as const;

export const languagesSchema = z.enum(languages);

export type Language = z.infer<typeof languagesSchema>;

export const isLanguage = (value: string): value is Language => languages.includes(value as Language);

/**
 * ReasoningEffort
 */

export const reasoningEfforts = ['low', 'medium', 'high'] as const;

export const reasoningEffortsSchema = z.enum(reasoningEfforts);

export type ReasoningEffort = z.infer<typeof reasoningEffortsSchema>;

/**
 * ModelInfo
 */

export const modelInfoSchema = z.object({
  maxTokens: z.number().nullish(),
  maxThinkingTokens: z.number().nullish(),
  contextWindow: z.number(),
  supportsImages: z.boolean().optional(),
  supportsComputerUse: z.boolean().optional(),
  supportsPromptCache: z.boolean(),
  isPromptCacheOptional: z.boolean().optional(),
  inputPrice: z.number().optional(),
  outputPrice: z.number().optional(),
  cacheWritesPrice: z.number().optional(),
  cacheReadsPrice: z.number().optional(),
  description: z.string().optional(),
  reasoningEffort: reasoningEffortsSchema.optional(),
  thinking: z.boolean().optional(),
  minTokensPerCachePoint: z.number().optional(),
  maxCachePoints: z.number().optional(),
  cachableFields: z.array(z.string()).optional(),
  tiers: z
    .array(
      z.object({
        contextWindow: z.number(),
        inputPrice: z.number().optional(),
        outputPrice: z.number().optional(),
        cacheWritesPrice: z.number().optional(),
        cacheReadsPrice: z.number().optional(),
      })
    )
    .optional(),
});

export type ModelInfo = z.infer<typeof modelInfoSchema>;

/**
 * HistoryItem
 */

export const historyItemSchema = z.object({
  id: z.string(),
  number: z.number(),
  ts: z.number(),
  task: z.string(),
  tokensIn: z.number(),
  tokensOut: z.number(),
  cacheWrites: z.number().optional(),
  cacheReads: z.number().optional(),
  totalCost: z.number(),
  size: z.number().optional(),
  workspace: z.string().optional(),
});

export type HistoryItem = z.infer<typeof historyItemSchema>;
/**
 * ExperimentId
 */

export const experimentIds = ['powerSteering'] as const;

export const experimentIdsSchema = z.enum(experimentIds);

export type ExperimentId = z.infer<typeof experimentIdsSchema>;

/**
 * Experiments
 */

const experimentsSchema = z.object({
  powerSteering: z.boolean(),
});

export type Experiments = z.infer<typeof experimentsSchema>;

/**
 * ProviderSettings
 */

export const providerSettingsSchema = z.object({
  apiProvider: providerNamesSchema.optional(),
  // OpenAI
  openAiBaseUrl: z.string().optional(),
  openAiApiKey: z.string().optional(),
  openAiLegacyFormat: z.boolean().optional(),
  openAiR1FormatEnabled: z.boolean().optional(),
  openAiModelId: z.string().optional(),
  openAiCustomModelInfo: modelInfoSchema.nullish(),
  openAiUseAzure: z.boolean().optional(),
  azureApiVersion: z.string().optional(),
  openAiStreamingEnabled: z.boolean().optional(),
  enableReasoningEffort: z.boolean().optional(),
  openAiHostHeader: z.string().optional(), // Keep temporarily for backward compatibility during migration
  openAiHeaders: z.record(z.string(), z.string()).optional(),
});

export type ProviderSettings = z.infer<typeof providerSettingsSchema>;

type ProviderSettingsRecord = Record<Keys<ProviderSettings>, undefined>;

const providerSettingsRecord: ProviderSettingsRecord = {
  apiProvider: undefined,
  // OpenAI
  openAiBaseUrl: undefined,
  openAiApiKey: undefined,
  openAiLegacyFormat: undefined,
  openAiR1FormatEnabled: undefined,
  openAiModelId: undefined,
  openAiCustomModelInfo: undefined,
  openAiUseAzure: undefined,
  azureApiVersion: undefined,
  openAiStreamingEnabled: undefined,
  enableReasoningEffort: undefined,
  openAiHostHeader: undefined, // Keep temporarily for backward compatibility during migration
  openAiHeaders: undefined,
};

export const PROVIDER_SETTINGS_KEYS = Object.keys(providerSettingsRecord) as Keys<ProviderSettings>[];

/**
 * GlobalSettings
 */

export const globalSettingsSchema = z.object({
  currentApiConfigName: z.string().optional(),
  listApiConfigMeta: z.array(apiConfigMetaSchema).optional(),
  pinnedApiConfigs: z.record(z.string(), z.boolean()).optional(),

  lastShownAnnouncementId: z.string().optional(),
  customInstructions: z.string().optional(),
  taskHistory: z.array(historyItemSchema).optional(),
  taskHistoryRemote: z.array(historyItemSchema).optional(),

  autoApprovalEnabled: z.boolean().optional(),
  alwaysAllowReadOnly: z.boolean().optional(),
  alwaysAllowReadOnlyOutsideWorkspace: z.boolean().optional(),
  alwaysAllowWrite: z.boolean().optional(),
  alwaysAllowWriteOutsideWorkspace: z.boolean().optional(),
  writeDelayMs: z.number().optional(),
  alwaysAllowBrowser: z.boolean().optional(),
  alwaysApproveResubmit: z.boolean().optional(),
  requestDelaySeconds: z.number().optional(),
  alwaysAllowMcp: z.boolean().optional(),
  alwaysAllowModeSwitch: z.boolean().optional(),
  alwaysAllowSubtasks: z.boolean().optional(),
  alwaysAllowExecute: z.boolean().optional(),
  allowedCommands: z.array(z.string()).optional(),

  browserToolEnabled: z.boolean().optional(),
  browserViewportSize: z.string().optional(),
  screenshotQuality: z.number().optional(),
  remoteBrowserEnabled: z.boolean().optional(),
  remoteBrowserHost: z.string().optional(),
  cachedChromeHostUrl: z.string().optional(),

  enableCheckpoints: z.boolean().optional(),

  ttsEnabled: z.boolean().optional(),
  ttsSpeed: z.number().optional(),
  soundEnabled: z.boolean().optional(),
  soundVolume: z.number().optional(),

  maxOpenTabsContext: z.number().optional(),
  maxWorkspaceFiles: z.number().optional(),
  showRooIgnoredFiles: z.boolean().optional(),
  maxReadFileLine: z.number().optional(),

  terminalOutputLineLimit: z.number().optional(),
  terminalShellIntegrationTimeout: z.number().optional(),
  terminalShellIntegrationDisabled: z.boolean().optional(),
  terminalCommandDelay: z.number().optional(),
  terminalPowershellCounter: z.boolean().optional(),
  terminalZshClearEolMark: z.boolean().optional(),
  terminalZshOhMy: z.boolean().optional(),
  terminalZshP10k: z.boolean().optional(),
  terminalZdotdir: z.boolean().optional(),
  terminalCompressProgressBar: z.boolean().optional(),

  rateLimitSeconds: z.number().optional(),
  diffEnabled: z.boolean().optional(),
  fuzzyMatchThreshold: z.number().optional(),
  experiments: experimentsSchema.optional(),

  language: languagesSchema.optional(),

  mcpEnabled: z.boolean().optional(),
  enableMcpServerCreation: z.boolean().optional(),

  mode: z.string().optional(),
  modeApiConfigs: z.record(z.string(), z.string()).optional(),
  customModes: z.array(modeConfigSchema).optional(),
  customModePrompts: customModePromptsSchema.optional(),
  customSupportPrompts: customSupportPromptsSchema.optional(),
  enhancementApiConfigId: z.string().optional(),
  historyPreviewCollapsed: z.boolean().optional(),
});

export type GlobalSettings = z.infer<typeof globalSettingsSchema>;

type GlobalSettingsRecord = Record<Keys<GlobalSettings>, undefined>;

const globalSettingsRecord: GlobalSettingsRecord = {
  currentApiConfigName: undefined,
  listApiConfigMeta: undefined,
  pinnedApiConfigs: undefined,

  lastShownAnnouncementId: undefined,
  customInstructions: undefined,
  taskHistory: undefined,
  taskHistoryRemote: undefined,

  autoApprovalEnabled: undefined,
  alwaysAllowReadOnly: undefined,
  alwaysAllowReadOnlyOutsideWorkspace: undefined,
  alwaysAllowWrite: undefined,
  alwaysAllowWriteOutsideWorkspace: undefined,
  writeDelayMs: undefined,
  alwaysAllowBrowser: undefined,
  alwaysApproveResubmit: undefined,
  requestDelaySeconds: undefined,
  alwaysAllowMcp: undefined,
  alwaysAllowModeSwitch: undefined,
  alwaysAllowSubtasks: undefined,
  alwaysAllowExecute: undefined,
  allowedCommands: undefined,

  browserToolEnabled: undefined,
  browserViewportSize: undefined,
  screenshotQuality: undefined,
  remoteBrowserEnabled: undefined,
  remoteBrowserHost: undefined,

  enableCheckpoints: undefined,

  ttsEnabled: undefined,
  ttsSpeed: undefined,
  soundEnabled: undefined,
  soundVolume: undefined,

  maxOpenTabsContext: undefined,
  maxWorkspaceFiles: undefined,
  showRooIgnoredFiles: undefined,
  maxReadFileLine: undefined,

  terminalOutputLineLimit: undefined,
  terminalShellIntegrationTimeout: undefined,
  terminalShellIntegrationDisabled: undefined,
  terminalCommandDelay: undefined,
  terminalPowershellCounter: undefined,
  terminalZshClearEolMark: undefined,
  terminalZshOhMy: undefined,
  terminalZshP10k: undefined,
  terminalZdotdir: undefined,
  terminalCompressProgressBar: undefined,

  rateLimitSeconds: undefined,
  diffEnabled: undefined,
  fuzzyMatchThreshold: undefined,
  experiments: undefined,

  language: undefined,

  mcpEnabled: undefined,
  enableMcpServerCreation: undefined,

  mode: undefined,
  modeApiConfigs: undefined,
  customModes: undefined,
  customModePrompts: undefined,
  customSupportPrompts: undefined,
  enhancementApiConfigId: undefined,
  cachedChromeHostUrl: undefined,
  historyPreviewCollapsed: undefined,
};

export const GLOBAL_SETTINGS_KEYS = Object.keys(globalSettingsRecord) as Keys<GlobalSettings>[];

/**
 * JoyCoderSettings
 */

export const JoyCoderSettingsSchema = providerSettingsSchema.merge(globalSettingsSchema);

export type JoyCoderSettings = GlobalSettings & ProviderSettings;

/**
 * SecretState
 */

export type SecretState = Pick<ProviderSettings, 'apiProvider'>;

type SecretStateRecord = Record<Keys<SecretState>, undefined>;

const secretStateRecord: SecretStateRecord = {
  apiProvider: undefined,
};

export const SECRET_STATE_KEYS = Object.keys(secretStateRecord) as Keys<SecretState>[];

export const isSecretStateKey = (key: string): key is Keys<SecretState> =>
  SECRET_STATE_KEYS.includes(key as Keys<SecretState>);

/**
 * GlobalState
 */

export type GlobalState = Omit<JoyCoderSettings, Keys<SecretState>>;

export const GLOBAL_STATE_KEYS = [...GLOBAL_SETTINGS_KEYS, ...PROVIDER_SETTINGS_KEYS].filter(
  (key: Keys<JoyCoderSettings>) => !SECRET_STATE_KEYS.includes(key as Keys<SecretState>)
) as Keys<GlobalState>[];

export const isGlobalStateKey = (key: string): key is Keys<GlobalState> =>
  GLOBAL_STATE_KEYS.includes(key as Keys<GlobalState>);
export const JoyCoderAskSchema = z.enum(JoyCoderAsks);

// export type JoyCoderAsk = z.infer<typeof JoyCoderAskSchema>;

export const JoyCoderSaySchema = z.enum(JoyCoderSays);

// export type JoyCoderSay = z.infer<typeof JoyCoderSaySchema>;

/**
 * ToolProgressStatus
 */

export const toolProgressStatusSchema = z.object({
  icon: z.string().optional(),
  text: z.string().optional(),
});

export type ToolProgressStatus = z.infer<typeof toolProgressStatusSchema>;

/**
 * ContextCondense
 */

export const contextCondenseSchema = z.object({
  cost: z.number(),
  prevContextTokens: z.number(),
  newContextTokens: z.number(),
  summary: z.string(),
});

export type ContextCondense = z.infer<typeof contextCondenseSchema>;

/**
 * JoyCoderMessage
 */

export const JoyCoderMessageSchema = z.object({
  ts: z.number(),
  type: z.union([z.literal('ask'), z.literal('say')]),
  ask: JoyCoderAskSchema.optional(),
  say: JoyCoderSaySchema.optional(),
  text: z.string().optional(),
  images: z.array(z.string()).optional(),
  partial: z.boolean().optional(),
  reasoning: z.string().optional(),
  conversationHistoryIndex: z.number().optional(),
  checkpoint: z.record(z.string(), z.unknown()).optional(),
  progressStatus: toolProgressStatusSchema.optional(),
  modeInfo: z
    .object({
      agentId: z.string(),
      name: z.string(),
      avatar: z.string().optional(),
    })
    .optional(),
  isUserMessage: z.boolean().optional(),
});

/**
 * TokenUsage
 */

export const tokenUsageSchema = z.object({
  totalTokensIn: z.number(),
  totalTokensOut: z.number(),
  totalCacheWrites: z.number().optional(),
  totalCacheReads: z.number().optional(),
  totalCost: z.number(),
  contextTokens: z.number(),
});

export type TokenUsage = z.infer<typeof tokenUsageSchema>;

/**
 * ToolName
 */

export const toolNames = [
  'use_command',
  'use_read_file',
  'use_write_file',
  'apply_diff',
  'insert_content',
  'use_search_and_replace',
  'use_search_files',
  'use_list_files',
  'use_definition_names',
  'use_browser',
  'use_mcp_tools',
  'get_mcp_resource',
  'get_user_question',
  'attempt_task_done',
  'switch_mode',
  'new_task',
  'fetch_instructions',
] as const;

export const toolNamesSchema = z.enum(toolNames);

export type ToolName = z.infer<typeof toolNamesSchema>;

/**
 * ToolUsage
 */

export const toolUsageSchema = z.record(
  toolNamesSchema,
  z.object({
    attempts: z.number(),
    failures: z.number(),
  })
);

export type ToolUsage = z.infer<typeof toolUsageSchema>;

/**
 * JoyCoderEvent
 */

export enum JoyCoderEventName {
  Message = 'message',
  TaskCreated = 'taskCreated',
  TaskStarted = 'taskStarted',
  TaskModeSwitched = 'taskModeSwitched',
  TaskPaused = 'taskPaused',
  TaskUnpaused = 'taskUnpaused',
  TaskAskResponded = 'taskAskResponded',
  TaskAborted = 'taskAborted',
  TaskSpawned = 'taskSpawned',
  TaskCompleted = 'taskCompleted',
  TaskTokenUsageUpdated = 'taskTokenUsageUpdated',
  TaskToolFailed = 'taskToolFailed',
}

export const JoyCoderEventsSchema = z.object({
  [JoyCoderEventName.Message]: z.tuple([
    z.object({
      taskId: z.string(),
      action: z.union([z.literal('created'), z.literal('updated')]),
      message: JoyCoderMessageSchema,
    }),
  ]),
  [JoyCoderEventName.TaskCreated]: z.tuple([z.string()]),
  [JoyCoderEventName.TaskStarted]: z.tuple([z.string()]),
  [JoyCoderEventName.TaskModeSwitched]: z.tuple([z.string(), z.string()]),
  [JoyCoderEventName.TaskPaused]: z.tuple([z.string()]),
  [JoyCoderEventName.TaskUnpaused]: z.tuple([z.string()]),
  [JoyCoderEventName.TaskAskResponded]: z.tuple([z.string()]),
  [JoyCoderEventName.TaskAborted]: z.tuple([z.string()]),
  [JoyCoderEventName.TaskSpawned]: z.tuple([z.string(), z.string()]),
  [JoyCoderEventName.TaskCompleted]: z.tuple([z.string(), tokenUsageSchema, toolUsageSchema]),
  [JoyCoderEventName.TaskTokenUsageUpdated]: z.tuple([z.string(), tokenUsageSchema]),
  [JoyCoderEventName.TaskToolFailed]: z.tuple([z.string(), toolNamesSchema, z.string()]),
});

export type JoyCoderEvents = z.infer<typeof JoyCoderEventsSchema>;

/**
 * TypeDefinition
 */

export type TypeDefinition = {
  schema: z.ZodTypeAny;
  identifier: string;
};

export const typeDefinitions: TypeDefinition[] = [
  { schema: providerSettingsSchema, identifier: 'ProviderSettings' },
  { schema: globalSettingsSchema, identifier: 'GlobalSettings' },
  { schema: JoyCoderMessageSchema, identifier: 'JoyCoderMessage' },
  { schema: tokenUsageSchema, identifier: 'TokenUsage' },
  { schema: JoyCoderEventsSchema, identifier: 'JoyCoderEvents' },
];

// Also export as default for ESM compatibility
export default { typeDefinitions };
export { experiments };
