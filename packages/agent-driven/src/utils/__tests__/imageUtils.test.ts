import fs from 'fs';
import path from 'path';
import { ImageUtils } from '../imageUtils';

describe('ImageUtils', () => {
  const testImageDir = path.join(__dirname, 'test-images');

  beforeAll(() => {
    // 创建测试图片目录
    if (!fs.existsSync(testImageDir)) {
      fs.mkdirSync(testImageDir, { recursive: true });
    }
  });

  afterAll(() => {
    // 清理测试图片目录
    if (fs.existsSync(testImageDir)) {
      fs.rmSync(testImageDir, { recursive: true, force: true });
    }
  });

  describe('convertImageToBase64', () => {
    it('should return empty string for non-existent file', async () => {
      const result = await ImageUtils.convertImageToBase64('/non/existent/file.jpg');
      expect(result).toBe('');
    });

    it('should return empty string for invalid file', async () => {
      // 创建一个非图片文件
      const textFile = path.join(testImageDir, 'test.txt');
      fs.writeFileSync(textFile, 'This is not an image');

      const result = await ImageUtils.convertImageToBase64(textFile);
      expect(result).toBe('');
    });

    it('should return empty string for files larger than 2MB', async () => {
      // 创建一个大于2MB的假图片文件（实际上是文本文件，但我们可以测试大小检查）
      const largeFile = path.join(testImageDir, 'large.jpg');
      const largeContent = 'A'.repeat(3 * 1024 * 1024); // 3MB
      fs.writeFileSync(largeFile, largeContent);

      const result = await ImageUtils.convertImageToBase64(largeFile);
      expect(result).toBe('');
    });
  });

  describe('convertImageToBase64Sync', () => {
    it('should return empty string for non-existent file', () => {
      const result = ImageUtils.convertImageToBase64Sync('/non/existent/file.jpg');
      expect(result).toBe('');
    });

    it('should return empty string for files larger than 2MB', () => {
      // 创建一个大于2MB的假图片文件
      const largeFile = path.join(testImageDir, 'large-sync.jpg');
      const largeContent = 'A'.repeat(3 * 1024 * 1024); // 3MB
      fs.writeFileSync(largeFile, largeContent);

      const result = ImageUtils.convertImageToBase64Sync(largeFile);
      expect(result).toBe('');
    });

    it('should return empty string for files larger than 500KB in sync mode', () => {
      // 创建一个600KB的假图片文件
      const mediumFile = path.join(testImageDir, 'medium-sync.jpg');
      const mediumContent = 'A'.repeat(600 * 1024); // 600KB
      fs.writeFileSync(mediumFile, mediumContent);

      const result = ImageUtils.convertImageToBase64Sync(mediumFile);
      expect(result).toBe('');
    });
  });

  describe('fileToBase64 (private method test via small valid image)', () => {
    it('should convert small valid image to base64', async () => {
      // 创建一个最小的有效JPEG文件头
      const smallJpeg = path.join(testImageDir, 'small.jpg');
      const jpegHeader = Buffer.from([
        0xff, 0xd8, 0xff, 0xe0, 0x00, 0x10, 0x4a, 0x46, 0x49, 0x46, 0x00, 0x01, 0x01, 0x01, 0x00, 0x48, 0x00, 0x48,
        0x00, 0x00, 0xff, 0xd9,
      ]);
      fs.writeFileSync(smallJpeg, jpegHeader);

      const result = await ImageUtils.convertImageToBase64(smallJpeg);
      expect(result).toMatch(/^data:image\/jpeg;base64,/);
      expect(result.length).toBeGreaterThan(50);
    });

    describe('convertToBase64 (综合转换函数)', () => {
      it('should return empty string for non-existent file', async () => {
        const result = await ImageUtils.convertToBase64('/non/existent/file.jpg');
        expect(result).toBe('');
      });

      it('should return empty string for invalid file', async () => {
        // 创建一个非图片文件
        const textFile = path.join(testImageDir, 'test-convert.txt');
        fs.writeFileSync(textFile, 'This is not an image');

        const result = await ImageUtils.convertToBase64(textFile);
        expect(result).toBe('');
      });

      it('should return empty string for files larger than 2MB', async () => {
        // 创建一个大于2MB的假图片文件
        const largeFile = path.join(testImageDir, 'large-convert.jpg');
        const largeContent = 'A'.repeat(3 * 1024 * 1024); // 3MB
        fs.writeFileSync(largeFile, largeContent);

        const result = await ImageUtils.convertToBase64(largeFile);
        expect(result).toBe('');
      });

      it('should convert small valid image to base64 directly', async () => {
        // 创建一个最小的有效JPEG文件头
        const smallJpeg = path.join(testImageDir, 'small-convert.jpg');
        const jpegHeader = Buffer.from([
          0xff, 0xd8, 0xff, 0xe0, 0x00, 0x10, 0x4a, 0x46, 0x49, 0x46, 0x00, 0x01, 0x01, 0x01, 0x00, 0x48, 0x00, 0x48,
          0x00, 0x00, 0xff, 0xd9,
        ]);
        fs.writeFileSync(smallJpeg, jpegHeader);

        const result = await ImageUtils.convertToBase64(smallJpeg);
        expect(result).toMatch(/^data:image\/jpeg;base64,/);
        expect(result.length).toBeGreaterThan(50);
      });
    });
  });

  describe('helper methods', () => {
    it('should get correct MIME type', () => {
      expect(ImageUtils.getImageMimeType('test.jpg')).toBe('image/jpeg');
      expect(ImageUtils.getImageMimeType('test.png')).toBe('image/png');
      expect(ImageUtils.getImageMimeType('test.gif')).toBe('image/gif');
      expect(ImageUtils.getImageMimeType('test.txt')).toBeNull();
    });

    it('should check image size correctly', () => {
      const testFile = path.join(testImageDir, 'size-test.jpg');
      const content = 'A'.repeat(1000); // 1KB
      fs.writeFileSync(testFile, content);

      expect(ImageUtils.checkImageSize(testFile, 2000)).toBe(true);
      expect(ImageUtils.checkImageSize(testFile, 500)).toBe(false);
    });

    it('should return supported extensions', () => {
      const extensions = ImageUtils.getSupportedExtensions();
      expect(extensions).toContain('.jpg');
      expect(extensions).toContain('.png');
      expect(extensions).toContain('.gif');
      expect(extensions.length).toBeGreaterThan(5);
    });
  });
});
