import * as path from 'path';
import fs from 'fs/promises';
import * as vscode from 'vscode';
import { isRemoteEnvironment } from './fs';
import { MakeDirectoryOptions, Dirent } from 'fs';

type FileSystemError = {
  code: string;
  message: string;
};

interface UnifiedStats {
  isFile(): boolean;
  isDirectory(): boolean;
  isSymbolicLink(): boolean;
  size: number;
  mtime: number;
  ctime: number;
  birthtime: number;
}

export class FileSystemHelper {
  private static readonly isRemote: boolean = isRemoteEnvironment();
  // 获取当前工作区的基础URI信息
  private static getWorkspaceBaseUri(): vscode.Uri | undefined {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    return workspaceFolders?.[0]?.uri;
  }
  static getRemotePath(p: string | vscode.Uri): string {
    if (p instanceof vscode.Uri) {
      return FileSystemHelper.isRemote ? p.path : p.fsPath;
    }
    return p;
  }

  /**
   * 异步检查文件或目录是否存在
   * @param p 文件或目录的路径或URI
   * @returns Promise<boolean> 文件是否存在
   */
  static async exists(p: string | vscode.Uri): Promise<boolean> {
    try {
      if (FileSystemHelper.isRemote) {
        const uri = FileSystemHelper.getUri(p);
        await vscode.workspace.fs.stat(uri);
        return true;
      } else {
        const path = p instanceof vscode.Uri ? p.fsPath : p;
        await fs.access(path);
        return true;
      }
    } catch (error) {
      // 文件不存在或无权限访问时返回 false
      const errorCode = (error as any)?.code;
      if (errorCode === 'ENOENT' || errorCode === 'FileNotFound') {
        return false;
      }
      if (errorCode === 'EACCES' || errorCode === 'NoPermissions') {
        return false;
      }
      return false;
    }
  }

  static basename(p: string | vscode.Uri, ext?: string): string {
    return path.basename(p instanceof vscode.Uri ? p.path : p, ext);
  }

  static normalize(p: string | vscode.Uri): string {
    if (p instanceof vscode.Uri) {
      return p.toString(); // 保留完整URI信息
    }
    return FileSystemHelper.isRemote ? vscode.Uri.file(p).toString() : path.normalize(p);
  }

  static dirname(p: string | vscode.Uri): string {
    return path.dirname(p instanceof vscode.Uri ? p.path : p);
  }

  static extname(p: string | vscode.Uri): string {
    return path.extname(p instanceof vscode.Uri ? p.path : p);
  }

  static join(...paths: (string | vscode.Uri)[]): string {
    if (FileSystemHelper.isRemote) {
      // 确保保留第一个URI的所有信息（scheme、authority等）
      const baseUri = paths[0] instanceof vscode.Uri ? paths[0] : vscode.Uri.file(paths[0] || '/');

      // 使用joinPath但保留baseUri的所有属性
      const result = vscode.Uri.joinPath(baseUri, ...paths.slice(1).map((p) => (p instanceof vscode.Uri ? p.path : p)));

      return result.toString(); // 返回完整URI字符串
    }
    return path.join(...paths.map((p) => (p instanceof vscode.Uri ? p.path : p)));
  }
  static resolveUri(...pathSegments: (string | vscode.Uri)[]): string | vscode.Uri {
    if (FileSystemHelper.isRemote) {
      // 获取基础URI
      const workspaceUri = FileSystemHelper.getWorkspaceBaseUri();
      if (!workspaceUri) {
        throw new Error('No workspace folder found');
      }

      // 确保保留第一个URI的所有信息
      const baseUri =
        pathSegments[0] instanceof vscode.Uri
          ? pathSegments[0]
          : vscode.Uri.from({
              scheme: workspaceUri.scheme,
              authority: workspaceUri.authority,
              path: pathSegments[0]?.replace(/^[A-Z]:/i, '') || '/',
            });

      // 使用joinPath但保留baseUri的所有属性
      const result = vscode.Uri.joinPath(
        baseUri,
        ...pathSegments.slice(1).map((p) => {
          // 获取路径字符串，并移除可能的驱动器前缀
          const path = (typeof p === 'string' ? p : (p as vscode.Uri).path).replace(/^[A-Z]:/i, '');

          // 从路径中移除基础路径部分
          const basePath = baseUri.path;
          if (path.startsWith(basePath)) {
            return path.substring(basePath.length).replace(/^\//, '');
          }

          // 如果路径以 / 开头，去掉开头的 /
          return path.startsWith('/') ? path.substring(1) : path;
        })
      );

      return result;
    }
    return path.resolve(...pathSegments.map((p) => (p instanceof vscode.Uri ? p.path : p)));
  }

  static resolve(...pathSegments: (string | vscode.Uri)[]): string {
    if (FileSystemHelper.isRemote) {
      // 确保保留第一个URI的所有信息
      const baseUri = pathSegments[0] instanceof vscode.Uri ? pathSegments[0] : vscode.Uri.file(pathSegments[0] || '/');

      // 使用joinPath但保留baseUri的所有属性
      const result = vscode.Uri.joinPath(
        baseUri,
        ...pathSegments.slice(1).map((p) => (p instanceof vscode.Uri ? p.path : p))
      );

      return result.toString(); // 返回完整URI字符串
    }
    return path.resolve(...pathSegments.map((p) => (p instanceof vscode.Uri ? p.path : p)));
  }

  static relative(from: string | vscode.Uri, to: string | vscode.Uri): string {
    if (FileSystemHelper.isRemote) {
      const fromUri = from instanceof vscode.Uri ? from : vscode.Uri.file(from);
      const toUri = to instanceof vscode.Uri ? to : vscode.Uri.file(to);

      // 使用asRelativePath但确保保留URI信息
      return vscode.workspace.asRelativePath(toUri, false);
    }
    return path.relative(from instanceof vscode.Uri ? from.path : from, to instanceof vscode.Uri ? to.path : to);
  }

  /**
   * 获取URI对象，保留原始URI的所有信息
   * 如果输入是字符串，则转换为正确的 URI
   * 如果输入已经是URI，则直接返回，保留所有属性
   */
  static getUri(path: string | vscode.Uri): vscode.Uri {
    if (path instanceof vscode.Uri) {
      return path; // 直接返回原始URI，保留所有信息
    }

    // 如果是字符串路径
    if (FileSystemHelper.isRemote) {
      // 在远程环境中，如果路径已经是完整的 URI 字符串，直接解析
      if (path.includes('://')) {
        try {
          // 直接解析URI，vscode.Uri.parse会自动处理URL编码的路径
          const uri = vscode.Uri.parse(path);
          return uri;
        } catch (error) {
          console.warn(`[FileSystemHelper] Failed to parse URI: ${path}`, error);
          // 如果解析失败，尝试作为相对路径处理
        }
      }
      // 否则，获取工作区的基础 URI 并构建正确的远程 URI
      const workspaceUri = FileSystemHelper.getWorkspaceBaseUri();
      if (workspaceUri) {
        // 确保路径是相对路径
        const relativePath = path.startsWith('/') ? path.substring(1) : path;
        return vscode.Uri.joinPath(workspaceUri, relativePath);
      }
    }

    // 本地环境或无法获取工作区 URI 时，使用 file scheme
    return vscode.Uri.file(path);
  }

  /**
   * 读取文件内容
   * @param path 文件路径或URI
   * @param encoding 文件编码
   * @returns 文件内容
   */
  static async readFile(path: string | vscode.Uri, encoding: BufferEncoding = 'utf8'): Promise<any> {
    try {
      const uri = FileSystemHelper.getUri(path);
      if (FileSystemHelper.isRemote) {
        const data = await vscode.workspace.fs.readFile(uri);
        return new TextDecoder(encoding).decode(data);
      } else {
        return await fs.readFile(uri.fsPath, { encoding });
      }
    } catch (error) {
      throw FileSystemHelper.handleError(error as FileSystemError);
    }
  }

  /**
   * 写入文件内容
   * @param path 文件路径或URI
   * @param data 要写入的数据
   */
  static async writeFile(path: string | vscode.Uri, data: Uint8Array | string): Promise<void> {
    try {
      const uri = FileSystemHelper.getUri(path);
      const content = typeof data === 'string' ? new TextEncoder().encode(data) : data;
      if (FileSystemHelper.isRemote) {
        await vscode.workspace.fs.writeFile(uri, content);
      } else {
        await fs.writeFile(uri.fsPath, content);
      }
    } catch (error) {
      throw FileSystemHelper.handleError(error as FileSystemError);
    }
  }

  /**
   * 获取文件或目录的状态信息
   * @param path 文件或目录的路径或URI
   * @returns 统一的状态信息对象
   */
  static async stat(path: string | vscode.Uri): Promise<UnifiedStats> {
    try {
      const uri = FileSystemHelper.getUri(path);
      if (FileSystemHelper.isRemote) {
        const stat = await vscode.workspace.fs.stat(uri);
        return {
          isFile: () => stat.type === vscode.FileType.File,
          isDirectory: () => stat.type === vscode.FileType.Directory,
          isSymbolicLink: () => stat.type === vscode.FileType.SymbolicLink,
          size: stat.size,
          mtime: stat.mtime,
          ctime: stat.ctime,
          birthtime: stat.ctime, // vscode.FileStat doesn't have birthtime, so we use ctime
        };
      } else {
        const stat = await fs.stat(uri.fsPath);
        return {
          isFile: stat.isFile.bind(stat),
          isDirectory: stat.isDirectory.bind(stat),
          isSymbolicLink: stat.isSymbolicLink.bind(stat),
          size: stat.size,
          mtime: stat.mtimeMs,
          ctime: stat.ctimeMs,
          birthtime: stat.birthtimeMs,
        };
      }
    } catch (error) {
      throw FileSystemHelper.handleError(error as FileSystemError);
    }
  }

  static async mkdir(path: string | vscode.Uri, options?: MakeDirectoryOptions): Promise<void> {
    try {
      if (FileSystemHelper.isRemote) {
        await vscode.workspace.fs.createDirectory(FileSystemHelper.getUri(path));
      } else {
        await fs.mkdir(path instanceof vscode.Uri ? path.fsPath : path, options);
      }
    } catch (error) {
      throw FileSystemHelper.handleError(error as FileSystemError);
    }
  }

  static async readdir(path: string | vscode.Uri, options?: any): Promise<any> {
    const isRemote = FileSystemHelper.isRemote;
    const uri = FileSystemHelper.getUri(path);
    const pathString = isRemote ? uri.toString() : path instanceof vscode.Uri ? path.fsPath : path;

    try {
      if (isRemote) {
        const entries = await vscode.workspace.fs.readDirectory(uri);
        if (typeof options === 'object' && options.withFileTypes) {
          return entries.map(
            ([name, type]) =>
              ({
                name,
                isFile: () => type === vscode.FileType.File,
                isDirectory: () => type === vscode.FileType.Directory,
                isSymbolicLink: () => type === vscode.FileType.SymbolicLink,
                isBlockDevice: () => false,
                isCharacterDevice: () => false,
                isFIFO: () => false,
                isSocket: () => false,
              } as Dirent)
          );
        }
        return entries.map(([name]) => name);
      } else {
        return await fs.readdir(pathString, options);
      }
    } catch (error) {
      const errorCode = (error as NodeJS.ErrnoException).code;
      if (errorCode === 'ENOENT' || errorCode === 'FileNotFound') {
        console.warn(`文件夹路径找不到: ${pathString}`);
        return [];
      }
      if (errorCode === 'EACCES' || errorCode === 'NoPermissions') {
        console.warn(`没有权限访问文件夹: ${pathString}`);
        return [];
      }
      console.error(`读取目录失败: ${pathString}`, error);
      return [];
    }
  }

  static async unlink(path: string | vscode.Uri): Promise<void> {
    try {
      if (FileSystemHelper.isRemote) {
        await vscode.workspace.fs.delete(FileSystemHelper.getUri(path));
      } else {
        await fs.unlink(path instanceof vscode.Uri ? path.fsPath : path);
      }
    } catch (error) {
      throw FileSystemHelper.handleError(error as FileSystemError);
    }
  }

  static async rmdir(path: string | vscode.Uri): Promise<void> {
    try {
      if (FileSystemHelper.isRemote) {
        await vscode.workspace.fs.delete(FileSystemHelper.getUri(path), { recursive: true });
      } else {
        await fs.rmdir(path instanceof vscode.Uri ? path.fsPath : path);
      }
    } catch (error) {
      throw FileSystemHelper.handleError(error as FileSystemError);
    }
  }

  static async access(path: string | vscode.Uri, mode?: number): Promise<void> {
    try {
      if (FileSystemHelper.isRemote) {
        const uri = FileSystemHelper.getUri(path);
        await vscode.workspace.fs.stat(uri);
      } else {
        await fs.access(path instanceof vscode.Uri ? path.fsPath : path, mode);
      }
    } catch (error) {
      // 提供更详细的错误信息用于调试
      const pathStr = typeof path === 'string' ? path : path.toString();
      const errorMsg = error instanceof Error ? error.message : String(error);
      throw FileSystemHelper.handleError(error as FileSystemError);
    }
  }

  /**
   * 处理文件系统错误
   * @param error 捕获的错误
   * @returns 格式化的错误对象
   */
  private static handleError(error: FileSystemError): Error {
    const errorMap: { [key: string]: string } = {
      FileNotFound: '文件未找到',
      ENOENT: '文件未找到',
      NoPermissions: '权限被拒绝',
      EACCES: '权限被拒绝',
    };
    const errorType = errorMap[error.code] || '文件系统错误';
    return new Error(`${errorType}：${error.message}`);
  }
}
