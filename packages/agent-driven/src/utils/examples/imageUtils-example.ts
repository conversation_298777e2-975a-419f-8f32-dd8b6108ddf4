import { ImageUtils } from '../imageUtils';
import path from 'path';

/**
 * ImageUtils使用示例
 * 演示如何使用新添加的图片转base64功能
 */
export class ImageUtilsExample {
  /**
   * 综合转换函数示例（推荐使用）
   */
  static async convertToBase64Example() {
    console.log('=== ImageUtils.convertToBase64 示例（推荐） ===');

    // 示例1: 转换小图片（自动选择直接转换）
    const smallImagePath = path.join(__dirname, 'sample-images/small-image.jpg');
    console.log('转换小图片:', smallImagePath);

    try {
      const base64Result = await ImageUtils.convertToBase64(smallImagePath);
      if (base64Result) {
        console.log('✅ 转换成功，base64长度:', base64Result.length);
        console.log('📄 MIME类型:', base64Result.split(';')[0]);
      } else {
        console.log('❌ 转换失败或文件不符合要求');
      }
    } catch (error) {
      console.error('转换过程中出错:', error);
    }

    // 示例2: 转换中等大小图片（自动选择压缩转换）
    const mediumImagePath = path.join(__dirname, 'sample-images/medium-image.jpg');
    console.log('\n转换中等大小图片:', mediumImagePath);

    try {
      const base64Result = await ImageUtils.convertToBase64(mediumImagePath);
      if (base64Result) {
        console.log('✅ 自动压缩并转换成功，base64长度:', base64Result.length);
        // 计算压缩后的大小
        const base64Data = base64Result.split(',')[1];
        const compressedSize = (base64Data.length * 3) / 4;
        console.log('📊 压缩后大小:', Math.round(compressedSize / 1024), 'KB');
      } else {
        console.log('❌ 转换失败或文件不符合要求');
      }
    } catch (error) {
      console.error('转换过程中出错:', error);
    }

    // 示例3: 尝试转换过大的图片（>2M）
    const largeImagePath = path.join(__dirname, 'sample-images/large-image.jpg');
    console.log('\n尝试转换大图片:', largeImagePath);

    try {
      const base64Result = await ImageUtils.convertToBase64(largeImagePath);
      if (base64Result) {
        console.log('✅ 意外成功转换');
      } else {
        console.log('❌ 按预期返回空字符串（文件过大）');
      }
    } catch (error) {
      console.error('转换过程中出错:', error);
    }
  }

  /**
   * 异步转换图片为base64的示例（详细版本）
   */
  static async convertImageExample() {
    console.log('=== ImageUtils.convertImageToBase64 示例 ===');

    // 示例1: 转换小图片（500K以内）
    const smallImagePath = path.join(__dirname, 'sample-images/small-image.jpg');
    console.log('转换小图片:', smallImagePath);

    try {
      const base64Result = await ImageUtils.convertImageToBase64(smallImagePath);
      if (base64Result) {
        console.log('✅ 转换成功，base64长度:', base64Result.length);
        console.log('📄 MIME类型:', base64Result.split(';')[0]);
      } else {
        console.log('❌ 转换失败或文件不符合要求');
      }
    } catch (error) {
      console.error('转换过程中出错:', error);
    }

    // 示例2: 转换中等大小图片（500K-2M，需要压缩）
    const mediumImagePath = path.join(__dirname, 'sample-images/medium-image.jpg');
    console.log('\n转换中等大小图片:', mediumImagePath);

    try {
      const base64Result = await ImageUtils.convertImageToBase64(mediumImagePath);
      if (base64Result) {
        console.log('✅ 压缩并转换成功，base64长度:', base64Result.length);
        // 计算压缩后的大小
        const base64Data = base64Result.split(',')[1];
        const compressedSize = (base64Data.length * 3) / 4;
        console.log('📊 压缩后大小:', Math.round(compressedSize / 1024), 'KB');
      } else {
        console.log('❌ 转换失败或文件不符合要求');
      }
    } catch (error) {
      console.error('转换过程中出错:', error);
    }

    // 示例3: 尝试转换过大的图片（>2M）
    const largeImagePath = path.join(__dirname, 'sample-images/large-image.jpg');
    console.log('\n尝试转换大图片:', largeImagePath);

    try {
      const base64Result = await ImageUtils.convertImageToBase64(largeImagePath);
      if (base64Result) {
        console.log('✅ 意外成功转换');
      } else {
        console.log('❌ 按预期返回空字符串（文件过大）');
      }
    } catch (error) {
      console.error('转换过程中出错:', error);
    }
  }

  /**
   * 同步转换图片为base64的示例
   */
  static convertImageSyncExample() {
    console.log('\n=== ImageUtils.convertImageToBase64Sync 示例 ===');

    // 同步方法只支持500K以内的图片
    const smallImagePath = path.join(__dirname, 'sample-images/small-image.jpg');
    console.log('同步转换小图片:', smallImagePath);

    try {
      const base64Result = ImageUtils.convertImageToBase64Sync(smallImagePath);
      if (base64Result) {
        console.log('✅ 同步转换成功，base64长度:', base64Result.length);
      } else {
        console.log('❌ 同步转换失败或文件不符合要求');
      }
    } catch (error) {
      console.error('同步转换过程中出错:', error);
    }
  }

  /**
   * 图片验证示例
   */
  static imageValidationExample() {
    console.log('\n=== 图片验证示例 ===');

    const testFiles = [
      'test.jpg',
      'test.png',
      'test.gif',
      'test.txt', // 非图片文件
      'nonexistent.jpg', // 不存在的文件
    ];

    testFiles.forEach(async (filename) => {
      const filePath = path.join(__dirname, 'sample-images', filename);

      // 异步验证
      const isValidAsync = await ImageUtils.isValidImage(filePath);
      console.log(`📁 ${filename} - 异步验证:`, isValidAsync ? '✅ 有效' : '❌ 无效');

      // 同步验证
      const isValidSync = ImageUtils.isValidImageSync(filePath);
      console.log(`📁 ${filename} - 同步验证:`, isValidSync ? '✅ 有效' : '❌ 无效');

      // 获取MIME类型
      const mimeType = ImageUtils.getImageMimeType(filePath);
      console.log(`📁 ${filename} - MIME类型:`, mimeType || '未知');
    });
  }

  /**
   * 实用工具方法示例
   */
  static utilityMethodsExample() {
    console.log('\n=== 实用工具方法示例 ===');

    // 获取支持的扩展名
    const supportedExtensions = ImageUtils.getSupportedExtensions();
    console.log('🎨 支持的图片扩展名:', supportedExtensions.join(', '));

    // 检查文件大小
    const testFile = path.join(__dirname, 'sample-images/test.jpg');
    const isUnder500KB = ImageUtils.checkImageSize(testFile, 500 * 1024);
    const isUnder2MB = ImageUtils.checkImageSize(testFile, 2 * 1024 * 1024);

    console.log(`📏 ${testFile}:`);
    console.log('  - 小于500KB:', isUnder500KB ? '✅ 是' : '❌ 否');
    console.log('  - 小于2MB:', isUnder2MB ? '✅ 是' : '❌ 否');
  }

  /**
   * 运行所有示例
   */
  static async runAllExamples() {
    console.log('🚀 ImageUtils 功能演示开始\n');

    await this.convertToBase64Example();
    await this.convertImageExample();
    this.convertImageSyncExample();
    await this.imageValidationExample();
    this.utilityMethodsExample();

    console.log('\n✨ 演示完成！');
  }
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
  ImageUtilsExample.runAllExamples().catch(console.error);
}
