# ImageUtils 图片处理工具类

ImageUtils 是一个强大的图片处理工具类，提供图片验证、格式检测和 base64 转换等功能。

## 新增功能：图片转 Base64

### 功能特性

- ✅ **智能大小处理**：根据文件大小采用不同策略
  - 500K 以内：直接转换
  - 500K-2M：自动压缩至 500K 以内
  - 超过 2M：返回空字符串
- ✅ **多格式支持**：支持 JPEG、PNG、GIF、WebP、BMP 等主流格式
- ✅ **图片验证**：通过魔数验证确保文件为有效图片
- ✅ **压缩优化**：支持 Sharp 库进行高质量压缩（可选）
- ✅ **同步/异步**：提供同步和异步两种调用方式

### 核心方法

#### 1. `convertToBase64(filePath: string): Promise<string>` ⭐ **推荐**

综合转换函数，根据文件大小自动选择最佳处理策略。

```typescript
import { ImageUtils } from './utils/imageUtils';

// 推荐使用：自动选择最佳转换策略
const base64 = await ImageUtils.convertToBase64('/path/to/image.jpg');
if (base64) {
  console.log('转换成功！');
  // base64 格式: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
} else {
  console.log('转换失败或文件不符合要求');
}
```

#### 2. `convertImageToBase64(filePath: string): Promise<string>`

异步转换图片为 base64 字符串（详细版本）。

```typescript
// 详细版本转换
const base64 = await ImageUtils.convertImageToBase64('/path/to/image.jpg');
```

#### 3. `convertImageToBase64Sync(filePath: string): string`

同步转换图片为 base64 字符串（仅支持 500K 以内的图片）。

```typescript
// 同步转换（仅限小文件）
const base64 = ImageUtils.convertImageToBase64Sync('/path/to/small-image.jpg');
```

### 处理策略

| 文件大小 | 处理方式 | 说明 |
|---------|---------|------|
| ≤ 500K | 直接转换 | 无需压缩，直接读取并转换为 base64 |
| 500K - 2M | 压缩转换 | 多轮压缩至 500K 以内，然后转换 |
| > 2M | 返回空字符串 | 文件过大，不进行处理 |

### 压缩算法

当图片大小在 500K-2M 之间时，会自动进行压缩：

1. **优先使用 Sharp**：如果项目中安装了 Sharp 库，使用高质量压缩
2. **备用方案**：如果 Sharp 不可用，使用基础压缩策略
3. **多轮压缩**：通过调整质量和尺寸，逐步压缩至目标大小

### 使用示例

```typescript
import { ImageUtils } from './utils/imageUtils';

async function processImages() {
  // 示例1: 使用综合转换函数（推荐）
  const image1 = await ImageUtils.convertToBase64('./small.jpg');    // 自动直接转换
  const image2 = await ImageUtils.convertToBase64('./medium.jpg');   // 自动压缩转换
  const image3 = await ImageUtils.convertToBase64('./large.jpg');    // 返回空字符串
  
  // 示例2: 使用详细版本函数
  const detailedResult = await ImageUtils.convertImageToBase64('./image.jpg');
  
  // 示例3: 检查图片是否有效
  const isValid = await ImageUtils.isValidImage('./test.jpg');
  
  // 示例4: 获取图片 MIME 类型
  const mimeType = ImageUtils.getImageMimeType('./test.png');
  // 返回: "image/png"
  
  // 示例5: 检查文件大小
  const isUnder500K = ImageUtils.checkImageSize('./test.jpg', 500 * 1024);
}
```

### 错误处理

函数会在以下情况返回空字符串：

- 文件不存在
- 文件不是有效的图片格式
- 文件大小超过 2M
- 压缩失败且无法处理

### 支持的图片格式

- **JPEG/JPG** (.jpg, .jpeg)
- **PNG** (.png)
- **GIF** (.gif)
- **WebP** (.webp)
- **BMP** (.bmp)
- **TIFF** (.tiff)
- **ICO** (.ico)
- **HEIC** (.heic)
- **AVIF** (.avif)

### 性能优化建议

1. **使用异步方法**：对于大文件处理，推荐使用 `convertImageToBase64`
2. **安装 Sharp**：为获得更好的压缩效果，建议安装 Sharp 库
3. **预检查大小**：可先使用 `checkImageSize` 检查文件大小
4. **批量处理**：使用 `Promise.all` 并行处理多个图片

```typescript
// 批量处理示例（推荐使用综合转换函数）
const imagePaths = ['img1.jpg', 'img2.png', 'img3.gif'];
const base64Results = await Promise.all(
  imagePaths.map(path => ImageUtils.convertToBase64(path))
);
```

### 安装 Sharp（可选）

为获得更好的压缩效果，可以安装 Sharp 库：

```bash
npm install sharp
# 或
pnpm add sharp
```

安装后，ImageUtils 会自动检测并使用 Sharp 进行高质量压缩。

### 注意事项

- 同步方法 `convertImageToBase64Sync` 不支持压缩功能
- 压缩过程可能需要一些时间，特别是对于较大的图片
- Base64 编码会增加约 33% 的数据大小
- 建议在生产环境中对返回结果进行适当的缓存

### 测试

运行测试以验证功能：

```bash
cd packages/agent-driven
npx jest src/utils/__tests__/imageUtils.test.ts
```

### 更新日志

- **v3.0.1**: 新增图片转 base64 功能，支持智能压缩和多格式处理
