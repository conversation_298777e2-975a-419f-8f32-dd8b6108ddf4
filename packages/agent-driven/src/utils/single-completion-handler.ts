import { buildApiHandler } from '../adaptor/api';

/**
 * Enhances a prompt using the configured API without creating a full Cline instance or task history.
 * This is a lightweight alternative that only uses the API's completion functionality.
 */
export async function singleCompletionHandler(apiConfiguration: any, promptText: string): Promise<string> {
  if (!promptText) {
    throw new Error('未提供提示词');
  }
  if (!apiConfiguration || !apiConfiguration.apiProvider) {
    throw new Error('API配置不可用');
  }

  const handler = buildApiHandler(apiConfiguration);

  // Check if handler supports single completions
  if (!('completePrompt' in handler)) {
    throw new Error('提示词增强失败');
  }

  return handler.completePrompt(promptText);
}
