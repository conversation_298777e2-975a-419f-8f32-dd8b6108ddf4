import fs from 'fs';
import os from 'os';
import * as path from 'path';
import { JoyCoderProvider } from '../../core/webview/JoycoderProvider';
import to from 'await-to-js';
import axios from 'axios';
import { getBaseUrl, getJdhCgiUrl } from '@joycoder/shared';
import { DataSetsAPI } from '@joycoder/plugin-base-ai/src/knowledgeBase/api';

class DocTracker {
  private providerRef: WeakRef<JoyCoderProvider>;
  private docPath: string = '';

  private readonly BASE_URL = `${getJdhCgiUrl(getBaseUrl() + '/api/saas/knowledge/v1/retrieval')}`;
  private readonly headers = DataSetsAPI.headers();

  constructor(provider: JoyCoderProvider) {
    this.providerRef = new WeakRef(provider);
    const dataStesPath = path.join(os.homedir(), '.joycode', 'knowledge-base.json');
    this.docPath = dataStesPath;
    try {
      const dir = path.dirname(dataStesPath);
      // 确保目录存在
      if (fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        fs.watch(dataStesPath, (eventType) => {
          if (eventType === 'change') {
            this.updateDocList();
          }
        });
      }
    } catch (error) {}
  }

  private async getDocList() {
    try {
      const docs = await fs.promises.readFile(this.docPath, 'utf-8');
      if (docs) {
        return JSON.parse(docs);
      }
    } catch (error) {}
    return [];
  }

  async updateDocList() {
    const docList = await this.getDocList();
    this.providerRef.deref()?.postMessageToWebview({
      type: 'docListUpdated',
      docList,
    });
  }

  async getDocContent(props: any) {
    const question = props?.question;
    const name = props?.name;
    const dataset_ids = (await this.getDocList())
      ?.filter((item: { name: string | undefined }) => item.name === name)
      ?.map((item: any) => item.id);
    const [err, response]: [any, any] = await to(
      axios.post(
        `${this.BASE_URL}`,
        {
          dataset_ids,
          question,
        },
        { headers: this.headers }
      )
    );
    if (err) {
      return '';
    }
    const resData = response.data?.data?.chunks?.map((item: { content: string }) => item.content).join('\n');
    return resData;
  }
}

export default DocTracker;
