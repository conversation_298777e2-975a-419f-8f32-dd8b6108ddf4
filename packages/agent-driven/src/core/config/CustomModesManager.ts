import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs-extra';
import { ModeConfig } from '../../shared/modes';
import { fileExistsAtPath, isRemoteEnvironment } from '../../utils/fs';
import { arePathsEqual, getWorkspacePath } from '../../utils/path';
import { GlobalFileNames } from '../storage/disk';
import { customModesSettingsSchema } from '../../../web-agent/src/utils/modes';
import { FileSystemHelper } from '../../utils/FileSystemHelper';
import { setSettingWebviewData } from '@joycoder/plugin-base-ai/src/dialog/setting';

const ROOMODES_FILENAME = 'mode.json';
const ROOMODES_PATH = `.joycode/${ROOMODES_FILENAME}`;
const INITIAL_MODE_DATA = {
  version: '0.1.0',
  customModes: [],
};

export class CustomModesManager {
  private static readonly cacheTTL = 10_000;

  private disposables: vscode.Disposable[] = [];
  private isWriting = false;
  private writeQueue: Array<() => Promise<void>> = [];
  private cachedModes: ModeConfig[] | null = null;
  private cachedAt: number = 0;
  private readonly isRemote: boolean;

  constructor(private readonly context: vscode.ExtensionContext, private readonly onUpdate: () => Promise<void>) {
    this.isRemote = isRemoteEnvironment();
    // TODO: We really shouldn't have async methods in the constructor.
    this.watchCustomModesFiles();
  }

  private async queueWrite(operation: () => Promise<void>): Promise<void> {
    this.writeQueue.push(operation);

    if (!this.isWriting) {
      await this.processWriteQueue();
    }
  }

  private async processWriteQueue(): Promise<void> {
    if (this.isWriting || this.writeQueue.length === 0) {
      return;
    }

    this.isWriting = true;

    try {
      while (this.writeQueue.length > 0) {
        const operation = this.writeQueue.shift();

        if (operation) {
          await operation();
        }
      }
    } finally {
      this.isWriting = false;
    }
  }

  private async getWorkspaceRoomodes(relativePath?: string): Promise<string | vscode.Uri | undefined> {
    const workspaceFolders = vscode.workspace.workspaceFolders;

    if (!workspaceFolders || workspaceFolders.length === 0) {
      return undefined;
    }

    const workspaceRoot = getWorkspacePath();
    let roomodesPath: string | vscode.Uri;

    if (this.isRemote) {
      // 在远程环境下，workspaceRoot 应该是 vscode.Uri 对象
      const workspaceUri = workspaceRoot instanceof vscode.Uri ? workspaceRoot : vscode.Uri.parse(workspaceRoot);
      const targetPath = relativePath ? relativePath : ROOMODES_PATH;
      roomodesPath = vscode.Uri.joinPath(workspaceUri, targetPath);
    } else {
      roomodesPath = FileSystemHelper.resolveUri(workspaceRoot, relativePath ? relativePath : ROOMODES_PATH);
    }

    // 在远程环境下，如果是查找 mode.json 文件，即使不存在也返回路径，以便后续创建
    if (this.isRemote && !relativePath) {
      return roomodesPath.toString();
    }

    const exists = await fileExistsAtPath(roomodesPath);
    return exists ? roomodesPath : undefined;
  }

  private async loadModesFromFile(filePath: string | vscode.Uri): Promise<ModeConfig[]> {
    try {
      // 统一转换为 URI 对象
      const uri = FileSystemHelper.getUri(filePath);

      // 读取文件内容
      const content = await FileSystemHelper.readFile(uri, 'utf-8');

      // 解析 JSON 内容
      let settings;
      try {
        settings = JSON.parse(content);
      } catch (error) {
        console.error(`[CustomModesManager] JSON 解析失败: ${FileSystemHelper.getRemotePath(uri)}`);
        return [];
      }

      // 验证配置格式
      const result = customModesSettingsSchema.safeParse(settings);
      if (!result.success) {
        console.warn(`[CustomModesManager] 配置格式验证失败: ${FileSystemHelper.getRemotePath(uri)}`);
        return [];
      }

      // 根据文件路径确定来源
      const normalizedPath = FileSystemHelper.getRemotePath(uri);
      const isRoomodes = normalizedPath.endsWith(ROOMODES_FILENAME);
      const source = isRoomodes ? ('project' as const) : ('global' as const);

      // 添加来源信息到每个模式
      return result.data.customModes.map((mode: ModeConfig) => ({
        ...mode,
        source,
      }));
    } catch (error) {
      // 使用更详细的错误日志
      const errorMsg = `从 ${FileSystemHelper.getRemotePath(filePath)} 加载失败: ${
        error instanceof Error ? error.message : String(error)
      }`;
      console.log(`[CustomModesManager] ${errorMsg}`);

      // 文件不存在时不需要打印错误
      if (error instanceof Error && error.message.includes('文件未找到')) {
        console.log(`[CustomModesManager] 配置文件不存在: ${FileSystemHelper.getRemotePath(filePath)}`);
      }

      return [];
    }
  }

  private async mergeCustomModes(projectModes: ModeConfig[], globalModes: ModeConfig[]): Promise<ModeConfig[]> {
    const slugs = new Set<string>();
    const merged: ModeConfig[] = [];

    // Add project mode (takes precedence)
    for (const mode of projectModes) {
      if (!slugs.has(mode.agentId)) {
        slugs.add(mode.agentId);
        merged.push({ ...mode, source: 'project' });
      }
    }

    // Add non-duplicate global modes
    for (const mode of globalModes) {
      if (!slugs.has(mode.agentId)) {
        slugs.add(mode.agentId);
        merged.push({ ...mode, source: 'global' });
      }
    }

    return merged;
  }

  public async getCustomModesFilePath(): Promise<string | null> {
    // 在远程环境下，不处理全局配置文件
    if (this.isRemote) {
      return null;
    }

    const settingsDir = await this.ensureSettingsDirectoryExists();
    const filePath = path.join(settingsDir, GlobalFileNames.customModes);
    const fileExists = await fileExistsAtPath(filePath);

    if (!fileExists) {
      await this.queueWrite(() => fs.writeFile(filePath, JSON.stringify(INITIAL_MODE_DATA, null, 2)));
    }

    return filePath;
  }

  private async watchCustomModesFiles(): Promise<void> {
    const settingsPath = await this.getCustomModesFilePath();

    // 在远程环境下，不监听全局配置文件
    if (!this.isRemote && settingsPath) {
      // Watch settings file
      this.disposables.push(
        vscode.workspace.onDidSaveTextDocument(async (document) => {
          if (arePathsEqual(document.uri.fsPath, settingsPath)) {
            const content = await fs.readFile(settingsPath, 'utf-8');

            const errorMessage = '无效的自定义模式格式。请确保您的设置遵循正确的 JSON 格式。';

            let config: any;

            try {
              config = JSON.parse(content);
            } catch (error) {
              console.error(error);
              vscode.window.showErrorMessage(errorMessage);
              return;
            }

            const result = customModesSettingsSchema.safeParse(config);

            if (!result.success) {
              vscode.window.showErrorMessage(errorMessage);
              return;
            }

            // Get modes from mode.json if it exists (takes precedence)
            const roomodesPath = await this.getWorkspaceRoomodes();
            const roomodesModes = roomodesPath ? await this.loadModesFromFile(roomodesPath) : [];

            // Merge modes from both sources (mode.json takes precedence)
            const mergedModes = await this.mergeCustomModes(roomodesModes, result.data.customModes);
            await this.context.globalState.update('customModes', mergedModes);
            this.clearCache();
            await this.onUpdate();
          }
        })
      );
    }

    // Watch mode.json file if it exists
    const roomodesPath = await this.getWorkspaceRoomodes();

    if (roomodesPath) {
      this.disposables.push(
        vscode.workspace.onDidSaveTextDocument(async (document) => {
          if (
            arePathsEqual(document.uri.fsPath, FileSystemHelper.getRemotePath(roomodesPath)) ||
            document.uri.fsPath.indexOf('.joycode/modes/') > -1
          ) {
            // 在远程环境下，只处理项目级别的配置
            if (this.isRemote) {
              const roomodesModes = await this.loadModesFromFile(roomodesPath);
              await this.context.globalState.update('customModes', roomodesModes);
              this.clearCache();
              await this.onUpdate();
            } else {
              // 本地环境下，合并全局和项目配置
              const settingsModes = settingsPath ? await this.loadModesFromFile(settingsPath) : [];
              const roomodesModes = await this.loadModesFromFile(roomodesPath);
              const currentMDInfo = await fs.readFile(document.uri.fsPath);
              const currentFolderName = path.basename(path.dirname(document.uri.fsPath));
              const modeId = currentFolderName.split('rules-')[1];
              const currentFileName = path.basename(document.uri.fsPath).split('.')[0];

              roomodesModes.forEach((item, idx) => {
                if (item.agentId === modeId) {
                  (roomodesModes[idx] as { [key: string]: any })[currentFileName] = currentMDInfo.toString();
                }
              });

              // mode.json takes precedence
              const mergedModes = await this.mergeCustomModes(roomodesModes, settingsModes);
              await this.context.globalState.update('customModes', mergedModes);
              this.clearCache();
              await this.onUpdate();
              setSettingWebviewData();
            }
          }
        })
      );
    }
  }

  public async getCustomModes(): Promise<ModeConfig[]> {
    // Check if we have a valid cached result.
    const now = Date.now();

    if (this.cachedModes && now - this.cachedAt < CustomModesManager.cacheTTL) {
      return this.cachedModes;
    }

    // Get modes from mode.json if it exists.
    const roomodesPath = await this.getWorkspaceRoomodes();

    // project modes
    const roomodesModes = roomodesPath ? await this.loadModesFromFile(roomodesPath) : [];

    // 在远程环境下，只返回项目级别的配置
    if (this.isRemote) {
      // Create maps to store modes by source.
      const projectModes = new Map<string, ModeConfig>();

      // Add project modes.
      for (const mode of roomodesModes) {
        let agentDefinitionContent = '';
        let customInstructionsContent = '';

        try {
          // 在远程环境下，使用 FileSystemHelper.resolveUri 来构建正确的路径
          if (mode.agentDefinitionPath) {
            const workspaceRoot = getWorkspacePath();
            const workspaceUri = workspaceRoot instanceof vscode.Uri ? workspaceRoot : vscode.Uri.parse(workspaceRoot);
            const agentDefinitionUri = FileSystemHelper.resolveUri(workspaceUri, mode.agentDefinitionPath);

            try {
              agentDefinitionContent = await FileSystemHelper.readFile(agentDefinitionUri, 'utf-8');
            } catch (error) {
              console.log(`[CustomModesManager] Agent definition file not found for ${mode.agentId}, using default`);
            }
          }

          if (mode.customInstructionsPath) {
            const workspaceRoot = getWorkspacePath();
            const workspaceUri = workspaceRoot instanceof vscode.Uri ? workspaceRoot : vscode.Uri.parse(workspaceRoot);
            const customInstructionsUri = FileSystemHelper.resolveUri(workspaceUri, mode.customInstructionsPath);

            try {
              customInstructionsContent = await FileSystemHelper.readFile(customInstructionsUri, 'utf-8');
            } catch (error) {
              console.log(`[CustomModesManager] Custom instructions file not found for ${mode.agentId}, using default`);
            }
          }
        } catch (error) {
          console.error(`[CustomModesManager] Error loading files for mode ${mode.agentId}:`, error);
        }

        projectModes.set(mode.agentId, {
          ...mode,
          agentDefinition: agentDefinitionContent ? agentDefinitionContent.trim() : mode.agentDefinition,
          customInstructions: customInstructionsContent ? customInstructionsContent.trim() : mode.customInstructions,
          source: 'project' as const,
        });
      }

      const mergedModes = Array.from(projectModes.values());
      await this.context.globalState.update('customModes', mergedModes);

      this.cachedModes = mergedModes;
      this.cachedAt = now;

      return mergedModes;
    }

    // 本地环境下，处理全局和项目配置的合并
    // Get modes from settings file.
    const settingsPath = await this.getCustomModesFilePath();
    const settingsModes = settingsPath ? await this.loadModesFromFile(settingsPath) : [];

    // Create maps to store modes by source.
    const projectModes = new Map<string, ModeConfig>();
    const globalModes = new Map<string, ModeConfig>();

    // Add project modes (they take precedence).
    for (const mode of roomodesModes) {
      let agentDefinitionContent = '';
      let customInstructionsContent = '';

      try {
        // 在本地环境下，也使用 FileSystemHelper 来保持一致性
        if (mode.agentDefinitionPath) {
          const workspaceRoot = getWorkspacePath();
          const agentDefinitionUri = FileSystemHelper.resolveUri(workspaceRoot, mode.agentDefinitionPath);
          try {
            agentDefinitionContent = await FileSystemHelper.readFile(agentDefinitionUri, 'utf-8');
          } catch (error) {
            console.log(`[CustomModesManager] Agent definition file not found for ${mode.agentId}, using default`);
          }
        }

        if (mode.customInstructionsPath) {
          const workspaceRoot = getWorkspacePath();
          const customInstructionsUri = FileSystemHelper.resolveUri(workspaceRoot, mode.customInstructionsPath);
          try {
            customInstructionsContent = await FileSystemHelper.readFile(customInstructionsUri, 'utf-8');
          } catch (error) {
            console.log(`[CustomModesManager] Custom instructions file not found for ${mode.agentId}, using default`);
          }
        }
      } catch (error) {
        console.error(`[CustomModesManager] Error loading files for mode ${mode.agentId}:`, error);
      }

      projectModes.set(mode.agentId, {
        ...mode,
        agentDefinition: agentDefinitionContent ? agentDefinitionContent.trim() : mode.agentDefinition,
        customInstructions: customInstructionsContent ? customInstructionsContent.trim() : mode.customInstructions,
        source: 'project' as const,
      });
    }

    // Add global modes.
    for (const mode of settingsModes) {
      if (!projectModes.has(mode.agentId)) {
        globalModes.set(mode.agentId, { ...mode, source: 'global' as const });
      }
    }

    const globalModesArray = Array.from(globalModes.values()).map((mode) => ({ ...mode, source: 'global' as const }));
    const projectModesArray = Array.from(projectModes.values()).map((mode) => ({
      ...mode,
      source: 'project' as const,
    }));

    const mergedModes = [...globalModesArray, ...projectModesArray];
    // .sort((modeA, modeB) =>
    //   modeA.agentId.localeCompare(modeB.agentId, undefined, { sensitivity: 'base' })
    // );

    await this.context.globalState.update('customModes', mergedModes);

    this.cachedModes = mergedModes;
    this.cachedAt = now;

    return mergedModes;
  }

  public async updateCustomMode(agentId: string, config: ModeConfig): Promise<void> {
    try {
      // 在远程环境下，强制使用项目模式
      const isProjectMode = this.isRemote ? true : config.source === 'project';
      let targetPath: string;
      const workspaceFolders = vscode.workspace.workspaceFolders;

      if (isProjectMode) {
        if (!workspaceFolders || workspaceFolders.length === 0) {
          console.error('Failed to update project mode: No workspace folder found', { agentId });
          throw new Error('No workspace folder found for project-specific mode');
        }

        const workspaceRoot = getWorkspacePath();
        if (this.isRemote) {
          // 在远程环境下，workspaceRoot 应该是 vscode.Uri 对象
          const workspaceUri = workspaceRoot instanceof vscode.Uri ? workspaceRoot : vscode.Uri.parse(workspaceRoot);
          const roomodesUri = vscode.Uri.joinPath(workspaceUri, ROOMODES_PATH);
          targetPath = roomodesUri.toString();
        } else {
          targetPath = FileSystemHelper.join(workspaceRoot, ROOMODES_PATH);
        }
        const exists = await fileExistsAtPath(targetPath);

        console.info(`${exists ? 'Updating' : 'Creating'} project mode in ${ROOMODES_PATH}`, {
          agentId,
          workspace: workspaceRoot,
        });
      } else {
        const globalPath = await this.getCustomModesFilePath();
        if (!globalPath) {
          throw new Error('Global modes file path is not available in remote environment');
        }
        targetPath = globalPath;
      }

      await this.queueWrite(async () => {
        // Ensure source is set correctly based on target file.
        // 远程环境下使用 FileSystemHelper
        const workspaceRoot = getWorkspacePath();
        const workspaceUri = workspaceRoot instanceof vscode.Uri ? workspaceRoot : vscode.Uri.parse(workspaceRoot);

        const basePath = `.joycode/modes/rules-${config.agentId}`;
        const agentDefinitionPath = `${basePath}/agentDefinition.md`;
        const customInstructionsPath = `${basePath}/customInstructions.md`;

        const modeWithSource = {
          ...config,
          agentDefinitionPath,
          customInstructionsPath,
          // agentDefinitionPath: this.isRemote
          //   ? vscode.Uri.joinPath(workspaceUri, agentDefinitionPath).toString()
          //   : agentDefinitionPath,
          // customInstructionsPath: this.isRemote
          //   ? vscode.Uri.joinPath(workspaceUri, customInstructionsPath).toString()
          //   : customInstructionsPath,
          source: isProjectMode ? ('project' as const) : ('global' as const),
        };

        await this.updateModesInFile(targetPath, (modes) => {
          const updatedModes = modes.filter((m) => m.agentId !== agentId);
          updatedModes.push(modeWithSource);
          return updatedModes;
        });

        try {
          if (workspaceFolders && workspaceFolders.length > 0) {
            if (this.isRemote) {
              // 使用 FileSystemHelper.resolveUri 来构建正确的路径
              const agentDefinitionUri = FileSystemHelper.resolveUri(workspaceUri, modeWithSource.agentDefinitionPath);
              const customInstructionsUri = FileSystemHelper.resolveUri(
                workspaceUri,
                modeWithSource.customInstructionsPath
              );

              // 确保目录存在 - 使用 FileSystemHelper.resolveUri 构建目录路径
              const agentDefDir = FileSystemHelper.resolveUri(
                workspaceUri,
                path.dirname(modeWithSource.agentDefinitionPath)
              );
              const customInstDir = FileSystemHelper.resolveUri(
                workspaceUri,
                path.dirname(modeWithSource.customInstructionsPath)
              );

              await Promise.all([
                FileSystemHelper.mkdir(agentDefDir, { recursive: true }),
                FileSystemHelper.mkdir(customInstDir, { recursive: true }),
              ]);

              await FileSystemHelper.writeFile(agentDefinitionUri, modeWithSource.agentDefinition || '');

              await FileSystemHelper.writeFile(customInstructionsUri, modeWithSource.customInstructions || '');

              // try {
              //   await FileSystemHelper.readFile(agentDefinitionUri, 'utf-8');

              // } catch {

              // }

              // try {
              //   await FileSystemHelper.readFile(customInstructionsUri, 'utf-8');
              // } catch {

              // }
            } else {
              // 本地环境下使用 fs
              const workspaceRoot = workspaceFolders[0].uri.fsPath;
              const agentDefinitionPath = path.join(workspaceRoot, modeWithSource.agentDefinitionPath);
              const customInstructionsPath = path.join(workspaceRoot, modeWithSource.customInstructionsPath);

              await Promise.all([fs.ensureFile(agentDefinitionPath), fs.ensureFile(customInstructionsPath)]);

              await Promise.all([
                fs.writeFile(agentDefinitionPath, modeWithSource.agentDefinition || ''),
                fs.writeFile(customInstructionsPath, modeWithSource.customInstructions || ''),
              ]);
            }
          }
        } catch (error) {
          console.error('自定义模式关联markdown文件写入失败：', error);
          vscode.window.showErrorMessage(`自定义模式关联markdown文件写入失败`);
        }

        this.clearCache();
        await this.refreshMergedState();
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Failed to update custom mode', { agentId, error: errorMessage });
      vscode.window.showErrorMessage(`更新智能体失败: ${errorMessage}`);
    }
  }

  /**
   * 异步更新指定文件中的 customModes 配置。
   * 根据本地或远程环境，自动处理文件读写与目录创建。
   *
   * @param filePath 文件路径（支持本地与远程）vscode-remote://ssh-remote%2Bproject_715/config/workspace/
   * @param operation 对 customModes 的操作函数
   * @returns Promise<void>
   */
  private async updateModesInFile(filePath: string, operation: (modes: ModeConfig[]) => ModeConfig[]): Promise<void> {
    let content = '{}';

    try {
      // 在远程环境下，使用 FileSystemHelper 来处理文件操作
      if (this.isRemote) {
        try {
          // 在远程环境下，filePath 可能是 URI 字符串，需要转换为 URI 对象
          const uri = FileSystemHelper.getUri(filePath);
          content = await FileSystemHelper.readFile(uri, 'utf-8');
        } catch (error) {
          // 文件不存在时，确保目录存在并创建初始文件
          const uri = FileSystemHelper.getUri(filePath);

          // 获取目录路径并创建目录 URI
          const dirPath = path.dirname(uri.path);
          let dirUri: vscode.Uri;

          if (uri.scheme && uri.authority) {
            // 如果是远程 URI，保持相同的 scheme 和 authority
            dirUri = vscode.Uri.from({
              scheme: uri.scheme,
              authority: uri.authority,
              path: dirPath,
            });
          } else {
            // 本地文件，使用 file scheme
            dirUri = vscode.Uri.file(dirPath);
          }

          // 确保目录存在
          await FileSystemHelper.mkdir(dirUri, { recursive: true });

          // 创建初始文件内容
          content = JSON.stringify(INITIAL_MODE_DATA, null, 2);
        }
      } else {
        // 本地环境下的处理
        try {
          content = await fs.readFile(filePath, 'utf-8');
        } catch (error) {
          // 确保目录存在
          await fs.ensureDir(path.dirname(filePath));
          content = JSON.stringify(INITIAL_MODE_DATA, null, 2);
        }
      }
    } catch (error) {
      console.error(`[CustomModesManager] Failed to read file ${filePath}:`, error);
      content = JSON.stringify(INITIAL_MODE_DATA, null, 2);
    }

    let settings;

    try {
      settings = JSON.parse(content);
    } catch (error) {
      console.error(`[CustomModesManager] Failed to parse JSON from ${filePath}:`, error);
      settings = INITIAL_MODE_DATA;
    }

    settings.customModes = operation(settings.customModes || []);

    // 写入文件
    const newContent = JSON.stringify(settings, null, 2);
    if (this.isRemote) {
      // 在远程环境下，确保使用正确的 URI 对象
      const uri = FileSystemHelper.getUri(filePath);
      await FileSystemHelper.writeFile(uri, newContent);
    } else {
      await fs.writeFile(filePath, newContent, 'utf-8');
    }
  }

  private async refreshMergedState(): Promise<void> {
    const roomodesPath = await this.getWorkspaceRoomodes();
    const roomodesModes = roomodesPath ? await this.loadModesFromFile(roomodesPath) : [];

    // 在远程环境下，只处理项目级别的状态
    if (this.isRemote) {
      await this.context.globalState.update('customModes', roomodesModes);
      this.clearCache();
      await this.onUpdate();
      return;
    }

    // 本地环境下，合并全局和项目配置
    const settingsPath = await this.getCustomModesFilePath();
    const settingsModes = settingsPath ? await this.loadModesFromFile(settingsPath) : [];
    const mergedModes = await this.mergeCustomModes(roomodesModes, settingsModes);

    await this.context.globalState.update('customModes', mergedModes);

    this.clearCache();

    await this.onUpdate();
  }

  public async deleteCustomMode(agentId: string): Promise<void> {
    try {
      const roomodesPath = await this.getWorkspaceRoomodes();
      const roomodesModes = roomodesPath ? await this.loadModesFromFile(roomodesPath) : [];

      // 在远程环境下，只处理项目级别的删除
      if (this.isRemote) {
        const projectMode = roomodesModes.find((m) => m.agentId === agentId);

        if (!projectMode) {
          throw new Error('Write error: Mode not found in project');
        }

        await this.queueWrite(async () => {
          if (roomodesPath) {
            await this.updateModesInFile(FileSystemHelper.getRemotePath(roomodesPath), (modes) =>
              modes.filter((m) => m.agentId !== agentId)
            );
          }

          // Clear cache when modes are deleted
          this.clearCache();
          await this.refreshMergedState();
        });
        return;
      }

      // 本地环境下，处理全局和项目配置的删除
      const settingsPath = await this.getCustomModesFilePath();
      const settingsModes = settingsPath ? await this.loadModesFromFile(settingsPath) : [];

      // Find the mode in either file
      const projectMode = roomodesModes.find((m) => m.agentId === agentId);
      const globalMode = settingsModes.find((m) => m.agentId === agentId);

      if (!projectMode && !globalMode) {
        throw new Error('Write error: Mode not found');
      }

      await this.queueWrite(async () => {
        // Delete from project first if it exists there
        if (projectMode && roomodesPath) {
          await this.updateModesInFile(FileSystemHelper.getRemotePath(roomodesPath), (modes) =>
            modes.filter((m) => m.agentId !== agentId)
          );
        }

        // Delete from global settings if it exists there
        if (globalMode && settingsPath) {
          await this.updateModesInFile(settingsPath, (modes) => modes.filter((m) => m.agentId !== agentId));
        }

        // Clear cache when modes are deleted
        this.clearCache();
        await this.refreshMergedState();
      });
    } catch (error) {
      vscode.window.showErrorMessage(
        `Failed to delete custom mode: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  public async toggleCustomMode(agentId: string, isActive: boolean): Promise<void> {
    // return updateCustomMode(agentId, )
    const currentModeConfig = (await this.getCustomModes()).filter((mode) => {
      return mode.agentId === agentId;
    })[0];

    if (currentModeConfig) {
      currentModeConfig.isActive = isActive;
      return this.updateCustomMode(agentId, currentModeConfig);
    }
    return Promise.resolve();
  }

  private async ensureSettingsDirectoryExists(): Promise<string> {
    const settingsDir = path.join(this.context.globalStorageUri.fsPath, 'settings');
    await fs.mkdir(settingsDir, { recursive: true });
    return settingsDir;
  }

  public async resetCustomModes(): Promise<void> {
    try {
      // 在远程环境下，只重置项目级别的配置
      if (this.isRemote) {
        const roomodesPath = await this.getWorkspaceRoomodes();
        if (roomodesPath) {
          await this.updateModesInFile(FileSystemHelper.getRemotePath(roomodesPath), () => []);
        }
        await this.context.globalState.update('customModes', []);
        this.clearCache();
        await this.onUpdate();
        return;
      }

      // 本地环境下，重置全局配置
      const filePath = await this.getCustomModesFilePath();
      if (filePath) {
        await fs.writeFile(filePath, JSON.stringify(INITIAL_MODE_DATA, null, 2));
      }
      await this.context.globalState.update('customModes', []);
      this.clearCache();
      await this.onUpdate();
    } catch (error) {
      vscode.window.showErrorMessage(
        `Failed to reset custom modes: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private clearCache(): void {
    this.cachedModes = null;
    this.cachedAt = 0;
  }

  dispose(): void {
    for (const disposable of this.disposables) {
      disposable.dispose();
    }

    this.disposables = [];
  }

  // 添加新方法用于检测和加载mode.json文件
  public async checkAndLoadRoomodes(): Promise<ModeConfig[] | null> {
    // 获取mode.json文件路径
    const roomodesPath = await this.getWorkspaceRoomodes();
    if (!roomodesPath) {
      return null;
    }

    // 加载mode.json文件内容
    const roomodesModes = await this.loadModesFromFile(roomodesPath);

    // 在远程环境下，只返回项目级别的配置
    if (this.isRemote) {
      // 更新全局状态和缓存
      await this.context.globalState.update('customModes', roomodesModes);
      this.clearCache();
      await this.onUpdate();

      return roomodesModes;
    }

    // 本地环境下，合并全局和项目配置
    // 加载全局设置文件内容
    const settingsPath = await this.getCustomModesFilePath();
    const settingsModes = settingsPath ? await this.loadModesFromFile(settingsPath) : [];

    // 合并模式配置，mode.json优先
    const mergedModes = await this.mergeCustomModes(roomodesModes, settingsModes);

    // 更新全局状态和缓存
    await this.context.globalState.update('customModes', mergedModes);
    this.clearCache();
    await this.onUpdate();

    return mergedModes;
  }
}
