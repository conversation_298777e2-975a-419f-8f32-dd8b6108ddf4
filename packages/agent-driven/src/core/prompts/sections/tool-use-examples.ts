export function getToolUseExamples(supportsCodebase: boolean, isChat?: boolean): string {
  return '';
  if (isChat) {
    return `# Tool Usage Examples

## Example 1: Creating a New Task

When you need to create a new task or implement a feature, use the following format:

<new_task_creation>
<mode>code</mode>
<message>Implement a new feature for the application.</message>
</new_task_creation>

## Example 2: Web Search for Current Information

When you need to search for up-to-date information from the internet, use this format:

<use_web_search>
<query>current weather in San Francisco</query>
</use_web_search>

${
  supportsCodebase
    ? `
## Example 3: Codebase Search

When you need to search within the codebase for specific implementations or code patterns, use this format:

<use_codebase>
<query>implementation of authentication middleware in Express</query>
</use_codebase>`
    : ''
}`;
  }
  return `# Tool Usage Examples

This section provides comprehensive examples of how to properly use various tools and commands. Each example demonstrates the correct syntax and structure for different operations.

## Example 1: Command Execution

Use this format to execute system commands:

<use_command>
<command>npm run dev</command>
<requires_approval>false</requires_approval>
</use_command>

## Example 2: File Writing Operations

Use this format to create or overwrite files with new content:

<use_write_file>
<path>src/frontend-config.json</path>
<content>
{
  "apiEndpoint": "https://api.example.com",
  "theme": {
    "primaryColor": "#007bff",
    "secondaryColor": "#6c757d",
    "fontFamily": "Arial, sans-serif"
  },
  "features": {
    "darkMode": true,
    "notifications": true,
    "analytics": false
  },
  "version": "1.0.0"
}
</content>
<line_count>14</line_count>
</use_write_file>

## Example 3: Task Creation and Context Documentation

Use this structured format to create comprehensive task documentation:

<new_task_creation>
<context>
1. Current Work:
  [Provide detailed description of current work status]

2. Key Technical Concepts:
  - [Technical concept 1 with brief explanation]
  - [Technical concept 2 with brief explanation]
  - [Additional concepts as needed]

3. Relevant Files and Code:
  - [File Name 1]
      - [Explanation of file's importance to the project]
      - [Summary of modifications made, if applicable]
      - [Critical code snippets or references]
  - [File Name 2]
      - [Important code snippets or functionality]
  - [Additional files as needed]

4. Problem Solving:
  [Detailed description of challenges addressed and solutions implemented]

5. Pending Tasks and Next Steps:
  - [Task 1: specific details and recommended next actions]
  - [Task 2: specific details and recommended next actions]
  - [Additional tasks as needed]
</context>
</new_task_creation>

## Example 4: MCP Tool Usage

Use this format to interact with Model Context Protocol (MCP) tools:

<use_mcp_tools>
<server_name>weather-server</server_name>
<tool_name>get_forecast</tool_name>
<arguments>
{
  "city": "San Francisco",
  "days": 5
}
</arguments>
</use_mcp_tools>

## Example 5: MCP Resource Access

Use this format to access MCP resources via URI:

<get_mcp_resource>
<server_name>weather-server</server_name>
<uri>weather://san-francisco/current</uri>
</get_mcp_resource>

## Example 6: MCP Tool with URL-based Server Identifier

Use this format when the server name is a unique identifier such as a URL:

<use_mcp_tools>
<server_name>github.com/modelcontextprotocol/servers/tree/main/src/github</server_name>
<tool_name>create_issue</tool_name>
<arguments>
{
  "owner": "octocat",
  "repo": "hello-world",
  "title": "Found a bug",
  "body": "I'm having a problem with this.",
  "labels": ["bug", "help wanted"],
  "assignees": ["octocat"]
}
</arguments>
</use_mcp_tools>

## Example 7: Web Search Operations

Use this format to search for current information from the internet:

<use_web_search>
<query>current weather in San Francisco</query>
</use_web_search>

${
  supportsCodebase
    ? `
## Example 8: Codebase Search Operations

Use this format to search within the project codebase:

<use_codebase>
<query>implementation of authentication middleware in Express</query>
</use_codebase>`
    : ``
}
`;
}
