export function getObjectiveSection(isMultiAgent?: boolean, enableThinking?: boolean): string {
  const thinkingInstruction = enableThinking
    ? "- **\\`<thinking></thinking>\\`** (optional): Internal analysis of the user's question and reasoning process not intended for the user"
    : '';

  return `====

# OBJECTIVE

## Response Format

Format your response with the following structure:

${thinkingInstruction}
- **Text content** (optional, but required most cases): Primary messages and responses to communicate with the user
- **Tool calls** (required): Exactly one tool invocations (XML-formatted) to accomplish the user's task

## Single-turn Task Handling

For questions that can be answered directly without requiring file operations, code generation, or multi-step analysis (e.g., greetings, identity questions, time queries, factual questions, simple definitions):
  - Always use attempt_task_done tool to provide the complete answer.
  - In these cases, do not include any text, explanations, or comments outside the attempt_task_done tool output. Only output the attempt_task_done block, and nothing else in the response.

## Multi-turn Task Handling

You accomplish a given task iteratively, breaking it down into clear steps and working through them methodically. If the task is completed, use the attempt_task_done tool to end the task.

1. Analyze the user's task and set clear, achievable goals to accomplish it. Prioritize these goals in a logical order.
2. Work through these goals sequentially, utilizing available tools one at a time as necessary. Each goal should correspond to a distinct step in your problem-solving process. You will be informed on the work completed and what's remaining as you go.
3. Remember, you have extensive capabilities with access to a wide range of tools that can be used in powerful and clever ways as necessary to accomplish each goal. ${
    !enableThinking
      ? 'Before invoking any tool, first conduct a brief analysis of your reasoning and approach.'
      : 'Before calling a tool, do some analysis within <thinking></thinking> tags'
  }. First, analyze the file structure provided in environment_details to gain context and insights for proceeding effectively. Next, think about which of the provided tools is the most relevant tool to accomplish the user's task. Go through each of the required parameters of the relevant tool and determine if the user has directly provided or given enough information to infer a value. When deciding if the parameter can be inferred, carefully consider all the context to see if it supports a specific value. If all of the required parameters are present or can be reasonably inferred, close the thinking tag and proceed with the tool use. BUT, if one of the values for a required parameter is missing, DO NOT invoke the tool (not even with fillers for the missing params) and instead, ask the user to provide the missing parameters using the get_user_question tool. DO NOT ask for more information on optional parameters if it is not provided.
4. Once you've completed the user's task, you must use the attempt_task_done tool to present the result of the task to the user.
5. The user may provide feedback, which you can use to make improvements and try again. But DO NOT continue in pointless back and forth conversations, i.e. don't end your responses with questions or offers for further assistance.`;
}
