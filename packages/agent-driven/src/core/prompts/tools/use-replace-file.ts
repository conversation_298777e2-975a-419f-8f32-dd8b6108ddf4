import { ToolArgs } from './types';

export function getUseReplaceFileDescription(args: ToolArgs, isSearchReplace?: boolean) {
  if (isSearchReplace) {
    return `## use_search_and_replace
Description: Use this tool to find and replace specific text strings or patterns (using regex) within a file. It's suitable for targeted replacements across multiple locations within the file. Supports literal text and regex patterns, case sensitivity options, and optional line ranges. Shows a diff preview before applying changes.

**REQUIRED PARAMETERS:**
- path: The path of the file to modify (relative to the current workspace directory ${args.cwd.toPosix()})
- search: The text or pattern to search for
- replace: The text to replace matches with


Optional Parameters:
- start_line: Starting line number for restricted replacement (1-based)
- end_line: Ending line number for restricted replacement (1-based)
- use_regex: Set to "true" to treat search as a regex pattern (default: false)
- ignore_case: Set to "true" to ignore case when matching (default: false)

Notes:
- When use_regex is true, the search parameter is treated as a regular expression pattern
- When ignore_case is true, the search is case-insensitive regardless of regex mode


**Critical Guidelines for Large Content:**
- **Search Content**: Keep search text concise but unique. If content is too long, use distinctive patterns or unique identifiers
- **Replace Content**: For large replacements, break into smaller chunks or use multiple operations
- **Content Preservation**: If search/replace content exceeds ~500 characters, consider using line ranges to limit scope
- **Verification Strategy**: Always use unique markers or patterns in search to ensure exact matching
- **Fallback Approach**: For very large content blocks, consider using use_write_file for complete file rewrites instead

Usage:

<use_search_and_replace>
<path>File path here</path>
<search>Text or pattern to search for</search>
<replace>Text to replace matches with</replace>
<start_line>Starting line number (optional)</start_line>
<end_line>Ending line number (optional)</end_line>
<use_regex>true/false (optional, default: false)</use_regex>
<ignore_case>true/false (optional, default: true)</ignore_case>
</use_search_and_replace>

Examples:

1. Simple text replacement:
<use_search_and_replace>
<path>example.ts</path>
<search>oldText</search>
<replace>newText</replace>
</use_search_and_replace>

2. Regex pattern with capture groups:
<use_search_and_replace>
<path>example.ts</path>
<search>old\w+</search>
<replace>new$&</replace>
<use_regex>true</use_regex>
<ignore_case>true</ignore_case>
</use_search_and_replace>

3. Range-limited replacement in large files:
<use_search_and_replace>
<path>example.ts</path>
<search>console.log(.*)</search>
<replace>// console.log(commented out)</replace>
<start_line>10</start_line>
<end_line>20</end_line>
<use_regex>true</use_regex>
</use_search_and_replace>

4. Handling large content with unique markers:
<use_search_and_replace>
<path>example.ts</path>
<search>// START: user authentication logic</search>
<replace>// START: updated user authentication logic</replace>
<start_line>50</start_line>
<end_line>100</end_line>
</use_search_and_replace>

5. Using distinctive patterns for precise matching:
<use_search_and_replace>
<path>example.ts</path>
<search>export class UserService {</search>
<replace>export class EnhancedUserService {</replace>
</use_search_and_replace>

`;
  }
  return ``;
}
