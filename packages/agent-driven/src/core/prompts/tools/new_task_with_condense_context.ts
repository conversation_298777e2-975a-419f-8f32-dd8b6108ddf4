import { ToolArgs } from './types';

export function getNewTaskWithContextTool(_args: ToolArgs): string {
  return `
## new_task_with_condense_context
Description: Request to create a new task with preloaded context covering the conversation with the user up to this point and key information for continuing with the new task. With this tool, you will create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions, with a focus on the most relevant information required for the new task.
Among other important areas of focus, this summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the new task. The user will be presented with a preview of your generated context and can choose to create a new task or keep chatting in the current conversation. The user may choose to start a new task at any point.

Parameters:
- Context: (required) The context to preload the new task with. If applicable based on the current task, this should include:
  1. Current Work: Describe in detail what was being worked on prior to this request to create a new task. Pay special attention to the more recent messages / conversation.
  2. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for the new task.
  3. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.
  4. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.
  5. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks. It's important to be detailed here.

Usage:
<new_task_with_condense_context>
<context>context to preload new task with</context>
</new_task_with_condense_context>

Example:
<new_task_with_condense_context>
<context>
## Current Work
The user and I were working on implementing a React-based dashboard application with real-time data visualization. We had just completed the main dashboard component and were in the process of adding authentication functionality using JWT tokens.

## Key Technical Concepts
- React 18 with functional components and hooks
- TypeScript for type safety
- Material-UI (MUI) for component library
- JWT authentication with refresh token mechanism
- WebSocket connections for real-time data updates
- Redux Toolkit for state management
- Axios for HTTP requests
- Chart.js for data visualization

## Relevant Files and Code
- \`src/components/Dashboard.tsx\` - Main dashboard component with data grid and charts
- \`src/auth/AuthContext.tsx\` - Authentication context provider
- \`src/services/api.ts\` - API service layer with JWT token handling
- \`src/types/user.ts\` - User and authentication type definitions
- \`package.json\` - Dependencies including @mui/material, @reduxjs/toolkit, chart.js

## Problem Solving
- Resolved CORS issues by configuring proxy in package.json
- Fixed TypeScript compilation errors related to chart component props
- Implemented token refresh logic to handle expired JWT tokens automatically

## Pending Tasks and Next Steps
The user explicitly requested: "Now we need to add role-based access control to restrict certain dashboard features based on user permissions."

Next steps I was planning to take:
1. Create a permissions system with role definitions (admin, user, viewer)
2. Modify the AuthContext to include user roles and permissions
3. Create a PermissionGuard component to wrap protected features
4. Update the Dashboard component to conditionally render features based on permissions

The user's exact words were: "I want admins to see everything, regular users to see most features but not user management, and viewers to only see read-only data." This indicates we need three permission levels with specific feature restrictions.
</context>
</new_task_with_condense_context>
`;
}
