import { McpHub } from '../../../services/mcp/McpHub';
import { BrowserSettings } from '../../../shared/BrowserSettings';
import { DiffStrategy } from '../../../shared/tools';

export interface ToolArgs {
  cwd: string;
  supportsComputerUse: boolean;
  supportsCodebase?: boolean;
  browserSettings?: BrowserSettings;
  mcpHub?: McpHub;
  diffStrategy?: DiffStrategy;
  partialReadsEnabled?: boolean;
  maxConcurrentFileReads?: number | null;
  webSearchEnabled?: boolean;
  toolOptions?: any;
}
