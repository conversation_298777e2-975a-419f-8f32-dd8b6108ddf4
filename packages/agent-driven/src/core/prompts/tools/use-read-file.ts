import { ToolArgs } from './types';

export function getUseReadFileDescription(args: ToolArgs): string {
  const maxConcurrentReads = args?.maxConcurrentFileReads ?? 1;
  const isMultipleReadsEnabled = maxConcurrentReads > 1;

  return `## use_read_file
Description: Request to read the contents of ${
    isMultipleReadsEnabled ? 'one or more files' : 'a file'
  }. The tool outputs line-numbered content (e.g. "1 | const x = 1") for easy reference when creating diffs or discussing code.${
    args.partialReadsEnabled ? ' Use line ranges to efficiently read specific portions of large files.' : ''
  } Supports text extraction from PDF and DOCX files, but may not handle other binary files properly.

${
  isMultipleReadsEnabled
    ? `**IMPORTANT: You can read a maximum of ${maxConcurrentReads} files in a single request.** If you need to read more files, use multiple sequential use_read_file requests.`
    : '**IMPORTANT: Multiple file reads are currently disabled. You can only read one file at a time.**'
}

${
  args.partialReadsEnabled
    ? `By specifying line ranges, you can efficiently read specific portions of large files without loading the entire file into memory.`
    : ''
}
Parameters:
- args: Contains one or more file elements, where each file contains:
  - path: (required) File path (relative to workspace directory ${args.cwd})
  ${
    args.partialReadsEnabled
      ? `- line_range: (optional) One or more line range elements in format "start-end" (1-based, inclusive)`
      : ''
  }

Usage:
<use_read_file>
<args>
  <file>
    <path>path/to/file</path>
    ${args.partialReadsEnabled ? `<line_range>start-end</line_range>` : ''}
  </file>
</args>
</use_read_file>

Examples:

1. Reading a single file:
<use_read_file>
<args>
  <file>
    <path>src/app.ts</path>
    ${args.partialReadsEnabled ? `<line_range>1-1000</line_range>` : ''}
  </file>
</args>
</use_read_file>

${isMultipleReadsEnabled ? `2. Reading multiple files (within the ${maxConcurrentReads}-file limit):` : ''}${
    isMultipleReadsEnabled
      ? `
<use_read_file>
<args>
  <file>
    <path>src/app.ts</path>
    ${
      args.partialReadsEnabled
        ? `<line_range>1-50</line_range>
    <line_range>100-150</line_range>`
        : ''
    }
  </file>
  <file>
    <path>src/utils.ts</path>
    ${args.partialReadsEnabled ? `<line_range>10-20</line_range>` : ''}
  </file>
</args>
</use_read_file>`
      : ''
  }

${isMultipleReadsEnabled ? '3. ' : '2. '}Reading an entire file:
<use_read_file>
<args>
  <file>
    <path>config.json</path>
  </file>
</args>
</use_read_file>

IMPORTANT: You MUST use this Efficient Reading Strategy:
- ${
    isMultipleReadsEnabled
      ? `You MUST read all related files and implementations together in a single operation (up to ${maxConcurrentReads} files at once)`
      : 'You MUST read files one at a time, as multiple file reads are currently disabled'
  }
- You MUST obtain all necessary context before proceeding with changes
${
  args.partialReadsEnabled
    ? `- You MUST use line ranges to read specific portions of large files, rather than reading entire files when not needed
- You MUST combine adjacent line ranges (<10 lines apart)
- You MUST use multiple ranges for content separated by >10 lines
- You MUST include sufficient line context for planned modifications while keeping ranges minimal
`
    : ''
}
${
  isMultipleReadsEnabled
    ? `- When you need to read more than ${maxConcurrentReads} files, prioritize the most critical files first, then use subsequent use_read_file requests for additional files`
    : ''
}`;
  //   return `## use_read_file
  // Description: Read file contents from the specified path. Use this to examine existing files when you need to analyze code, review documents, extract configuration data, or inspect any text-based content. Supports automatic text extraction from PDF and DOCX files. Not recommended for binary files.
  // Parameters:
  // - path: (required) File path relative to current working directory ${args.cwd}
  // - start_line: (optional) Starting line number (1-based indexing). Omit to start from beginning
  // - end_line: (optional) Ending line number (1-based, inclusive). Omit to read until end
  // Usage:
  // <use_read_file>
  // <path>File path here</path>
  // <start_line>Starting line number (optional)</start_line>
  // <end_line>Ending line number (optional)</end_line>
  // </use_read_file>
  // Examples:
  // 1. Reading an entire file:
  // <use_read_file>
  // <path>frontend-config.json</path>
  // </use_read_file>
  // 2. Reading the first 1000 lines of a large log file:
  // <use_read_file>
  // <path>logs/application.log</path>
  // <end_line>1000</end_line>
  // </use_read_file>
  // 3. Reading lines 500-1000 of a CSV file:
  // <use_read_file>
  // <path>data/large-dataset.csv</path>
  // <start_line>500</start_line>
  // <end_line>1000</end_line>
  // </use_read_file>
  // 4. Reading a specific function in a source file:
  // <use_read_file>
  // <path>src/app.ts</path>
  // <start_line>46</start_line>
  // <end_line>68</end_line>
  // </use_read_file>
  // Note: Line range parameters enable efficient streaming for large files, processing only requested sections without memory overhead. Ideal for logs, datasets, and large text files.`;
}
