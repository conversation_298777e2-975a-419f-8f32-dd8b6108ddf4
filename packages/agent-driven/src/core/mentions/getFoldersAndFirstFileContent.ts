import { Anthropic } from '@anthropic-ai/sdk';
import { isBinaryFile } from 'isbinaryfile';
import * as vscode from 'vscode';
import { FileSystemHelper } from '../../utils/FileSystemHelper';
import { extractTextFromFile } from '../../integrations/misc/extract-text';
import { countTokens } from '../../utils/countTokens';

const MAX_FILE_TOKENS = 6400;

/**
 * 获取指定路径下所有文件夹地址和每个文件夹下第一个文件的内容
 * @param rootPath 根路径
 * @param cwd 当前工作目录
 * @returns 包含文件夹地址、第一个文件内容和token数的结果
 */
export async function getFoldersAndFirstFileContent(
  rootPath: string,
  cwd: string | vscode.Uri
): Promise<{
  folderPaths: string[];
  firstFileContents: Record<string, string>;
  fileTokenCounts: Record<string, number>;
  tokenCount: number;
  folderFiles: Record<string, string[]>;
}> {
  const absPath = FileSystemHelper.resolveUri(cwd, rootPath);
  const folderPaths: string[] = [];
  const firstFileContents: Record<string, string> = {};
  const folderFiles: Record<string, string[]> = {};
  const fileTokenCounts: Record<string, number> = {};

  try {
    const stats = await FileSystemHelper.stat(absPath);

    if (!stats.isDirectory()) {
      return {
        folderPaths,
        firstFileContents,
        fileTokenCounts,
        tokenCount: 0,
        folderFiles,
      };
    }

    // 递归处理文件夹结构
    await processFolderStructure(rootPath, absPath, folderPaths, firstFileContents, folderFiles);

    // 处理文件内容的token计数和截断
    await processFileTokens(firstFileContents, fileTokenCounts);

    // 计算总token数
    const tokenCount = await calculateTotalTokens(folderPaths, firstFileContents);

    return {
      folderPaths,
      firstFileContents,
      fileTokenCounts,
      tokenCount,
      folderFiles,
    };
  } catch (error) {
    console.error(`获取文件夹和文件内容时出错: ${error.message}`);
    return {
      folderPaths,
      firstFileContents,
      fileTokenCounts,
      tokenCount: 0,
      folderFiles,
    };
  }
}

/**
 * 递归处理文件夹结构，收集文件夹路径和第一个文件内容
 */
async function processFolderStructure(
  relativePath: string,
  absolutePath: string | vscode.Uri,
  folderPaths: string[],
  firstFileContents: Record<string, string>,
  folderFiles: Record<string, string[]>
): Promise<void> {
  try {
    // 添加当前文件夹路径
    folderPaths.push(relativePath);
    folderFiles[relativePath] = [];

    // 读取文件夹内容
    const entries = await FileSystemHelper.readdir(absolutePath, { withFileTypes: true });

    // 处理文件
    const fileProcessingPromises: Promise<void>[] = [];

    // 首先处理所有文件
    for (const entry of entries) {
      if (entry.isFile()) {
        const filePath = FileSystemHelper.join(relativePath, entry.name);
        const absoluteFilePath = FileSystemHelper.resolveUri(absolutePath, entry.name);

        // 添加文件路径到当前文件夹的文件列表中
        folderFiles[relativePath].push(filePath);

        // 如果还没有获取第一个文件的内容，则处理这个文件
        if (!firstFileContents[relativePath]) {
          fileProcessingPromises.push(
            (async () => {
              try {
                const isBinary = await isBinaryFile(FileSystemHelper.getRemotePath(absoluteFilePath)).catch(
                  () => false
                );
                if (!isBinary) {
                  const content = await extractTextFromFile(absoluteFilePath);
                  firstFileContents[relativePath] = content;
                }
              } catch (error) {
                console.error(`处理文件 "${filePath}" 时出错: ${error.message}`);
                firstFileContents[relativePath] = '';
              }
            })()
          );
        }
      }
    }

    // 等待所有文件处理完成
    await Promise.all(fileProcessingPromises);

    // 如果没有找到任何文件
    if (!firstFileContents[relativePath]) {
      firstFileContents[relativePath] = '';
    }

    // 递归处理子文件夹
    const folderProcessingPromises: Promise<void>[] = [];
    for (const entry of entries) {
      if (entry.isDirectory()) {
        const subFolderPath = FileSystemHelper.join(relativePath, entry.name);
        const absoluteSubFolderPath = FileSystemHelper.resolveUri(absolutePath, entry.name);

        folderProcessingPromises.push(
          processFolderStructure(subFolderPath, absoluteSubFolderPath, folderPaths, firstFileContents, folderFiles)
        );
      }
    }

    // 等待所有子文件夹处理完成
    await Promise.all(folderProcessingPromises);
  } catch (error) {
    console.error(`处理文件夹 "${relativePath}" 时出错: ${error.message}`);
    // 继续处理其他文件夹，不中断整个过程
  }
}

/**
 * 处理文件内容的token计数和截断
 */
async function processFileTokens(
  firstFileContents: Record<string, string>,
  fileTokenCounts: Record<string, number>
): Promise<void> {
  for (const [folderPath, content] of Object.entries(firstFileContents)) {
    // 计算当前内容的token数
    const fileContentBlock: Anthropic.Messages.ContentBlockParam = {
      type: 'text',
      text: content,
    };
    let tokenCount = await countTokens([fileContentBlock]);
    fileTokenCounts[folderPath] = tokenCount;

    // 如果超过最大限制，则截断内容
    if (tokenCount > MAX_FILE_TOKENS) {
      // 估算需要保留的内容比例
      const ratio = MAX_FILE_TOKENS / tokenCount;
      // 保守估计，取稍小一点的比例，避免多次截断
      const safeRatio = ratio * 0.9;
      const truncatedLength = Math.floor(content.length * safeRatio);

      // 截断内容
      const truncatedContent =
        content.substring(0, truncatedLength) + `\n\n... [内容已截断，超过 ${MAX_FILE_TOKENS} tokens 限制]`;

      // 更新内容和token数
      firstFileContents[folderPath] = truncatedContent;

      // 重新计算截断后的token数
      const truncatedBlock: Anthropic.Messages.ContentBlockParam = {
        type: 'text',
        text: truncatedContent,
      };
      fileTokenCounts[folderPath] = await countTokens([truncatedBlock]);
    }
  }
}

/**
 * 计算总token数
 */
async function calculateTotalTokens(folderPaths: string[], firstFileContents: Record<string, string>): Promise<number> {
  const contentBlocks: Anthropic.Messages.ContentBlockParam[] = [];

  // 将文件夹路径添加到内容块
  const folderPathsText = folderPaths.join('\n');
  contentBlocks.push({
    type: 'text',
    text: `文件夹路径:\n${folderPathsText}`,
  });

  // 将每个文件夹的第一个文件内容添加到内容块
  for (const [folderPath, content] of Object.entries(firstFileContents)) {
    contentBlocks.push({
      type: 'text',
      text: `文件夹: ${folderPath}\n第一个文件内容:\n${content}`,
    });
  }

  // 计算总token数
  return await countTokens(contentBlocks);
}
