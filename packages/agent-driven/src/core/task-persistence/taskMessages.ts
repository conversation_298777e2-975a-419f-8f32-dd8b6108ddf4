import * as path from 'path';
import * as fs from 'fs/promises';

import { fileExistsAtPath } from '../../utils/fs';

import { getTaskDirectoryPath } from '../../utils/storage';
import { JoyCoderMessage } from '../../shared/ExtensionMessage';
import { safeWriteJson } from '../../utils/safeWriteJson';
import { GlobalFileNames } from '../storage/disk';

export type ReadTaskMessagesOptions = {
  taskId: string;
  globalStoragePath: string;
};

export async function readTaskMessages({
  taskId,
  globalStoragePath,
}: ReadTaskMessagesOptions): Promise<JoyCoderMessage[]> {
  const taskDir = await getTaskDirectoryPath(globalStoragePath, taskId);
  const filePath = path.join(taskDir, GlobalFileNames.uiMessages);
  const fileExists = await fileExistsAtPath(filePath);

  if (fileExists) {
    return JSON.parse(await fs.readFile(filePath, 'utf8'));
  }

  return [];
}

export type SaveTaskMessagesOptions = {
  messages: JoyCoderMessage[];
  taskId: string;
  globalStoragePath: string;
};

export async function saveTaskMessages({ messages, taskId, globalStoragePath }: SaveTaskMessagesOptions) {
  const taskDir = await getTaskDirectoryPath(globalStoragePath, taskId);
  const filePath = path.join(taskDir, GlobalFileNames.uiMessages);
  await safeWriteJson(filePath, messages);
}
