import '../utils/path'; // Import to ensure String.prototype.toPosix is available
import { Anthropic } from '@anthropic-ai/sdk';
import { setTimeout as setTimeoutPromise } from 'node:timers/promises';
import delay from 'delay';
import getFolderSize from 'get-folder-size';
import fs from 'fs/promises';
import os from 'os';
// import { Readable } from 'stream';
import { createWriteStream } from 'fs';

import pWaitFor from 'p-wait-for';
import * as path from 'path';
import { serializeError } from 'serialize-error';
import * as vscode from 'vscode';
import { v4 as uuidv4 } from 'uuid';
import { ApiHandler, buildApiHandler } from '../adaptor/api';
import { ApiStream } from '../adaptor/api/transform/stream';
import CheckpointTracker from '../integrations/checkpoints/CheckpointTracker';
import { DIFF_VIEW_URI_SCHEME, DiffViewProvider } from '../integrations/editor/DiffViewProvider';
import { findToolName } from '../integrations/misc/export-markdown';

import { TerminalManager } from '../integrations/terminal/TerminalManager';
import { BrowserSession } from '../services/browser/BrowserSessionPlaywright';
import { UrlContentFetcher } from '../services/browser/UrlContentFetcherPlaywright';
import { ApiConfiguration } from '../shared/api';
import { findLast, findLastIndex } from '../shared/array';
import {
  AUTO_APPROVAL_SETTINGS,
  AutoApprovalSettings,
  DEFAULT_AUTO_APPROVAL_SETTINGS,
} from '../shared/AutoApprovalSettings';
import { BrowserSettings } from '../shared/BrowserSettings';
import { combineApiRequests } from '../shared/combineApiRequests';
import { combineCommandSequences } from '../shared/combineCommandSequences';
import { JoyCoderApiReqInfo, JoyCoderAsk, JoyCoderMessage, JoyCoderSay } from '../shared/ExtensionMessage';
import { getApiMetrics } from '../shared/getApiMetrics';
import { HistoryItem } from '../shared/HistoryItem';
import { JoyCoderAskResponse, JoyCoderCheckpointRestore } from '../shared/WebviewMessage';
import { fileExistsAtPath, isRemoteEnvironment } from '../utils/fs';
import { AssistantMessageContent, ToolUseName } from './assistant-message';
import { JoyCoderIgnoreController, LOCK_TEXT_SYMBOL } from './ignore/JoycoderIgnoreController';
import { parseMentions } from './mentions';
import { formatResponse } from './prompts/responses';
import { addUserInstructions, SYSTEM_PROMPT } from './prompts/system';
import { truncateConversationIfNeeded } from './sliding-window';
import { JoyCoderProvider } from './webview/JoycoderProvider';
import { FileContextTracker } from './context-tracking/FileContextTracker';
import { ContextManager } from './context-management/ContextManager';
import { JoyCoderRestoreMessageMap, sayMissingParamError } from '../adaptor/translate/message';
import { getVscodeConfig, GlobalState, Logger, WorkspaceState } from '@joycoder/shared';
import { McpHub } from '../services/mcp/McpHub';
import { minimatch } from 'minimatch';
import { GlobalFileNames } from './storage/disk';
import { checkIsOpenRouterContextWindowError } from './context-management/context-error-handling';
import { ContextCondense, TokenUsage, ToolProgressStatus } from '../schemas';
import { MultiSearchReplaceDiffStrategy } from './diff/strategies/multi-search-replace';
import { DiffStrategy } from '../shared/tools';
import { defaultModeSlug } from '../../web-agent/src/utils/modes';
import { mergePromise, TerminalProcess } from '../integrations/terminal/TerminalProcess';
import { recursivelyMakeJoyCoderRequests } from './JoyCoder/recursivelyMakeJoyCoderRequests';
import { getEnvironmentDetails } from './JoyCoder/getEnvironmentDetails';
import { FileSystemHelper } from '../utils/FileSystemHelper';
import { parseSlashCommands } from './slash-commands';
import { getChatModelAndConfig } from '@joycoder/plugin-base-ai/src/model';
import { ModelContextTracker } from './context-tracking/ModelContextTracker';
import { getContextWindowInfo } from './context-management/context-window-utils';
import { maybeRemoveImageBlocks } from '../adaptor/api/transform/image-cleaning';
import { getMessagesSinceLastSummary } from './condense';
import { ApiMessage } from './task-persistence/apiMessages';
import { pushToolResult, toolDescription } from './JoyCoder/tools/common';
import { TodoItem } from '../shared/todo';

const cwd =
  vscode.workspace.workspaceFolders?.map((folder) => (isRemoteEnvironment() ? folder.uri : folder.uri.fsPath)).at(0) ??
  path.join(os.homedir(), 'Desktop'); // may or may not exist but fs checking existence would immediately ask for permission which would be bad UX, need to come up with a better solution

/**
 * 获取工作空间的显示名称，优先显示项目名称而不是远程路径
 */
function getWorkspaceDisplayName(cwd: string | vscode.Uri): string {
  // 获取工作空间文件夹信息
  const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
  if (workspaceFolder) {
    // 优先使用工作空间文件夹的名称
    return workspaceFolder.name;
  }

  // 如果没有工作空间文件夹，尝试从路径中提取项目名称
  const pathStr = cwd instanceof vscode.Uri ? cwd.path : cwd;
  const segments = pathStr.split('/').filter(Boolean);
  if (segments.length > 0) {
    return segments[segments.length - 1]; // 返回最后一个路径段作为项目名称
  }

  // 最后的备选方案
  return FileSystemHelper.getRemotePath(cwd).toPosix();
}

type ToolResponse = string | Array<Anthropic.TextBlockParam | Anthropic.ImageBlockParam>;
export type UserContent = Anthropic.Messages.ContentBlockParam[];

export class JoyCoder {
  readonly taskId: string;
  readonly instanceId: string;
  private taskIsFavorited?: boolean;
  readonly rootTask: JoyCoder | undefined = undefined;
  readonly parentTask: JoyCoder | undefined = undefined;
  readonly taskNumber: number;
  readonly cwd: string | vscode.Uri = cwd;
  api: ApiHandler;
  terminalManager: TerminalManager;
  private urlContentFetcher: UrlContentFetcher;
  browserSession: BrowserSession;
  contextManager: ContextManager;
  didEditFile: boolean = false;
  customInstructions?: string;
  autoApprovalSettings: AutoApprovalSettings;
  private globalAutoExecute: boolean = false;
  private browserSettings: BrowserSettings;
  apiConversationHistory: ApiMessage[] = [];
  JoyCoderMessages: JoyCoderMessage[] = [];
  JoyCoderIgnoreController: JoyCoderIgnoreController;
  private askResponse?: JoyCoderAskResponse;
  askResponseText?: string;
  askResponseImages?: string[];
  private lastMessageTs?: number;
  consecutiveAutoApprovedRequestsCount: number = 0;
  consecutiveMistakeCount: number = 0;
  providerRef: WeakRef<JoyCoderProvider>;
  abort: boolean = false;
  didFinishAbortingStream = false;
  abandoned = false;
  diffViewProvider: DiffViewProvider;
  checkpointTracker?: CheckpointTracker;
  checkpointTrackerErrorMessage?: string;
  conversationHistoryDeletedRange?: [number, number];
  isInitialized = false;
  isAwaitingPlanResponse = false;
  didRespondToPlanAskBySwitchingMode = false;

  isPaused: boolean = false;
  pausedModeSlug: string = defaultModeSlug;

  // File tracking
  fileContextTracker: FileContextTracker;

  // streaming
  isWaitingForFirstChunk = false;
  isStreaming = false;
  currentStreamingContentIndex = 0;
  assistantMessageContent: AssistantMessageContent[] = [];
  presentAssistantMessageLocked = false;
  presentAssistantMessageHasPendingUpdates = false;
  userMessageContent: (Anthropic.TextBlockParam | Anthropic.ImageBlockParam)[] = [];
  userMessageContentReady = false;
  didRejectTool = false;
  didAlreadyUseTool = false;
  didCompleteReadingStream = false;
  didAutomaticallyRetryFailedApiRequest = false;
  public currentFilePath = ''; //当前读取或修改的文件
  fuzzyMatchThreshold: number;
  diffStrategy: DiffStrategy;
  consecutiveMistakeCountForApplyDiff: Map<string, number> = new Map();

  terminalProcess: TerminalProcess | undefined;

  private pauseInterval: NodeJS.Timeout | undefined;
  modelContextTracker: ModelContextTracker;

  isWritingHistory = false;

  todoList: TodoItem[] = [];

  // reportAction
  // taskId 已经定义过
  sessionId: string = ''; //单轮对话ID
  conversationId: string = ''; //${taskId}-${sessionId}
  newSession() {
    if (!this.taskId) throw new Error('[JoyCode数据上报] newSession Error: JoyCode.taskId is not defined');
    this.sessionId = `session-${uuidv4().split('-')[0]}`;
    this.conversationId = `${this.taskId}_${this.sessionId}`;
  }

  constructor(
    provider: JoyCoderProvider,
    apiConfiguration: ApiConfiguration,
    autoApprovalSettings: AutoApprovalSettings,
    browserSettings: BrowserSettings,
    customInstructions?: string,
    task?: string,
    images?: string[],
    historyItem?: HistoryItem,
    fuzzyMatchThreshold = 1.0,
    rootTask?: JoyCoder,
    parentTask?: JoyCoder,
    taskNumber = -1
  ) {
    this.JoyCoderIgnoreController = new JoyCoderIgnoreController(FileSystemHelper.getRemotePath(cwd));
    this.JoyCoderIgnoreController.initialize().catch((error: any) => {
      console.error('Failed to initialize JoyCoderIgnoreController:', error);
    });
    this.providerRef = new WeakRef(provider);
    this.api = buildApiHandler(apiConfiguration);
    this.terminalManager = new TerminalManager();
    this.terminalManager.setShellIntegrationTimeout(4000);
    this.urlContentFetcher = new UrlContentFetcher(provider.context);
    this.browserSession = new BrowserSession(provider.context, browserSettings);
    this.contextManager = new ContextManager();
    this.diffViewProvider = new DiffViewProvider(FileSystemHelper.getRemotePath(cwd));
    this.customInstructions = customInstructions;
    this.autoApprovalSettings = autoApprovalSettings;
    this.browserSettings = browserSettings;
    // 初始化工作区 autoExecute 状态
    this.initializeWorkspaceAutoExecute();
    this.fuzzyMatchThreshold = fuzzyMatchThreshold;

    this.rootTask = rootTask;
    this.parentTask = parentTask;
    this.taskNumber = taskNumber;

    if (historyItem) {
      this.taskId = historyItem.id;
      this.taskIsFavorited = historyItem.isFavorited;
      // console.log(`[JoyCoder数据上报] Task resumued!\ntaskId=${this.taskId}`);
      this.conversationHistoryDeletedRange = historyItem.conversationHistoryDeletedRange;
      this.resumeTaskFromHistory();
    } else if (task || images) {
      // this.taskId = Date.now().toString();
      this.taskId = `task-${uuidv4()}`;
      // console.log(`[JoyCoder数据上报] Task created!\ntaskId=${this.taskId}`);
      this.startTask(task, images);
    } else {
      throw new Error('必须提供 历史记录 或 问答内容（图片） 中的一项');
    }
    // Initialize file context tracker
    this.fileContextTracker = new FileContextTracker(provider, this.taskId);
    this.modelContextTracker = new ModelContextTracker(provider.context, this.taskId);
    this.diffStrategy = new MultiSearchReplaceDiffStrategy(this.fuzzyMatchThreshold);
    this.instanceId = crypto.randomUUID().slice(0, 8); // 8位随机数
    this.newSession(); // 对话开始，创建sessionId
  }
  updateAutoApprovalSettings(autoApprovalSettings: AutoApprovalSettings) {
    this.autoApprovalSettings = autoApprovalSettings;
  }

  /**
   * 初始化全局 autoExecute 状态
   */
  private async initializeWorkspaceAutoExecute(): Promise<void> {
    try {
      const value = await WorkspaceState.get('autoExecute');
      this.globalAutoExecute = Boolean(value ?? false);
    } catch (error) {
      console.error('Failed to initialize workspace autoExecute state:', error);
      this.globalAutoExecute = false;
    }
  }

  /**
   * 更新全局 autoExecute 状态
   */
  async updateWorkspaceAutoExecute(autoExecute: boolean): Promise<void> {
    try {
      await WorkspaceState.update('autoExecute', autoExecute);
      this.globalAutoExecute = autoExecute;

      const autoApprovalSettingsByAutoExecute = autoExecute ? AUTO_APPROVAL_SETTINGS : DEFAULT_AUTO_APPROVAL_SETTINGS;
      this.updateAutoApprovalSettings(autoApprovalSettingsByAutoExecute);
    } catch (error) {
      console.error('Failed to update workspace autoExecute state:', error);
    }
  }

  updateBrowserSettings(browserSettings: BrowserSettings) {
    this.browserSettings = browserSettings;
    this.browserSession.browserSettings = browserSettings;
  }

  async ensureTaskDirectoryExists(): Promise<string> {
    const provider = this.providerRef.deref();
    if (!provider) {
      throw new Error('Provider reference is invalid');
    }

    const isRemote = isRemoteEnvironment();

    if (isRemote) {
      // 远程环境：使用 vscode.Uri 构建正确的远程路径
      const globalStorageUri = provider.context.globalStorageUri;
      const taskDirUri = vscode.Uri.joinPath(globalStorageUri, 'tasks', this.taskId);
      // 直接传递 URI 对象而不是字符串，让 FileSystemHelper 正确处理
      await FileSystemHelper.mkdir(taskDirUri);
      return taskDirUri.toString();
    } else {
      // 本地环境：使用传统的路径构建方式
      const globalStoragePath = provider.context.globalStorageUri.fsPath;
      if (!globalStoragePath) {
        throw new Error('全局存储 URI 无效');
      }
      const taskDir = path.join(globalStoragePath, 'tasks', this.taskId);
      await fs.mkdir(taskDir, { recursive: true });
      return taskDir;
    }
  }

  /**
   * 获取保存的API对话历史记录
   * @returns 返回ApiMessage[]类型的对话历史
   */
  private async getSavedApiConversationHistory(): Promise<ApiMessage[]> {
    const isRemote = isRemoteEnvironment();
    const taskDir = await this.ensureTaskDirectoryExists();

    let filePath: string | vscode.Uri;
    if (isRemote) {
      const taskDirUri = vscode.Uri.parse(taskDir);
      filePath = vscode.Uri.joinPath(taskDirUri, GlobalFileNames.apiConversationHistory);
    } else {
      filePath = path.join(taskDir, GlobalFileNames.apiConversationHistory);
    }

    const fileExists = await fileExistsAtPath(filePath);
    if (fileExists) {
      const content = await FileSystemHelper.readFile(filePath, 'utf8');
      return JSON.parse(content);
    }
    return [];
  }

  async addToApiConversationHistory(message: ApiMessage) {
    this.apiConversationHistory.push(message);
    await this.saveApiConversationHistory();
  }

  async overwriteApiConversationHistory(newHistory: ApiMessage[]) {
    this.apiConversationHistory = newHistory;
    await this.saveApiConversationHistory();
  }

  private async saveApiConversationHistory() {
    try {
      const isRemote = isRemoteEnvironment();
      const taskDir = await this.ensureTaskDirectoryExists();

      let filePath: string | vscode.Uri;
      if (isRemote) {
        const taskDirUri = vscode.Uri.parse(taskDir);
        filePath = vscode.Uri.joinPath(taskDirUri, GlobalFileNames.apiConversationHistory);
      } else {
        filePath = path.join(taskDir, GlobalFileNames.apiConversationHistory);
      }

      await FileSystemHelper.writeFile(filePath, JSON.stringify(this.apiConversationHistory));
    } catch (error) {
      // in the off chance this fails, we don't want to stop the task
      console.error('Failed to save API conversation history:', error);
    }
  }

  private async getSavedJoyCoderMessages(): Promise<JoyCoderMessage[]> {
    const isRemote = isRemoteEnvironment();
    const taskDir = await this.ensureTaskDirectoryExists();

    let filePath: string | vscode.Uri;
    if (isRemote) {
      const taskDirUri = vscode.Uri.parse(taskDir);
      filePath = vscode.Uri.joinPath(taskDirUri, GlobalFileNames.uiMessages);
    } else {
      filePath = path.join(taskDir, GlobalFileNames.uiMessages);
    }

    if (await fileExistsAtPath(filePath)) {
      const joyCoderMessages = await FileSystemHelper.readFile(filePath, 'utf8');
      try {
        return JSON.parse(joyCoderMessages);
      } catch (error) {
        console.error(
          '%c [ getSavedJoyCoderMessages-json-parse-error ]-232',
          'font-size:13px; background:pink; color:#bf2c9f;',
          error
        );
        return [];
      }
    } else {
      // check old location
      let oldPath: string | vscode.Uri;
      if (isRemote) {
        const taskDirUri = vscode.Uri.parse(taskDir);
        oldPath = vscode.Uri.joinPath(taskDirUri, 'claude_messages.json');
      } else {
        oldPath = path.join(taskDir, 'claude_messages.json');
      }

      if (await fileExistsAtPath(oldPath)) {
        const content = await FileSystemHelper.readFile(oldPath, 'utf8');
        const data = JSON.parse(content);
        await FileSystemHelper.unlink(oldPath); // remove old file
        return data;
      }
    }
    return [];
  }

  /**
   * 判断消息类型是否为用户消息
   */
  private getIsUserMessage(sayType: JoyCoderSay): boolean {
    // 明确的用户反馈消息类型
    if (sayType === 'user_feedback' || sayType === 'user_feedback_diff') {
      return true;
    }

    // 特殊情况：第一条文本消息（用户输入）
    if (sayType === 'text' && this.apiConversationHistory.length === 0) {
      return true;
    }

    return false;
  }

  private async addToJoyCoderMessages(message: JoyCoderMessage) {
    // these values allow us to reconstruct the conversation history at the time this JoyCode message was created
    // it's important that apiConversationHistory is initialized before we add JoyCode messages
    message.conversationHistoryIndex = this.apiConversationHistory.length - 1; // NOTE: this is the index of the last added message which is the user message, and once the JoyCodermessages have been presented we update the apiconversationhistory with the completed assistant message. This means when resetting to a message, we need to +1 this index to get the correct assistant message that this tool use corresponds to
    message.conversationHistoryDeletedRange = this.conversationHistoryDeletedRange;

    // Add mode information to the message if not already present
    if (!message.modeInfo) {
      const provider = this.providerRef.deref();
      if (provider) {
        try {
          const state = await provider.getState();
          const currentModeSlug = state.mode;
          if (currentModeSlug) {
            const { getModeBySlug } = await import('../../web-agent/src/utils/modes');
            const mode = getModeBySlug(currentModeSlug, state.customModes);
            if (mode) {
              message.modeInfo = {
                agentId: mode.agentId,
                name: mode.name,
                avatar: mode.avatar,
              };
            }
          }
        } catch (error) {
          // If we can't get mode info, continue without it
          console.warn('Failed to get mode info for message:', error);
        }
      }
    }

    this.JoyCoderMessages.push(message);
    await this.saveJoyCoderMessages();
  }

  async overwriteJoyCoderMessages(newMessages: JoyCoderMessage[]) {
    this.JoyCoderMessages = newMessages;
    await this.saveJoyCoderMessages();
  }
  async saveJoyCoderMessages() {
    try {
      const isRemote = isRemoteEnvironment();
      const taskDir = await this.ensureTaskDirectoryExists();
      const messages = this.getJoyCoderMessagesToString();

      let filePath: string | vscode.Uri;
      if (isRemote) {
        const taskDirUri = vscode.Uri.parse(taskDir);
        filePath = vscode.Uri.joinPath(taskDirUri, GlobalFileNames.uiMessages);
      } else {
        filePath = path.join(taskDir, GlobalFileNames.uiMessages);
      }

      try {
        this.isWritingHistory = true;
        await FileSystemHelper.writeFile(filePath, messages);
        this.isWritingHistory = false;
      } catch (error) {
        Logger.error('saveJoyCoderMessages->writeFile->写入文件失败，尝试分块写入:', error);
        if (!isRemote) {
          // 在本地环境下，尝试使用流式写入
          try {
            // 创建一个可写流
            const writeStream = createWriteStream(filePath as string);
            await new Promise((resolve, reject) => {
              writeStream.on('error', (err) => {
                reject(err);
              });
              writeStream.on('finish', () => {
                resolve(true);
              });
              // 分块写入
              const chunkSize = 1024 * 1024; // 1MB 块大小
              for (let i = 0; i < messages.length; i += chunkSize) {
                const chunk = messages.slice(i, i + chunkSize);
                writeStream.write(chunk);
              }
              writeStream.end();
            });
          } catch (error) {
            Logger.error('%c [ saveJoyCoderMessages->createWriteStream->最终写入失败 ]-294', error);
          }
          this.isWritingHistory = false;
        } else {
          // 在远程环境下，重新尝试使用 FileSystemHelper
          await FileSystemHelper.writeFile(filePath, messages);
          this.isWritingHistory = false;
          Logger.error('%c [ saveJoyCoderMessages->远程环境写入失败 ]-294', error);
        }
      }
      // combined as they are in ChatView
      const apiMetrics = this.getTokenUsage();
      const taskMessage = this.JoyCoderMessages[0]; // first message is always the task say
      const lastRelevantMessageIndex = findLastIndex(
        this.JoyCoderMessages,
        (m) => !(m.ask === 'resume_task' || m.ask === 'resume_completed_task')
      );
      const lastRelevantMessage = this.JoyCoderMessages[lastRelevantMessageIndex];
      let taskDirSize = 0;
      try {
        // getFolderSize.loose silently ignores errors
        // returns # of bytes, size/1000/1000 = MB
        taskDirSize = await getFolderSize.loose(taskDir);
      } catch (error) {
        console.error('Failed to get task directory size:', taskDir, error);
        taskDirSize = 0;
      }
      await this.providerRef.deref()?.updateTaskHistory({
        id: this.taskId,
        ts: lastRelevantMessage?.ts,
        task: taskMessage?.text ?? '',
        tokensIn: apiMetrics?.totalTokensIn,
        tokensOut: apiMetrics?.totalTokensOut,
        cacheWrites: apiMetrics?.totalCacheWrites,
        cacheReads: apiMetrics?.totalCacheReads,
        totalCost: apiMetrics?.totalCost,
        size: taskDirSize,
        shadowGitConfigWorkTree: await this.checkpointTracker?.getShadowGitConfigWorkTree(),
        conversationHistoryDeletedRange: this.conversationHistoryDeletedRange,
        isFavorited: this.taskIsFavorited,
      });
    } catch (error) {
      console.error('Failed to save JoyCode messages:', error);
    }
  }

  private getJoyCoderMessagesToString(retryCount: number = 0): string {
    const maxRetries = 3;
    try {
      const messages = JSON.stringify(this.JoyCoderMessages); // 将JoyCoderMessages对象序列化为JSON字符串
      return messages; // 返回序列化后的JSON字符串
    } catch (error) {
      Logger.error('getJoyCoderMessagesToString-error:', error); // 捕获异常并记录错误日志
      try {
        const messages =
          '[' +
          this.JoyCoderMessages.map((message) => {
            try {
              return JSON.stringify(message);
            } catch (error) {
              console.error(
                '%c [getJoyCoderMessagesToString-map内部转为字符串时-error ]-359',
                'font-size:13px; background:pink; color:#bf2c9f;',
                error
              );
              return '';
            }
          })
            .filter((item) => item !== '')
            .join(',') +
          ']'; // 尝试将每个消息对象序列化为JSON字符串
        return messages; // 返回序列化后的JSON字符串
      } catch (error) {
        console.error(
          '%c [getJoyCoderMessagesToString-MAP转为字符串时-error ]-364',
          'font-size:13px; background:pink; color:#bf2c9f;',
          error
        );
        if (retryCount < maxRetries) {
          console.error(
            '%c [getJoyCoderMessagesToString-正在重试-error ]-399',
            'font-size:13px; background:pink; color:#bf2c9f;',
            `第${retryCount}/${maxRetries}次`
          );
          return this.getJoyCoderMessagesToString(retryCount + 1);
        }
        console.error(
          '%c [getJoyCoderMessagesToString-最终尝试失败-error ]-406',
          'font-size:13px; background:pink; color:#bf2c9f;'
        );
        return '[]';
      }
    }
  }

  async restoreCheckpoint(messageTs: number, restoreType: JoyCoderCheckpointRestore): Promise<void> {
    const messageIndex = this.JoyCoderMessages.findIndex((m) => m.ts === messageTs);
    const message = this.JoyCoderMessages[messageIndex];
    if (!message) {
      console.error('Message not found', this.JoyCoderMessages);
      return;
    }

    let didWorkspaceRestoreFail = false;

    switch (restoreType) {
      case 'task':
        break;
      case 'taskAndWorkspace':
      case 'workspace':
        if (!this.checkpointTracker) {
          try {
            console.log('Checkpoint tracker not available:', JSON.stringify(message));
          } catch (error) {
            console.error(
              '%c [getJoyCoderMessagesToString-map内部转为字符串时-error ]-359',
              'font-size:13px; background:pink; color:#bf2c9f;',
              error
            );
          }
          return;
        }
        if (message.lastCheckpointHash && this.checkpointTracker) {
          try {
            await this.checkpointTracker.resetHead(message.lastCheckpointHash);
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            vscode.window.showErrorMessage('Failed to restore checkpoint: ' + errorMessage);
            didWorkspaceRestoreFail = true;
          }
        }
        break;
    }

    if (!didWorkspaceRestoreFail) {
      switch (restoreType) {
        case 'task':
        case 'taskAndWorkspace':
          this.conversationHistoryDeletedRange = message.conversationHistoryDeletedRange;
          const newConversationHistory = this.apiConversationHistory.slice(
            0,
            (message.conversationHistoryIndex || 0) + 2
          ); // +1 since this index corresponds to the last user message, and another +1 since slice end index is exclusive
          await this.overwriteApiConversationHistory(newConversationHistory);

          // update the context history state
          // await this.contextManager.truncateContextHistory(message.ts, await this.ensureTaskDirectoryExists());

          // aggregate deleted api reqs info so we don't lose costs/tokens
          const deletedMessages = this.JoyCoderMessages.slice(messageIndex + 1);
          const deletedApiReqsMetrics = getApiMetrics(combineApiRequests(combineCommandSequences(deletedMessages)));

          const newJoyCoderMessages = this.JoyCoderMessages.slice(0, messageIndex + 1);
          await this.overwriteJoyCoderMessages(newJoyCoderMessages); // calls saveJoyCoderMessages which saves historyItem

          await this.say(
            'deleted_api_reqs',
            JSON.stringify({
              tokensIn: deletedApiReqsMetrics.totalTokensIn,
              tokensOut: deletedApiReqsMetrics.totalTokensOut,
              cacheWrites: deletedApiReqsMetrics.totalCacheWrites,
              cacheReads: deletedApiReqsMetrics.totalCacheReads,
              cost: deletedApiReqsMetrics.totalCost,
            } as JoyCoderApiReqInfo)
          );
          break;
        case 'workspace':
          break;
      }
    }
  }

  async presentMultifileDiff(messageTs: number, seeNewChangesSinceLastTaskCompletion: boolean) {
    const relinquishButton = () => {
      this.providerRef.deref()?.postMessageToWebview({ type: 'relinquishControl' });
    };

    const messageIndex = this.JoyCoderMessages.findIndex((m) => m.ts === messageTs);
    const message = this.JoyCoderMessages[messageIndex];
    if (!message) {
      console.error('Message not found');
      relinquishButton();
      return;
    }
    const hash = message.lastCheckpointHash;
    if (!hash) {
      console.error('No checkpoint hash found');
      relinquishButton();
      return;
    }

    // TODO: handle if this is called from outside original workspace, in which case we need to show user error message we cant show diff outside of workspace?
    if (!this.checkpointTracker) {
      try {
        this.checkpointTracker = await CheckpointTracker.create(
          this.taskId,
          this.providerRef.deref()?.context.globalStorageUri.fsPath
        );
        this.checkpointTrackerErrorMessage = undefined;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.warn('Failed to initialize checkpoint tracker:', errorMessage);
        this.checkpointTrackerErrorMessage = errorMessage;
        await this.providerRef.deref()?.postStateToWebview();
        vscode.window.showErrorMessage(errorMessage);
        relinquishButton();
        return;
      }
    }

    // eslint-disable-next-line init-declarations
    let changedFiles:
      | {
          relativePath: string;
          absolutePath: string;
          before: string;
          after: string;
        }[]
      | undefined;

    try {
      if (seeNewChangesSinceLastTaskCompletion) {
        // Get last task completed
        const lastTaskCompletedMessageCheckpointHash = findLast(
          this.JoyCoderMessages.slice(0, messageIndex),
          (m) => m.say === 'completion_result'
        )?.lastCheckpointHash;

        const firstCheckpointMessageCheckpointHash = this.JoyCoderMessages.find(
          (m) => m.say === 'checkpoint_created'
        )?.lastCheckpointHash;

        const previousCheckpointHash = lastTaskCompletedMessageCheckpointHash || firstCheckpointMessageCheckpointHash; // either use the diff between the first checkpoint and the task completion, or the diff between the latest two task completions

        if (!previousCheckpointHash) {
          vscode.window.showErrorMessage('Unexpected error: No checkpoint hash found');
          relinquishButton();
          return;
        }
        // Get changed files between current state and commit
        changedFiles = await this.checkpointTracker?.getDiffSet(previousCheckpointHash, hash);
        if (!changedFiles?.length) {
          vscode.window.showInformationMessage(JoyCoderRestoreMessageMap['No changes found']);
          relinquishButton();
          return;
        }
      } else {
        // Get changed files between current state and commit
        changedFiles = await this.checkpointTracker?.getDiffSet(hash);
        if (!changedFiles?.length) {
          vscode.window.showInformationMessage(JoyCoderRestoreMessageMap['No changes found']);
          relinquishButton();
          return;
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      vscode.window.showErrorMessage(JoyCoderRestoreMessageMap['Failed to retrieve diff set: '] + errorMessage);
      relinquishButton();
      return;
    }

    // Check if multi-diff editor is enabled in VS Code settings
    // const config = vscode.workspace.getConfiguration()
    // const isMultiDiffEnabled = config.get("multiDiffEditor.experimental.enabled")

    // if (!isMultiDiffEnabled) {
    // 	vscode.window.showErrorMessage(
    // 		"Please enable 'multiDiffEditor.experimental.enabled' in your VS Code settings to use this feature.",
    // 	)
    // 	relinquishButton()
    // 	return
    // }
    // Open multi-diff editor
    await vscode.commands.executeCommand(
      'vscode.changes',
      seeNewChangesSinceLastTaskCompletion
        ? JoyCoderRestoreMessageMap['New changes']
        : JoyCoderRestoreMessageMap['Changes since snapshot'],
      changedFiles.map((file) => [
        vscode.Uri.file(file.absolutePath),
        vscode.Uri.parse(`${DIFF_VIEW_URI_SCHEME}:${file.relativePath}`).with({
          query: Buffer.from(file.before ?? '').toString('base64'),
        }),
        vscode.Uri.parse(`${DIFF_VIEW_URI_SCHEME}:${file.relativePath}`).with({
          query: Buffer.from(file.after ?? '').toString('base64'),
        }),
      ])
    );
    relinquishButton();
  }

  async doesLatestTaskCompletionHaveNewChanges() {
    const messageIndex = findLastIndex(this.JoyCoderMessages, (m) => m.say === 'completion_result');
    const message = this.JoyCoderMessages[messageIndex];
    if (!message) {
      console.error('Completion message not found');
      return false;
    }
    const hash = message.lastCheckpointHash;
    if (!hash) {
      console.error('No checkpoint hash found');
      return false;
    }

    if (!this.checkpointTracker) {
      try {
        this.checkpointTracker = await CheckpointTracker.create(
          this.taskId,
          this.providerRef.deref()?.context.globalStorageUri.fsPath
        );
        this.checkpointTrackerErrorMessage = undefined;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.warn('Failed to initialize checkpoint tracker:', errorMessage);
        return false;
      }
    }

    // Get last task completed
    const lastTaskCompletedMessage = findLast(
      this.JoyCoderMessages.slice(0, messageIndex),
      (m) => m.say === 'completion_result'
    );

    try {
      // Get changed files between current state and commit
      // const changedFiles = await this.checkpointTracker?.getDiffSet(
      //   lastTaskCompletedMessage?.lastCheckpointHash, // if undefined, then we get diff from beginning of git history, AKA when the task was started
      //   hash,
      // );
      const lastTaskCompletedMessageCheckpointHash = lastTaskCompletedMessage?.lastCheckpointHash; // ask is only used to relinquish control, its the last say we care about
      const firstCheckpointMessageCheckpointHash = this.JoyCoderMessages.find(
        (m) => m.say === 'checkpoint_created'
      )?.lastCheckpointHash;

      const previousCheckpointHash = lastTaskCompletedMessageCheckpointHash || firstCheckpointMessageCheckpointHash; // either use the diff between the first checkpoint and the task completion, or the diff between the latest two task completions

      if (!previousCheckpointHash) {
        return false;
      }

      // const changedFilesCount = changedFiles?.length || 0;
      const changedFilesCount = (await this.checkpointTracker?.getDiffCount(previousCheckpointHash, hash)) || 0;
      if (changedFilesCount > 0) {
        return true;
      }
      if (changedFilesCount > 0) {
        return true;
      }
    } catch (error) {
      console.error('Failed to get diff set:', error);
      return false;
    }

    return false;
  }

  // Communicate with webview

  // partial has three valid states true (partial message), false (completion of partial message), undefined (individual complete message)
  async ask(
    type: JoyCoderAsk,
    text?: string,
    partial?: boolean,
    progressStatus?: ToolProgressStatus,
    isTaskDone?: boolean
  ): Promise<{
    response: JoyCoderAskResponse;
    text?: string;
    images?: string[];
  }> {
    // If this JoyCode instance was aborted by the provider, then the only thing keeping us alive is a promise still running in the background, in which case we don't want to send its result to the webview as it is attached to a new instance of JoyCode now. So we can safely ignore the result of any active promises, and this class will be deallocated. (Although we set JoyCode = undefined in provider, that simply removes the reference to this instance, but the instance is still alive until this promise resolves or rejects.)
    if (this.abort) {
      throw new Error(JoyCoderRestoreMessageMap['JoyCode instance aborted']);
    }
    let askTs: number;
    if (partial !== undefined) {
      const lastMessage = this.JoyCoderMessages.at(-1);
      const isUpdatingPreviousPartial =
        lastMessage && lastMessage.partial && lastMessage.type === 'ask' && lastMessage.ask === type;
      if (partial) {
        if (isUpdatingPreviousPartial) {
          // existing partial message, so update it
          lastMessage.text = text;
          lastMessage.partial = partial;
          lastMessage.progressStatus = progressStatus;
          // todo be more efficient about saving and posting only new data or one whole message at a time so ignore partial for saves, and only post parts of partial message instead of whole array in new listener
          // await this.saveJoyCoderMessages()
          // await this.providerRef.deref()?.postStateToWebview()
          await this.providerRef.deref()?.postMessageToWebview({
            type: 'partialMessage',
            partialMessage: lastMessage,
          });
          throw new Error(JoyCoderRestoreMessageMap['Current ask promise was ignored 1']);
        } else {
          // this is a new partial message, so add it with partial state
          this.askResponse = undefined;
          this.askResponseText = undefined;
          this.askResponseImages = undefined;
          askTs = Date.now();
          this.lastMessageTs = askTs;
          await this.addToJoyCoderMessages({
            ts: askTs,
            type: 'ask',
            ask: type,
            text,
            partial,
          });
          await this.providerRef.deref()?.postStateToWebview();
          throw new Error(JoyCoderRestoreMessageMap['Current ask promise was ignored 2']);
        }
      } else {
        // partial=false means its a complete version of a previously partial message
        if (isUpdatingPreviousPartial) {
          // this is the complete version of a previously partial message, so replace the partial with the complete version
          this.askResponse = undefined;
          this.askResponseText = undefined;
          this.askResponseImages = undefined;

          /*
          Bug for the history books:
          In the webview we use the ts as the chatrow key for the virtuoso list. Since we would update this ts right at the end of streaming, it would cause the view to flicker. The key prop has to be stable otherwise react has trouble reconciling items between renders, causing unmounting and remounting of components (flickering).
          The lesson here is if you see flickering when rendering lists, it's likely because the key prop is not stable.
          So in this case we must make sure that the message ts is never altered after first setting it.
          */
          askTs = lastMessage.ts;
          this.lastMessageTs = askTs;
          // lastMessage.ts = askTs
          lastMessage.text = text;
          lastMessage.partial = false;
          lastMessage.progressStatus = progressStatus;
          await this.saveJoyCoderMessages();
          // await this.providerRef.deref()?.postStateToWebview()
          await this.providerRef.deref()?.postMessageToWebview({
            type: 'partialMessage',
            partialMessage: lastMessage,
          });
        } else {
          // this is a new partial=false message, so add it like normal
          this.askResponse = undefined;
          this.askResponseText = undefined;
          this.askResponseImages = undefined;
          askTs = Date.now();
          this.lastMessageTs = askTs;
          await this.addToJoyCoderMessages({
            ts: askTs,
            type: 'ask',
            ask: type,
            text,
          });
          await this.providerRef.deref()?.postStateToWebview();
        }
      }
    } else {
      // this is a new non-partial message, so add it like normal
      // const lastMessage = this.JoyCoderMessages.at(-1)
      this.askResponse = undefined;
      this.askResponseText = undefined;
      this.askResponseImages = undefined;
      askTs = Date.now();
      this.lastMessageTs = askTs;
      await this.addToJoyCoderMessages({
        ts: askTs,
        type: 'ask',
        ask: type,
        text,
      });
      await this.providerRef.deref()?.postStateToWebview();
    }

    await pWaitFor(() => this.askResponse !== undefined || this.lastMessageTs !== askTs, { interval: 100 });
    if (this.lastMessageTs !== askTs) {
      if (!isTaskDone) {
        throw new Error(JoyCoderRestoreMessageMap['Current ask promise was ignored']); // could happen if we send multiple asks in a row i.e. with command_output. It's important that when we know an ask could fail, it is handled gracefully
      }
    }
    const result = {
      response: this.askResponse!,
      text: this.askResponseText,
      images: this.askResponseImages,
    };
    this.askResponse = undefined;
    this.askResponseText = undefined;
    this.askResponseImages = undefined;
    return result;
  }
  async handleWebviewAskResponse(askResponse: JoyCoderAskResponse, text?: string, images?: string[]) {
    // this.newSession(); //新的用户回复，认为是新开session
    this.askResponse = askResponse;
    this.askResponseText = text;
    this.askResponseImages = images;
  }

  async handleTerminalOperation(terminalOperation: 'continue' | 'abort') {
    if (terminalOperation === 'continue') {
      this.terminalProcess?.continue();
    } else if (terminalOperation === 'abort') {
      this.terminalProcess?.terminal?.sendText('\x03'); // send Ctrl+C to the terminal
    }
  }

  async say(
    type: JoyCoderSay,
    text?: string,
    images?: string[],
    partial?: boolean,
    progressStatus?: ToolProgressStatus,
    contextCondense?: ContextCondense
  ): Promise<undefined> {
    if (this.abort) {
      throw new Error(JoyCoderRestoreMessageMap['JoyCode instance aborted']);
    }
    if (type === 'error' && text?.includes('未登录')) {
      vscode.commands.executeCommand('JoyCode.LogOut');
      return;
    }
    if (partial !== undefined) {
      const lastMessage = this.JoyCoderMessages.at(-1);
      const isUpdatingPreviousPartial =
        lastMessage && lastMessage.partial && lastMessage.type === 'say' && lastMessage.say === type;
      if (partial) {
        if (isUpdatingPreviousPartial) {
          // existing partial message, so update it
          lastMessage.text = text;
          lastMessage.images = images;
          lastMessage.partial = partial;
          lastMessage.progressStatus = progressStatus;
          await this.providerRef.deref()?.postMessageToWebview({
            type: 'partialMessage',
            partialMessage: lastMessage,
          });
        } else {
          // this is a new partial message, so add it with partial state
          const sayTs = Date.now();
          this.lastMessageTs = sayTs;
          await this.addToJoyCoderMessages({
            ts: sayTs,
            type: 'say',
            say: type,
            text,
            images,
            partial,
            // 在消息创建时设置用户消息标识
            isUserMessage: this.getIsUserMessage(type),
            contextCondense,
          });
          await this.providerRef.deref()?.postStateToWebview();
        }
      } else {
        // partial=false means its a complete version of a previously partial message
        if (isUpdatingPreviousPartial) {
          // this is the complete version of a previously partial message, so replace the partial with the complete version
          this.lastMessageTs = lastMessage.ts;
          // lastMessage.ts = sayTs
          lastMessage.text = text;
          lastMessage.images = images;
          lastMessage.partial = false;
          lastMessage.progressStatus = progressStatus;

          // instead of streaming partialMessage events, we do a save and post like normal to persist to disk
          await this.saveJoyCoderMessages();
          // await this.providerRef.deref()?.postStateToWebview()
          await this.providerRef.deref()?.postMessageToWebview({
            type: 'partialMessage',
            partialMessage: lastMessage,
          }); // more performant than an entire postStateToWebview
        } else {
          // this is a new partial=false message, so add it like normal
          const sayTs = Date.now();
          this.lastMessageTs = sayTs;
          await this.addToJoyCoderMessages({
            ts: sayTs,
            type: 'say',
            say: type,
            text,
            images,
            // 在消息创建时设置用户消息标识
            isUserMessage: this.getIsUserMessage(type),
            contextCondense,
          });
          await this.providerRef.deref()?.postStateToWebview();
        }
      }
    } else {
      // this is a new non-partial message, so add it like normal
      const sayTs = Date.now();
      this.lastMessageTs = sayTs;
      await this.addToJoyCoderMessages({
        ts: sayTs,
        type: 'say',
        say: type,
        text,
        images,
        // 在消息创建时设置用户消息标识
        isUserMessage: this.getIsUserMessage(type),
        contextCondense,
      });
      await this.providerRef.deref()?.postStateToWebview();
    }
  }

  async sayAndCreateMissingParamError(
    toolName: ToolUseName,
    paramName: string,
    removeLastMessageType?: JoyCoderAsk | JoyCoderSay,
    relPath?: string
  ) {
    // await this.say(
    //   'error',
    //   sayMissingParamError(toolName, paramName, relPath),
    //   // `JoyCode tried to use ${toolName}${
    //   //   relPath ? ` for '${relPath.toPosix()}'` : ''
    //   // } without value for required parameter '${paramName}'. Retrying...`,
    // );
    if (removeLastMessageType) {
      this.removeLastPartialMessageIfExistsWithType('ask', removeLastMessageType);
      this.removeLastPartialMessageIfExistsWithType('say', removeLastMessageType);
    }
    Logger.error(sayMissingParamError(toolName, paramName, relPath));
    return formatResponse.toolError(formatResponse.missingToolParameterError(paramName));
  }

  async removeLastPartialMessageIfExistsWithType(type: 'ask' | 'say', askOrSay: JoyCoderAsk | JoyCoderSay) {
    const lastMessage = this.JoyCoderMessages.at(-1);
    if (
      lastMessage?.partial &&
      lastMessage.type === type &&
      (lastMessage.ask === askOrSay || lastMessage.say === askOrSay)
    ) {
      const messageToRemove = this.JoyCoderMessages.pop();
      await this.saveJoyCoderMessages();
      await this.providerRef.deref()?.postStateToWebview();
    }
  }

  // Task lifecycle

  private async startTask(task?: string, images?: string[]): Promise<void> {
    // conversationHistory (for API) and JoyCoderMessages (for webview) need to be in sync
    // if the extension process were killed, then on restart the JoyCoderMessages might not be empty, so we need to set it to [] when we create a new JoyCode client (otherwise webview would show stale messages from previous session)
    this.JoyCoderMessages = [];
    this.apiConversationHistory = [];

    await this.providerRef.deref()?.postStateToWebview();

    await this.say('user_feedback', task, images);

    // Initialize checkpoint tracker early, before api_req_started
    // This ensures checkpoint is ready when the first API request is made
    await this.initializeCheckpointTracker();

    this.isInitialized = true;

    const imageBlocks: Anthropic.ImageBlockParam[] = formatResponse.imageBlocks(images);
    await this.initiateTaskLoop([
      {
        type: 'text',
        text: `<task>\n${task}\n</task>`,
      },
      ...imageBlocks,
    ]);
  }

  /**
   * Initialize checkpoint tracker early in the task lifecycle
   * This method is called after user message is added but before api_req_started
   * Initialization failure will not affect the main flow
   */
  private async initializeCheckpointTracker(): Promise<void> {
    // Only initialize if not already initialized
    if (this.checkpointTracker) {
      return;
    }

    try {
      // Create a Promise race with timeout to avoid blocking the main flow
      const checkpointPromise = CheckpointTracker.create(
        this.taskId,
        this.providerRef.deref()?.context.globalStorageUri.fsPath
      );

      // Create a timeout Promise (15 seconds timeout)
      const timeoutPromise = new Promise<CheckpointTracker | undefined>((resolve) => {
        setTimeout(() => {
          resolve(undefined);
        }, 15000);
      });

      // Use Promise.race to implement timeout mechanism
      this.checkpointTracker = await Promise.race([checkpointPromise, timeoutPromise]);

      // If checkpoint tracker was successfully created, send checkpoint_created message
      if (this.checkpointTracker) {
        await this.say('checkpoint_created');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Failed to initialize checkpoint tracker:', errorMessage);
      this.checkpointTrackerErrorMessage = errorMessage;
      // Don't throw the error - let the main flow continue
    }
  }

  public async resumePausedTask(lastMessage: string) {
    // release this Cline instance from paused state
    this.isPaused = false;

    // fake an answer from the subtask that it has completed running and this is the result of what it has done
    // add the message to the chat history and to the webview ui
    try {
      await this.say('subtask_result', lastMessage);

      await this.addToApiConversationHistory({
        role: 'user',
        content: [
          {
            type: 'text',
            text: `[new_task_creation completed] Result: ${lastMessage}`,
          },
        ],
      });
    } catch (error) {
      console.error(`Error failed to add reply from subtast into conversation of parent task, error: ${error}`);
      throw error;
    }
  }

  private async resumeTaskFromHistory() {
    // TODO: right now we let users init checkpoints for old tasks, assuming they're continuing them from the same workspace (which we never tied to tasks, so no way for us to know if it's opened in the right workspace)
    // const doesShadowGitExist = await CheckpointTracker.doesShadowGitExist(this.taskId, this.providerRef.deref())
    // if (!doesShadowGitExist) {
    // 	this.checkpointTrackerErrorMessage = "Checkpoints are only available for new tasks"
    // }

    const modifiedJoyCoderMessages = await this.getSavedJoyCoderMessages();

    // Remove any resume messages that may have been added before
    const lastRelevantMessageIndex = findLastIndex(
      modifiedJoyCoderMessages,
      (m) => !(m.ask === 'resume_task' || m.ask === 'resume_completed_task')
    );
    if (lastRelevantMessageIndex !== -1) {
      modifiedJoyCoderMessages.splice(lastRelevantMessageIndex + 1);
    }

    // since we don't use api_req_finished anymore, we need to check if the last api_req_started has a cost value, if it doesn't and no cancellation reason to present, then we remove it since it indicates an api request without any partial content streamed
    const lastApiReqStartedIndex = findLastIndex(
      modifiedJoyCoderMessages,
      (m) => m.type === 'say' && m.say === 'api_req_started'
    );
    if (lastApiReqStartedIndex !== -1) {
      const lastApiReqStarted = modifiedJoyCoderMessages[lastApiReqStartedIndex];
      const { cost, cancelReason }: JoyCoderApiReqInfo = JSON.parse(lastApiReqStarted.text || '{}');
      if (cost === undefined && cancelReason === undefined) {
        modifiedJoyCoderMessages.splice(lastApiReqStartedIndex, 1);
      }
    }

    await this.overwriteJoyCoderMessages(modifiedJoyCoderMessages);
    this.JoyCoderMessages = await this.getSavedJoyCoderMessages();

    // Now present the JoyCode messages to the user and ask if they want to resume (NOTE: we ran into a bug before where the apiconversationhistory wouldnt be initialized when opening a old task, and it was because we were waiting for resume)
    // This is important in case the user deletes messages without resuming the task first
    this.apiConversationHistory = await this.getSavedApiConversationHistory();

    // load the context history state
    // await this.contextManager.initializeContextHistory(await this.ensureTaskDirectoryExists());

    const lastJoyCoderMessage = this.JoyCoderMessages.slice()
      .reverse()
      .find((m) => !(m.ask === 'resume_task' || m.ask === 'resume_completed_task')); // could be multiple resume tasks
    // const lastJoyCoderMessage = this.JoyCoderMessages[lastJoyCoderMessageIndex]
    // could be a completion result with a command
    // const secondLastJoyCoderMessage = this.JoyCoderMessages
    // 	.slice()
    // 	.reverse()
    // 	.find(
    // 		(m, index) =>
    // 			index !== lastJoyCoderMessageIndex && !(m.ask === "resume_task" || m.ask === "resume_completed_task")
    // 	)
    // (lastJoyCoderMessage?.ask === "command" && secondLastJoyCoderMessage?.ask === "completion_result")

    let askType: JoyCoderAsk;
    if (lastJoyCoderMessage?.ask === 'completion_result') {
      askType = 'resume_completed_task';
    } else {
      askType = 'resume_task';
    }

    this.isInitialized = true;

    const { response, text, images } = await this.ask(askType); // calls poststatetowebview
    let responseText: string | undefined;
    let responseImages: string[] | undefined;
    if (response === 'messageResponse') {
      await this.say('user_feedback', text, images);
      responseText = text;
      responseImages = images;
    }

    // need to make sure that the api conversation history can be resumed by the api, even if it goes out of sync with JoyCode messages

    let existingApiConversationHistory: Anthropic.Messages.MessageParam[] = await this.getSavedApiConversationHistory();

    // v2.0 xml tags refactor caveat: since we don't use tools anymore, we need to replace all tool use blocks with a text block since the API disallows conversations with tool uses and no tool schema
    const conversationWithoutToolBlocks = existingApiConversationHistory.map((message) => {
      if (Array.isArray(message.content)) {
        const newContent = message.content.map((block) => {
          if (block.type === 'tool_use') {
            // it's important we convert to the new tool schema format so the model doesn't get confused about how to invoke tools
            const inputAsXml = Object.entries(block.input as Record<string, string>)
              .map(([key, value]) => `<${key}>\n${value}\n</${key}>`)
              .join('\n');
            return {
              type: 'text',
              text: `<${block.name}>\n${inputAsXml}\n</${block.name}>`,
            } as Anthropic.Messages.TextBlockParam;
          } else if (block.type === 'tool_result') {
            // Convert block.content to text block array, removing images
            const contentAsTextBlocks = Array.isArray(block.content)
              ? block.content.filter((item) => item.type === 'text')
              : [{ type: 'text', text: block.content }];
            const textContent = contentAsTextBlocks.map((item) => item.text).join('\n\n');
            const toolName = findToolName(block.tool_use_id, existingApiConversationHistory);
            return {
              type: 'text',
              text: `[${toolName} Result]\n\n${textContent}`,
            } as Anthropic.Messages.TextBlockParam;
          }
          return block;
        });
        return { ...message, content: newContent };
      }
      return message;
    });
    existingApiConversationHistory = conversationWithoutToolBlocks;

    // FIXME: remove tool use blocks altogether

    // if the last message is an assistant message, we need to check if there's tool use since every tool use has to have a tool response
    // if there's no tool use and only a text block, then we can just add a user message
    // (note this isn't relevant anymore since we use custom tool prompts instead of tool use blocks, but this is here for legacy purposes in case users resume old tasks)

    // if the last message is a user message, we can need to get the assistant message before it to see if it made tool calls, and if so, fill in the remaining tool responses with 'interrupted'

    let modifiedOldUserContent: UserContent; // either the last message if its user message, or the user message before the last (assistant) message
    let modifiedApiConversationHistory: Anthropic.Messages.MessageParam[]; // need to remove the last user message to replace with new modified user message
    if (existingApiConversationHistory.length > 0) {
      const lastMessage = existingApiConversationHistory[existingApiConversationHistory.length - 1];

      if (lastMessage.role === 'assistant') {
        const content = Array.isArray(lastMessage.content)
          ? lastMessage.content
          : [{ type: 'text', text: lastMessage.content }];
        const hasToolUse = content.some((block) => block.type === 'tool_use');

        if (hasToolUse) {
          const toolUseBlocks = content.filter(
            (block) => block.type === 'tool_use'
          ) as Anthropic.Messages.ToolUseBlock[];
          const toolResponses: Anthropic.ToolResultBlockParam[] = toolUseBlocks.map((block) => ({
            type: 'tool_result',
            tool_use_id: block.id,
            content: JoyCoderRestoreMessageMap['Task was interrupted before this tool call could be completed.'],
          }));
          modifiedApiConversationHistory = [...existingApiConversationHistory]; // no changes
          modifiedOldUserContent = [...toolResponses];
        } else {
          modifiedApiConversationHistory = [...existingApiConversationHistory];
          modifiedOldUserContent = [];
        }
      } else if (lastMessage.role === 'user') {
        const previousAssistantMessage: Anthropic.Messages.MessageParam | undefined =
          existingApiConversationHistory[existingApiConversationHistory.length - 2];

        const existingUserContent: UserContent = Array.isArray(lastMessage.content)
          ? lastMessage.content
          : [{ type: 'text', text: lastMessage.content }];
        if (previousAssistantMessage && previousAssistantMessage.role === 'assistant') {
          const assistantContent = Array.isArray(previousAssistantMessage.content)
            ? previousAssistantMessage.content
            : [
                {
                  type: 'text',
                  text: previousAssistantMessage.content,
                },
              ];

          const toolUseBlocks = assistantContent.filter(
            (block) => block.type === 'tool_use'
          ) as Anthropic.Messages.ToolUseBlock[];

          if (toolUseBlocks.length > 0) {
            const existingToolResults = existingUserContent.filter(
              (block) => block.type === 'tool_result'
            ) as Anthropic.ToolResultBlockParam[];

            const missingToolResponses: Anthropic.ToolResultBlockParam[] = toolUseBlocks
              .filter((toolUse) => !existingToolResults.some((result) => result.tool_use_id === toolUse.id))
              .map((toolUse) => ({
                type: 'tool_result',
                tool_use_id: toolUse.id,
                content: JoyCoderRestoreMessageMap['Task was interrupted before this tool call could be completed.'],
              }));

            modifiedApiConversationHistory = existingApiConversationHistory.slice(0, -1); // removes the last user message
            modifiedOldUserContent = [...existingUserContent, ...missingToolResponses];
          } else {
            modifiedApiConversationHistory = existingApiConversationHistory.slice(0, -1);
            modifiedOldUserContent = [...existingUserContent];
          }
        } else {
          modifiedApiConversationHistory = existingApiConversationHistory.slice(0, -1);
          modifiedOldUserContent = [...existingUserContent];
        }
      } else {
        throw new Error(JoyCoderRestoreMessageMap['Unexpected: Last message is not a user or assistant message']);
      }
    } else {
      throw new Error(JoyCoderRestoreMessageMap['Unexpected: No existing API conversation history']);
      // console.error("Unexpected: No existing API conversation history")
      // modifiedApiConversationHistory = []
      // modifiedOldUserContent = []
    }

    const newUserContent: UserContent = [...modifiedOldUserContent];

    const wasRecent = lastJoyCoderMessage?.ts && Date.now() - lastJoyCoderMessage.ts < 30_000;

    newUserContent.push({
      type: 'text',
      text:
        `[TASK RESUMPTION] ${
          wasRecent
            ? '\n\nIMPORTANT: If the last tool use was a use_search_and_replace or use_write_file that was interrupted, the file was reverted back to its original state before the interrupted edit, and you do NOT need to re-read the file as you already have its up-to-date contents.'
            : ''
        }` +
        (responseText
          ? `\n\nNew instructions for task continuation:\n<user_message>\n${responseText}\n</user_message>`
          : ''),
    });

    if (responseImages && responseImages.length > 0) {
      newUserContent.push(...formatResponse.imageBlocks(responseImages));
    }

    await this.overwriteApiConversationHistory(modifiedApiConversationHistory);
    await this.initiateTaskLoop(newUserContent);
  }

  private async initiateTaskLoop(userContent: UserContent): Promise<void> {
    let nextUserContent = userContent;
    let includeFileDetails = true;
    while (!this.abort) {
      const didEndLoop = await recursivelyMakeJoyCoderRequests(this, nextUserContent, includeFileDetails, this.cwd);
      includeFileDetails = false; // we only need file details the first time

      //  The way this agentic loop works is that JoyCode will be given a task that he then calls tools to complete. unless there's an attempt_task_done call, we keep responding back to him with his tool's responses until he either attempt_task_done or does not use anymore tools. If he does not use anymore tools, we ask him to consider if he's completed the task and then call attempt_task_done, otherwise proceed with completing the task.
      // There is a MAX_REQUESTS_PER_TASK limit to prevent infinite requests, but JoyCode is prompted to finish the task as efficiently as he can.

      //const totalCost = this.calculateApiCost(totalInputTokens, totalOutputTokens)
      if (didEndLoop) {
        // For now a task never 'completes'. This will only happen if the user hits max requests and denies resetting the count.
        //this.say("task_completed", `Task completed. Total API usage cost: ${totalCost}`)
        break;
      } else {
        // this.say(
        // 	"tool",
        // 	"JoyCode responded with only text blocks but has not called attempt_task_done yet. Forcing him to continue with task..."
        // )
        // 尝试：如果没有使用工具，直接结束任务？
        // break;
        nextUserContent = [
          {
            type: 'text',
            text: formatResponse.noToolsUsed(),
          },
        ];
        this.consecutiveMistakeCount++;
        // console.error('initiateTaskLoop', formatResponse.noToolsUsed());

        // const { response, text, images } = await this.ask('completion_result', '', false, undefined);
        // if (response === 'yesButtonClicked') {
        //   // await pushToolResult(this, 'comp', ''); // signals to recursive loop to stop (for now this never happens since yesButtonClicked will trigger a new task)
        //   // return;
        // }
        // this.newSession();

        // if (text || images) {
        //   await this.say('user_feedback', text ?? '', images);
        // }

        // const toolResults: (Anthropic.TextBlockParam | Anthropic.ImageBlockParam)[] = [];
        // toolResults.push({
        //   type: 'text',
        //   text: `The user has provided feedback on the results. Consider their input to continue the task, and then attempt completion again.\n<feedback>\n${text}\n</feedback>`,
        // });
        // toolResults.push(...formatResponse.imageBlocks(images));
        // // this.userMessageContent.push({
        // //   type: 'text',
        // //   text: `[${await toolDescription(block, joyCoder)}] Result:`,
        // // });
        // this.userMessageContent.push(...toolResults);
      }
    }
  }

  async abortTask() {
    this.abort = true; // will stop any autonomously running promises
    this.terminalManager.disposeAll();
    this.urlContentFetcher.closeBrowser();
    this.browserSession.closeBrowser();
    this.JoyCoderIgnoreController.dispose();
    this.fileContextTracker.dispose();
    // Stop waiting for child task completion.
    if (this.pauseInterval) {
      clearInterval(this.pauseInterval);
      this.pauseInterval = undefined;
    }
    // If we're not streaming then `abortStream` (which reverts the diff
    // view changes) won't be called, so we need to revert the changes here.
    if (!this.isStreaming && this.diffViewProvider.isEditing) {
      await this.diffViewProvider.revertChanges();
    }

    // Save the countdown message in the automatic retry or other content.
    await this.saveJoyCoderMessages();
  }

  // Checkpoints

  async saveCheckpoint(isAttemptCompletionMessage: boolean = false) {
    // 远程环境暂不支持checkpoint，因此隐藏
    if (isRemoteEnvironment()) return;

    const commitHash = await this.checkpointTracker?.commit(); // silently fails for now
    // Set isCheckpointCheckedOut to false for all checkpoint_created messages
    this.JoyCoderMessages.forEach((message) => {
      if (message.say === 'checkpoint_created') {
        message.isCheckpointCheckedOut = false;
      }
    });
    if (commitHash) {
      if (!isAttemptCompletionMessage) {
        // For non-attempt completion we just say checkpoints
        await this.say('checkpoint_created', commitHash);
        const lastCheckpointMessage = findLast(this.JoyCoderMessages, (m) => m.say === 'checkpoint_created');
        if (lastCheckpointMessage) {
          lastCheckpointMessage.lastCheckpointHash = commitHash;
          await this.saveJoyCoderMessages();
        }
      } else {
        // For attempt_task_done, find the last completion_result message and set its checkpoint hash. This will be used to present the 'see new changes' button
        const lastCompletionResultMessage = findLast(
          this.JoyCoderMessages,
          (m) => m.say === 'completion_result' || m.ask === 'completion_result'
        );
        if (lastCompletionResultMessage) {
          lastCompletionResultMessage.lastCheckpointHash = commitHash;
          await this.saveJoyCoderMessages();
        }
      }
    }
  }

  // Tools

  async executeCommandTool(command: string): Promise<[boolean, ToolResponse]> {
    const terminalInfo = await this.terminalManager.getOrCreateTerminal(FileSystemHelper.getRemotePath(this.cwd));
    terminalInfo.terminal.show(); // weird visual bug when creating new terminals (even manually) where there's an empty space at the top.
    const process = this.terminalManager.runCommand(terminalInfo, command);
    this.terminalProcess = process;
    let userFeedback: { text?: string; images?: string[] } | undefined;
    let didContinue = false;

    // Chunked terminal output buffering
    const CHUNK_LINE_COUNT = 20;
    const CHUNK_BYTE_SIZE = 2048; // 2KB
    const CHUNK_DEBOUNCE_MS = 100;

    let outputBuffer: string[] = [];
    let outputBufferSize: number = 0;
    let chunkTimer: NodeJS.Timeout | null = null;
    let chunkEnroute = false;

    const flushBuffer = async (force = false) => {
      if (chunkEnroute || outputBuffer.length === 0) {
        if (force && !chunkEnroute && outputBuffer.length > 0) {
          // If force is true and no chunkEnroute, flush anyway
        } else {
          return;
        }
      }
      let chunk = outputBuffer.join('\n');
      outputBuffer = [];
      outputBufferSize = 0;
      chunkEnroute = true;
      try {
        chunk = TerminalManager.compressTerminalOutput(chunk);
        const { response, text, images } = await this.ask('command_output', chunk);
        if (response === 'yesButtonClicked') {
          // proceed while running
        } else {
          userFeedback = { text, images };
        }
        didContinue = true;
        process.continue();
      } catch {
        Logger.error('Error while asking for command output');
      } finally {
        chunkEnroute = false;
        // If more output accumulated while chunkEnroute, flush again
        if (outputBuffer.length > 0) {
          await flushBuffer();
        }
      }
    };

    const scheduleFlush = () => {
      if (chunkTimer) {
        clearTimeout(chunkTimer);
      }
      chunkTimer = setTimeout(async () => await flushBuffer(), CHUNK_DEBOUNCE_MS);
    };

    let result = '';
    let completed = false;
    let isTimedOut = false;
    let timeoutId: NodeJS.Timeout | null = null;
    try {
      process.on('line', async (line) => {
        if (completed) return; // Ignore lines after completion
        result += line + '\n';
        if (!didContinue) {
          outputBuffer.push(line);
          outputBufferSize += Buffer.byteLength(line, 'utf8');
          // Flush if buffer is large enough
          if (outputBuffer.length >= CHUNK_LINE_COUNT || outputBufferSize >= CHUNK_BYTE_SIZE) {
            await flushBuffer();
          } else {
            scheduleFlush();
          }
        } else {
          this.say('command_output', line);
        }
      });

      // 统一的完成处理函数
      const handleCompletion = async (source: 'completed' | 'timeout') => {
        if (completed) return; // 防止重复处理
        completed = true;
        isTimedOut = source === 'timeout';
        // 清理超时定时器
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
        // 清理chunk定时器
        if (chunkTimer) {
          clearTimeout(chunkTimer);
          chunkTimer = null;
        }
        // 处理剩余输出
        if (!didContinue && outputBuffer.length > 0) {
          try {
            await flushBuffer(true);
          } catch (err) {
            console.error('Error flushing buffer:', err);
          }
        }
        // 如果是超时且还没有continue，则触发continue
        if (isTimedOut && !didContinue) {
          try {
            process.continue();
          } catch (err) {
            console.error('Error triggering continue on timeout:', err);
          }
        }
      };

      process.once('completed', () => handleCompletion('completed'));

      process.once('no_shell_integration', async () => {
        await this.say('shell_integration_warning');
      });

      // 创建超时处理
      timeoutId = setTimeout(() => {
        handleCompletion('timeout');
      }, 10_000);

      // Promise只处理continue和error事件
      const promise = new Promise<void>((resolve, reject) => {
        process.once('continue', () => {
          // 清理超时定时器
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }
          resolve();
        });

        process.once('error', (error) => {
          // 清理超时定时器
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }
          reject(error);
        });
      });

      /**
       * 合并进程和Promise，使进程对象具有Promise的方法。
       * @param process 要增强的进程对象
       * @param promise 要合并的Promise
       * @returns 增强后的进程对象，具有Promise功能
       */
      await mergePromise(process, promise);
    } catch (error) {
      console.warn('%c [ process->error ]-1255', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
    // Wait for a short delay to ensure all messages are sent to the webview
    // This delay allows time for non-awaited promises to be created and
    // for their associated messages to be sent to the webview, maintaining
    // the correct order of messages (although the webview is smart about
    // grouping command_output messages despite any gaps anyways)
    await delay(50);

    result = result.trim();

    if (userFeedback) {
      await this.say('user_feedback', userFeedback.text, userFeedback.images);
      return [
        true,
        formatResponse.toolResult(
          `Command is still running in the user's terminal.${
            result.length > 0 ? `\nHere's the output so far:\n${result}` : ''
          }\n\nThe user provided the following feedback:\n<feedback>\n${userFeedback.text}\n</feedback>`,
          userFeedback.images
        ),
      ];
    }

    if (completed) {
      return [false, `Command executed.${result.length > 0 ? `\nOutput:\n${result}` : ''}`];
    } else {
      return [
        false,
        `Command is still running in the user's terminal.${
          result.length > 0 ? `\nHere's the output so far:\n${result}` : ''
        }\n\nYou will be updated on the terminal status and new output in the future.`,
      ];
    }
  }

  shouldAutoApproveTool(toolName: ToolUseName): boolean {
    if (this.autoApprovalSettings.enabled) {
      switch (toolName) {
        case 'use_read_file':
        case 'use_list_files':
        case 'use_definition_names':
        case 'use_search_files':
          return this.autoApprovalSettings.actions.readFiles;
        case 'use_write_file':
        case 'use_search_and_replace':
        case 'insert_content':
        case 'apply_diff':
        case 'switch_mode':
        case 'fetch_instructions':
        case 'new_task_creation':
          return this.autoApprovalSettings.actions.editFiles;
        case 'use_command':
          return this.autoApprovalSettings.actions.executeCommands;
        case 'use_browser':
          return this.autoApprovalSettings.actions.useBrowser;

        case 'get_mcp_resource':
        case 'use_mcp_tools':
          return this.autoApprovalSettings.actions.useMcp;
        case 'update_todo_list':
          return this.autoApprovalSettings.actions.todoList;
      }
    }
    return false;
  }

  formatErrorWithStatusCode(error: any): string {
    const statusCode = error.status || error.statusCode || (error.response && error.response.status);
    const message = error.message ?? JSON.stringify(serializeError(error), null, 2);

    // Only prepend the statusCode if it's not already part of the message
    return statusCode && !message.includes(statusCode.toString()) ? `${statusCode} - ${message}` : message;
  }
  private requestId: string = '';
  refreshRequestId(): string {
    this.requestId = this.conversationId + '_' + new Date().getTime();
    return this.requestId;
  }

  public getRequestId() {
    return this.requestId;
  }

  public isCurrentModelSupportsComputerUse() {
    const enableBrowserTool = GlobalState.get('browserToolEnabled') ?? false;
    const label = WorkspaceState.get('openAiModelId');
    const modelConfig = getChatModelAndConfig(label);
    const modelSupportsComputerUse = modelConfig.features?.includes('vision') ?? false;
    const supportsComputerUse = modelSupportsComputerUse && enableBrowserTool;
    return supportsComputerUse;
  }

  private async getSystemPrompt(): Promise<string> {
    // Wait for MCP servers to be connected before generating system prompt
    await pWaitFor(() => this.providerRef.deref()?.mcpHub?.isConnecting !== true, { timeout: 10_000 }).catch(() => {
      console.error('MCP servers failed to connect in time');
    });

    const mcpHub = this.providerRef.deref()?.mcpHub;
    if (!mcpHub) {
      Logger.error('MCP hub not available');
    }

    const supportsComputerUse = this.isCurrentModelSupportsComputerUse();
    const supportsCodebase = vscode.workspace.getConfiguration('JoyCode').get<boolean>('enableCodebase', false);
    let isTodoListEnabled = getVscodeConfig('JoyCode.config.todoListEnabled', true);
    let systemPrompt = '',
      contextMenuContent = '';
    const provider = this.providerRef.deref();
    if (!provider) {
      throw new Error('Provider not available');
    }
    const { customModes, mode, customModePrompts, contextMenuInstructions, maxConcurrentFileReads } =
      (await provider?.getState()) ?? {};
    const partialReadsEnabled = getVscodeConfig('JoyCode.coder.maxReadFileLine') != -1;
    // 获取思考模式状态
    const enableThinking = Boolean((await provider.getGlobalState('thinkingMode')) ?? false);
    // 获取联网搜索状态
    const webSearchEnabled = Boolean((await provider.getGlobalState('webSearchEnabled')) ?? false);
    systemPrompt = await SYSTEM_PROMPT(
      provider.context,
      FileSystemHelper.getRemotePath(this.cwd),
      supportsComputerUse,
      mode,
      mcpHub ?? (null as unknown as McpHub),
      supportsCodebase,
      this.browserSettings,
      true,
      'zh-CN',
      customModes,
      customModePrompts,
      this.customInstructions,
      this.diffStrategy,
      partialReadsEnabled,
      maxConcurrentFileReads,
      isTodoListEnabled,
      enableThinking,
      webSearchEnabled
    );

    console.log('systemPrompt----->', systemPrompt);

    if (contextMenuInstructions) {
      // 右键菜单系统提示词拼接
      contextMenuContent = `# The following are the functionalities requested by the user, which must be taken seriously and cannot be ignored under any circumstances:\n\n**${contextMenuInstructions}**`;
      this.providerRef.deref()?.updateGlobalState('contextMenuInstructions', ''); // 拼接完成后清空
    }
    const settingsCustomInstructions = this.customInstructions?.trim();
    const JoyCoderRulesFilePath = FileSystemHelper.resolveUri(this.cwd, GlobalFileNames.JoyCodeRules);
    let JoyCoderRulesFileInstructions: string | undefined = undefined;

    // 处理.joycode/rules目录下的mdc文件
    const rulesDir = FileSystemHelper.resolveUri(this.cwd, '.joycode/rules');
    let mdcRulesInstructions: string[] = [];

    try {
      // 检查.joycode/rules目录是否存在
      // 确保在远程环境下正确处理 Uri 对象
      const rulesDirExists = await fileExistsAtPath(rulesDir);

      if (rulesDirExists) {
        // 读取目录下的所有文件
        const files = await FileSystemHelper.readdir(rulesDir);
        const mdcFiles = files.filter((file: string) => file.endsWith('.mdc') || file.endsWith('.md'));

        // 处理每个mdc文件
        for (const mdcFile of mdcFiles) {
          try {
            const mdcFilePath = FileSystemHelper.resolveUri(rulesDir, mdcFile);

            const mdcContent = await FileSystemHelper.readFile(mdcFilePath, 'utf8');

            // 解析mdc文件的前置配置
            const configMatch = mdcContent.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);
            if (configMatch) {
              const configText = configMatch[1];
              const ruleContent = configMatch[2].trim();

              // 解析配置
              const globsMatch = configText.match(/globs:\s*(.*)/);
              const alwaysApplyMatch = configText.match(/alwaysApply:\s*(true|false)/);

              const globs = globsMatch ? globsMatch[1].trim() : '';
              const alwaysApply = alwaysApplyMatch ? alwaysApplyMatch[1] === 'true' : false;

              // 判断规则是否应该生效
              let shouldApply = false;

              // 情况1: 全都默认生效
              if (alwaysApply) {
                shouldApply = true;
              }
              // 情况3: 指定文件才生效
              else if (globs && !alwaysApply) {
                const globPatterns = globs.split(',').map((g: string) => g.trim());

                // 1. 检查当前活动的编辑器（正在编辑的文件）
                const activeEditor = vscode.window.activeTextEditor;
                if (activeEditor) {
                  const activeFilePath = activeEditor.document.uri.fsPath;
                  const fileName = FileSystemHelper.basename(activeFilePath);

                  for (const pattern of globPatterns) {
                    if (minimatch(fileName, pattern)) {
                      shouldApply = true;
                      break;
                    }
                  }
                }

                // 2. 如果没有匹配到活动编辑器的文件，检查最近的工具调用中是否有文件路径参数
                if (!shouldApply) {
                  // 查找最近的工具调用消息
                  const lastToolMessage = this.JoyCoderMessages.slice()
                    .reverse()
                    .find((m) => m.type === 'say' && m.say === 'text');
                  // console.log('this.lastToolMessage', lastToolMessage);
                  const msgPath = this.currentFilePath || lastToolMessage?.text || '';
                  if (msgPath) {
                    try {
                      // 尝试从工具调用中提取文件路径`([^`]+\.[a-zA-Z0-9]+)`

                      let filePath = '';
                      const filePathMatch = msgPath.match(/([^`]+\.[a-zA-Z0-9]+)/);
                      // console.log('filePath1', filePathMatch);

                      if (filePathMatch) {
                        filePath = filePathMatch[0];
                      }

                      if (filePath) {
                        const fileName = FileSystemHelper.basename(filePath);

                        for (const pattern of globPatterns) {
                          if (minimatch(fileName, pattern)) {
                            shouldApply = true;
                            break;
                          }
                        }
                      }
                    } catch (e) {
                      // 解析JSON失败，忽略错误
                      console.error('Failed to parse tool message:', e);
                    }
                  }
                }
              }
              // 情况2: 默认不生效 (alwaysApply: false 且没有匹配的文件)

              // 如果规则应该生效，添加到指令中
              if (shouldApply) {
                const ruleInstruction = `# ${FileSystemHelper.basename(
                  mdcFile
                )}\n\nconfig:\n${configText}\n\n${ruleContent}`;
                mdcRulesInstructions.push(ruleInstruction);
              }
            }
          } catch (fileError) {
            console.error(`[Rules] Failed to process rule file ${mdcFile}:`, fileError);
          }
        }
      }
    } catch (error) {
      console.error(`Failed to process .joycoder/rules directory:`, error);
    }

    // 合并所有生效的规则
    if (mdcRulesInstructions.length > 0) {
      const combinedMdcRules = mdcRulesInstructions.join('\n\n---\n\n');
      // 获取更有意义的工作目录显示名称
      const cwdDisplayName = getWorkspaceDisplayName(this.cwd);
      JoyCoderRulesFileInstructions = `# JoyCode Rules\n\nThe following is provided by a root-level .joycode/rules/*.mdc file where the user has specified instructions for this working directory (${cwdDisplayName}) :\n\n${combinedMdcRules}`;
    }

    // 处理根目录下的.JoyCoderRules文件
    if (await fileExistsAtPath(JoyCoderRulesFilePath)) {
      try {
        const ruleFileContent = (await FileSystemHelper.readFile(JoyCoderRulesFilePath, 'utf8')).trim();
        if (ruleFileContent) {
          // 获取更有意义的工作目录显示名称
          const cwdDisplayName = getWorkspaceDisplayName(this.cwd);
          const rootRulesContent = `# .JoyCodeRules\n\nThe following is provided by a root-level .JoyCodeRules file where the user has specified instructions for this working directory (${cwdDisplayName})\n\n${ruleFileContent}`;
          JoyCoderRulesFileInstructions = JoyCoderRulesFileInstructions
            ? `${JoyCoderRulesFileInstructions}\n\n---\n\n${rootRulesContent}`
            : rootRulesContent;
        } else {
          //驼峰不存在的时候也支持小写
          const JoyCoderRulesFilePath = FileSystemHelper.resolveUri(
            this.cwd,
            GlobalFileNames.JoyCodeRules.toLowerCase()
          );
          const ruleFileContentLowerCase = (await FileSystemHelper.readFile(JoyCoderRulesFilePath, 'utf8')).trim();
          if (ruleFileContentLowerCase) {
            // 获取更有意义的工作目录显示名称
            const cwdDisplayName = getWorkspaceDisplayName(this.cwd);
            const rootRulesContent = `# .JoyCodeRules\n\nThe following is provided by a root-level .JoyCodeRules file where the user has specified instructions for this working directory (${cwdDisplayName})\n\n${ruleFileContentLowerCase}`;
            JoyCoderRulesFileInstructions = JoyCoderRulesFileInstructions
              ? `${JoyCoderRulesFileInstructions}\n\n---\n\n${rootRulesContent}`
              : rootRulesContent;
          }
        }
      } catch {
        console.error(`Failed to read .JoyCodeRules file at ${JoyCoderRulesFilePath.toString()}`);
      }
    }

    const JoyCoderIgnoreContent = this.JoyCoderIgnoreController.joycoderIgnoreContent;
    let JoyCoderIgnoreInstructions: string | undefined = undefined;
    if (JoyCoderIgnoreContent) {
      JoyCoderIgnoreInstructions = `# .JoyCodeignore\n\n(The following is provided by a root-level .JoyCoderignore file where the user has specified files and directories that should not be accessed. When using use_list_files, you'll notice a ${LOCK_TEXT_SYMBOL} next to files that are blocked. Attempting to access the file's contents e.g. through use_read_file will result in an error.)\n\n${JoyCoderIgnoreContent}\n.JoyCodeignore`;
    }

    if (settingsCustomInstructions || JoyCoderRulesFileInstructions || contextMenuContent) {
      // altering the system prompt mid-task will break the prompt cache, but in the grand scheme this will not change often so it's better to not pollute user messages with it the way we have to with <potentially relevant details>

      const userInstructions = await addUserInstructions(
        settingsCustomInstructions,
        JoyCoderRulesFileInstructions,
        JoyCoderIgnoreInstructions,
        contextMenuContent
      );
      systemPrompt += userInstructions;
    }
    return systemPrompt;
  }

  public combineMessages(messages: JoyCoderMessage[]) {
    return combineApiRequests(combineCommandSequences(messages));
  }

  public getTokenUsage(): TokenUsage {
    return getApiMetrics(this.combineMessages(this.JoyCoderMessages.slice(1)));
  }

  async *attemptApiRequest(retryAttempt: number = 0): ApiStream {
    const systemPrompt = await this.getSystemPrompt();

    const { contextTokens } = this.getTokenUsage();
    console.log(
      '%c [ 上下文压缩this.getTokenUsage() ]-1962',
      'font-size:13px; background:pink; color:#bf2c9f;',
      this.getTokenUsage()
    );

    // if (contextTokens) {
    const label = WorkspaceState.get('openAiModelId');
    const modelConfig = getChatModelAndConfig(label);
    const maxTokens = modelConfig.respMaxTokens || 4096;
    const { contextWindow } = getContextWindowInfo();

    const truncateResult = await truncateConversationIfNeeded({
      messages: this.apiConversationHistory,
      totalTokens: contextTokens,
      maxTokens,
      contextWindow,
      apiHandler: this.api,
      autoCondenseContext: true,
      autoCondenseContextPercent: 70, //TODO:改成70%
      systemPrompt,
      taskId: this.taskId,
    });
    console.log(
      '%c [ 上下文压缩truncateResult ]-1966',
      'font-size:13px; background:pink; color:#bf2c9f;',
      truncateResult
    );
    if (truncateResult.messages !== this.apiConversationHistory) {
      await this.overwriteApiConversationHistory(truncateResult.messages);
    }
    if (truncateResult.summary) {
      const { summary, cost, prevContextTokens, newContextTokens = 0 } = truncateResult;
      const contextCondense: ContextCondense = { summary, cost, newContextTokens, prevContextTokens };
      await this.say(
        'condense_context',
        undefined /* text */,
        undefined /* images */,
        false /* partial */,
        undefined /* progressStatus */,
        contextCondense
      );
    } else if (truncateResult.error) {
      // await this.say('condense_context_error', truncateResult.error);
      console.error('condense_context_error', truncateResult);
    }
    // }

    const messagesSinceLastSummary = getMessagesSinceLastSummary(this.apiConversationHistory);

    const cleanConversationHistory = maybeRemoveImageBlocks(messagesSinceLastSummary, this.api).map(
      ({ role, content }) => ({ role, content })
    );

    const stream = this.api.createMessage(systemPrompt, cleanConversationHistory, this.refreshRequestId());
    const iterator = stream[Symbol.asyncIterator]();

    try {
      // awaiting first chunk to see if it will throw an error
      this.isWaitingForFirstChunk = true;
      const firstChunk = await iterator.next();
      yield firstChunk.value;
      this.isWaitingForFirstChunk = false;
    } catch (error) {
      // const isOpenRouterContextWindowError = checkIsOpenRouterContextWindowError(error);
      // // const isAnthropicContextWindowError = checkIsAnthropicContextWindowError(error);
      // if (!this.didAutomaticallyRetryFailedApiRequest) {
      //   if (isOpenRouterContextWindowError) {
      //     this.conversationHistoryDeletedRange = this.contextManager.getNextTruncationRange(
      //       this.apiConversationHistory,

      //       this.conversationHistoryDeletedRange,
      //       'quarter', // Force aggressive truncation
      //     );
      //     await this.saveJoyCoderMessages();
      //     await this.contextManager.triggerApplyStandardContextTruncationNoticeChange(
      //       Date.now(),
      //       await this.ensureTaskDirectoryExists(),
      //     );
      //   }
      //   console.log('first chunk failed, waiting 2 second before retrying');
      //   await setTimeoutPromise(2000);
      //   this.didAutomaticallyRetryFailedApiRequest = true;
      // } else {
      //   // request failed after retrying automatically once, ask user if they want to retry again
      //   // note that this api_req_failed ask is unique in that we only present this option if the api hasn't streamed any content yet (ie it fails on the first chunk due), as it would allow them to hit a retry button. However if the api failed mid-stream, it could be in any arbitrary state where some tools may have executed, so that error is handled differently and requires cancelling the task entirely.

      //   if (isOpenRouterContextWindowError) {
      //     const truncatedConversationHistory = this.contextManager.getTruncatedMessages(
      //       this.apiConversationHistory,
      //       this.conversationHistoryDeletedRange,
      //     );

      //     // If the conversation has more than 3 messages, we can truncate again. If not, then the conversation is bricked.
      //     // ToDo: Allow the user to change their input if this is the case.
      //     if (truncatedConversationHistory.length > 3) {
      //       error = new Error('上下文窗口已超出限制。点击重试以截断对话并再次尝试。');
      //       this.didAutomaticallyRetryFailedApiRequest = false;
      //     }
      //   }
      // request failed after retrying automatically once, ask user if they want to retry again
      // note that this api_req_failed ask is unique in that we only present this option if the api hasn't streamed any content yet (ie it fails on the first chunk due), as it would allow them to hit a retry button. However if the api failed mid-stream, it could be in any arbitrary state where some tools may have executed, so that error is handled differently and requires cancelling the task entirely.
      const errorMessage = this.formatErrorWithStatusCode(error);

      // Update the 'api_req_started' message to reflect final failure before asking user to manually retry
      const lastApiReqStartedIndex = findLastIndex(this.JoyCoderMessages, (m) => m.say === 'api_req_started');
      if (lastApiReqStartedIndex !== -1) {
        const currentApiReqInfo: JoyCoderApiReqInfo = JSON.parse(
          this.JoyCoderMessages[lastApiReqStartedIndex].text || '{}'
        );
        delete currentApiReqInfo.retryStatus;

        this.JoyCoderMessages[lastApiReqStartedIndex].text = JSON.stringify({
          ...currentApiReqInfo, // Spread the modified info (with retryStatus removed)
          cancelReason: 'retries_exhausted', // Indicate that automatic retries failed
          streamingFailedMessage: errorMessage,
        } satisfies JoyCoderApiReqInfo);
        // this.ask will trigger postStateToWebview, so this change should be picked up.
      }
      const { response } = await this.ask('api_req_failed', errorMessage);
      if (response !== 'yesButtonClicked') {
        // this will never happen since if noButtonClicked, we will clear current task, aborting this instance
        throw new Error('接口请求失败');
      }
      await this.say('api_req_retried');
      // }

      // delegate generator output from the recursive call
      yield* this.attemptApiRequest();
      return;
    }

    // no error, so we can continue to yield all remaining chunks
    // (needs to be placed outside of try/catch since it we want caller to handle errors not with api_req_failed as that is reserved for first chunk failures only)
    // this delegates to another generator or iterable object. In this case, it's saying "yield all remaining values from this iterator". This effectively passes along all subsequent chunks from the original stream.
    yield* iterator;
  }

  // Used when a sub-task is launched and the parent task is waiting for it to
  // finish.
  // TBD: The 1s should be added to the settings, also should add a timeout to
  // prevent infinite waiting.
  public async waitForResume() {
    await new Promise<void>((resolve) => {
      this.pauseInterval = setInterval(() => {
        if (!this.isPaused) {
          clearInterval(this.pauseInterval);
          this.pauseInterval = undefined;
          resolve();
        }
      }, 1000);
    });
  }

  async loadContext(userContent: UserContent, includeFileDetails: boolean = false) {
    return await Promise.all([
      // This is a temporary solution to dynamically load context mentions from tool results. It checks for the presence of tags that indicate that the tool was rejected and feedback was provided (see formatToolDeniedFeedback, attemptCompletion, executeCommand, and consecutiveMistakeCount >= 3) or "<answer>" (see askFollowupQuestion), we place all user generated content in these tags so they can effectively be used as markers for when we should parse mentions). However if we allow multiple tools responses in the future, we will need to parse mentions specifically within the user content tags.
      // (Note: this caused the @/ import alias bug where file contents were being parsed as well, since v2 converted tool results to text blocks)
      Promise.all(
        userContent.map(async (block) => {
          if (block.type === 'text') {
            // We need to ensure any user generated content is wrapped in one of these tags so that we know to parse mentions
            // FIXME: Only parse text in between these tags instead of the entire text block which may contain other tool results. This is part of a larger issue where we shouldn't be using regex to parse mentions in the first place (ie for cases where file paths have spaces)
            if (
              block.text.includes('<feedback>') ||
              block.text.includes('<answer>') ||
              block.text.includes('<task>') ||
              block.text.includes('<user_message>')
            ) {
              block.text = await parseSlashCommands(block.text);
              const paresdBlock = await parseMentions(
                block,
                this.cwd,
                this.urlContentFetcher,
                this.fileContextTracker,
                this.providerRef.deref()?.docTracker
              );
              return paresdBlock;
            }
          }
          return [block];
        })
      ).then((result) => result.flat()),
      getEnvironmentDetails(this, this.cwd, includeFileDetails),
    ]);
  }
  async createAgent(prompt: string) {
    try {
      const agentJSON = await this.api.completeAgent(prompt);
      if (agentJSON) {
        const agent = JSON.parse(agentJSON);
        return agent;
      }
      return undefined;
    } catch (error) {
      return undefined;
    }
  }
}
