import * as fs from 'fs/promises';
import * as os from 'os';
import * as path from 'path';
import { fileExistsAtPath } from '../../utils/fs';
import { GlobalFileNames } from '../storage/disk';
import { getWorkspacePath } from '../../utils/path';
import { FileSystemHelper } from '../../utils/FileSystemHelper';
import { defaultUserPrompts } from './promptContent';
import { isRemoteEnvironment } from '../../utils/fs';

export interface IUserPrompt {
  name: string;
  prompt: string;
  label: string;
  description?: string;
}

export class UserPromptManager {
  private userPrompts: Set<IUserPrompt> = new Set();
  private userPromptFilePath: string;

  constructor() {
    const cwd = getWorkspacePath();
    this.userPromptFilePath = FileSystemHelper.join(cwd, '.joycode', GlobalFileNames.userPrompts);
    this.initialize();
  }

  async getAndCreateUserPromptFilePath(): Promise<string> {
    const userPromptFilePath = this.userPromptFilePath;
    const fileExists = await fileExistsAtPath(userPromptFilePath);
    const defaultUserPrompt = JSON.stringify(defaultUserPrompts);
    if (!fileExists) {
      await FileSystemHelper.writeFile(userPromptFilePath, defaultUserPrompt);
    }
    return userPromptFilePath;
  }

  /**
   * 读取项目配置文件中的用户提示词
   * @returns Promise<IUserPrompt[]> 项目配置中的用户提示词数组
   */
  async readProjectPromptFile(): Promise<IUserPrompt[]> {
    try {
      const settingsPath = await this.getAndCreateUserPromptFilePath();
      const content = (await FileSystemHelper.readFile(settingsPath, 'utf-8')) ?? '[]';
      const promptList = JSON.parse(content);
      return promptList as IUserPrompt[];
    } catch (error) {
      // console.error('读取项目UserPrompt文件错误:', error);
      return [];
    }
  }

  /**
   * 读取系统全局配置文件中的用户提示词
   * @returns Promise<IUserPrompt[]> 系统全局配置中的用户提示词数组
   */
  async readSystemPromptFile(): Promise<IUserPrompt[]> {
    try {
      const osHomePromptFilePath = path.join(os.homedir(), '.joycode', GlobalFileNames.userPrompts);
      const fileExists = await fileExistsAtPath(osHomePromptFilePath);
      if (fileExists) {
        const promptContent = (await FileSystemHelper.readFile(osHomePromptFilePath, 'utf-8')) ?? '[]';
        const homePromptList = JSON.parse(promptContent);
        return homePromptList as IUserPrompt[];
      }
      return [];
    } catch (error) {
      // console.error('读取系统UserPrompt文件错误:', error);
      return [];
    }
  }

  /**
   * 读取所有用户提示词文件（项目配置 + 系统全局配置）
   * @returns Promise<IUserPrompt[]> 合并后的用户提示词数组
   */
  async readPromptFile(): Promise<IUserPrompt[]> {
    try {
      const projectPrompts = await this.readProjectPromptFile();
      const systemPrompts = await this.readSystemPromptFile();
      const allPrompts = [...projectPrompts, ...systemPrompts];
      const userPrompts = new Set(allPrompts);
      return Array.from(userPrompts);
    } catch (error) {
      // console.error('读取UserPrompt文件错误:', error);
      return [];
    }
  }

  async getUserPromptByName(name: string): Promise<IUserPrompt | undefined> {
    const userPrompts = await this.readPromptFile();
    const userPrompt = userPrompts.find((item: IUserPrompt) => item.name === name);
    return userPrompt;
  }

  private async initialize(): Promise<void> {
    await this.getAndCreateUserPromptFilePath();
    const fileContent = await this.readPromptFile();
    this.userPrompts = new Set(fileContent as IUserPrompt[]);
  }

  async addProjectUserPrompt(promptData: IUserPrompt): Promise<void> {
    if (!promptData) return;
    const fileContent = await this.readProjectPromptFile();
    this.userPrompts = new Set(fileContent as IUserPrompt[]);
    this.userPrompts.add(promptData);
    this.updatePromptFile();
  }

  /**
   * 添加系统级别的用户提示词到 os.homedir
   * @param promptData 要添加的用户提示词数据
   * @returns Promise<void>
   */
  async addSystemUserPrompt(promptData: IUserPrompt): Promise<void> {
    if (!promptData) return;

    try {
      // 获取系统级别的配置文件路径
      const osHomePromptFilePath = path.join(os.homedir(), '.joycode', GlobalFileNames.userPrompts);

      // 确保目录存在
      const osHomeJoyCodeDir = path.join(os.homedir(), '.joycode');
      await FileSystemHelper.mkdir(osHomeJoyCodeDir, { recursive: true });

      // 读取现有的系统级别配置
      const existingSystemPrompts = await this.readSystemPromptFile();

      // 创建新的配置集合并添加新的提示词
      const systemPrompts = new Set(existingSystemPrompts);
      systemPrompts.add(promptData);

      // 将配置写入系统级别的文件
      const systemPromptsArray = Array.from(systemPrompts);
      const fileContent = JSON.stringify(systemPromptsArray, null, 2);
      await FileSystemHelper.writeFile(osHomePromptFilePath, fileContent);

      // 更新内存中的用户提示词集合
      const allFileContent = await this.readPromptFile();
      this.userPrompts = new Set(allFileContent);
    } catch (error) {
      console.error('添加系统级别用户提示词失败:', error);
      throw error;
    }
  }

  async removeProjectUserPrompt(promptData: IUserPrompt) {
    if (!promptData) return;
    const fileContent = await this.readProjectPromptFile();
    // 基于name属性过滤，而不是依赖对象引用
    const filteredContent = fileContent.filter((item) => item.name !== promptData.name);
    this.userPrompts = new Set(filteredContent);
    this.updatePromptFile();
  }
  /**
   * 删除系统级别的用户提示词从 os.homedir
   * @param promptData 要删除的用户提示词数据
   * @returns Promise<void>
   */
  async removeSystemUserPrompt(promptData: IUserPrompt): Promise<void> {
    if (!promptData) return;

    try {
      // 获取系统级别的配置文件路径
      const osHomePromptFilePath = path.join(os.homedir(), '.joycode', GlobalFileNames.userPrompts);

      // 检查系统级别配置文件是否存在
      const fileExists = await fileExistsAtPath(osHomePromptFilePath);
      if (!fileExists) {
        console.warn('系统级别用户提示词配置文件不存在');
        return;
      }

      // 读取现有的系统级别配置
      const existingSystemPrompts = await this.readSystemPromptFile();

      // 基于name属性过滤，删除指定的提示词
      const filteredSystemPrompts = existingSystemPrompts.filter((item) => item.name !== promptData.name);

      // 将更新后的配置写入系统级别的文件
      const fileContent = JSON.stringify(filteredSystemPrompts, null, 2);
      await FileSystemHelper.writeFile(osHomePromptFilePath, fileContent);

      // 更新内存中的用户提示词集合
      const allFileContent = await this.readPromptFile();
      this.userPrompts = new Set(allFileContent);
    } catch (error) {
      console.error('删除系统级别用户提示词失败:', error);
      throw error;
    }
  }

  async updateUserPrompt(promptData: IUserPrompt) {
    if (!promptData) return;
    const fileContent = await this.readProjectPromptFile();
    // 更新匹配的项，创建新的数组而不是直接修改Set中的对象
    const updatedContent = fileContent.map((item) => {
      if (item.name === promptData.name) {
        return { ...item, ...promptData }; // 合并更新的属性
      }
      return item;
    });
    this.userPrompts = new Set(updatedContent);
    this.updatePromptFile();
  }

  /**
   * 获取用户提示数据
   * @param isString 是否返回字符串格式
   * @returns 如果isString为true，返回JSON字符串；否则返回提示数组
   */
  async getUserPrompt(isString?: boolean): Promise<string | IUserPrompt[]> {
    const userPrompts = Array.from(this.userPrompts);
    if (isString) {
      try {
        return JSON.stringify(userPrompts);
      } catch (error) {
        console.error('格式化UserPrompt文件错误:', error);
        return '';
      }
    }
    return userPrompts;
  }
  /**
   * 更新用户提示词文件
   * 获取用户提示词并写入文件
   * @returns Promise<void>
   */
  private async updatePromptFile(): Promise<void> {
    try {
      const fileContent = await this.getUserPrompt(true);
      await FileSystemHelper.writeFile(this.userPromptFilePath, `${fileContent}`);
    } catch (error) {
      console.error('%c [ 更新用户提示词失败 ]-63', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  }
}
