import { Anthropic } from '@anthropic-ai/sdk';
import <PERSON><PERSON> from 'jimp';

import {
  getMessagesSinceLastSummary,
  MAX_CONDENSE_THRESHOLD,
  MIN_CONDENSE_THRESHOLD,
  summarizeConversation,
  SummarizeResponse,
} from '../condense';
import { ApiMessage } from '../task-persistence/apiMessages';
import { ApiHandler } from '../../adaptor/api';

/**
 * Default percentage of the context window to use as a buffer when deciding when to truncate
 */
export const TOKEN_BUFFER_PERCENTAGE = 0.1;

/**
 * Counts tokens for user content using the provider's token counting implementation.
 *
 * @param {Array<Anthropic.Messages.ContentBlockParam>} content - The content to count tokens for
 * @param {ApiHandler} apiHandler - The API handler to use for token counting
 * @returns {Promise<number>} A promise resolving to the token count
 */
export async function estimateTokenCount(
  content: Array<Anthropic.Messages.ContentBlockParam>,
  apiHandler: <PERSON><PERSON><PERSON><PERSON><PERSON>
): Promise<number> {
  if (!content || content.length === 0) return 0;
  return apiHandler.countTokens(content);
}

/**
 * Truncates a conversation by removing a fraction of the messages.
 *
 * The first message is always retained, and a specified fraction (rounded to an even number)
 * of messages from the beginning (excluding the first) is removed.
 *
 * @param {ApiMessage[]} messages - The conversation messages.
 * @param {number} fracToRemove - The fraction (between 0 and 1) of messages (excluding the first) to remove.
 * @param {string} taskId - The task ID for the conversation, used for telemetry
 * @returns {ApiMessage[]} The truncated conversation messages.
 */
export function truncateConversation(messages: ApiMessage[], fracToRemove: number, taskId: string): ApiMessage[] {
  const truncatedMessages = [messages[0]];
  const rawMessagesToRemove = Math.floor((messages.length - 1) * fracToRemove);
  const messagesToRemove = rawMessagesToRemove - (rawMessagesToRemove % 2);
  const remainingMessages = messages.slice(messagesToRemove + 1);
  truncatedMessages.push(...remainingMessages);

  return truncatedMessages;
}

/**
 * Conditionally truncates the conversation messages if the total token count
 * exceeds the model's limit, considering the size of incoming content.
 *
 * @param {ApiMessage[]} messages - The conversation messages.
 * @param {number} totalTokens - The total number of tokens in the conversation (excluding the last user message).
 * @param {number} contextWindow - The context window size.
 * @param {number} maxTokens - The maximum number of tokens allowed.
 * @param {ApiHandler} apiHandler - The API handler to use for token counting.
 * @param {boolean} autoCondenseContext - Whether to use LLM summarization or sliding window implementation
 * @param {string} systemPrompt - The system prompt, used for estimating the new context size after summarizing.
 * @returns {ApiMessage[]} The original or truncated conversation messages.
 */

type TruncateOptions = {
  messages: ApiMessage[];
  totalTokens: number;
  contextWindow: number;
  maxTokens?: number | null;
  apiHandler: ApiHandler;
  autoCondenseContext: boolean;
  autoCondenseContextPercent: number;
  systemPrompt: string;
  taskId: string;
  customCondensingPrompt?: string;
  condensingApiHandler?: ApiHandler;
};

type TruncateResponse = SummarizeResponse & { prevContextTokens: number };

/**
 * Conditionally truncates the conversation messages if the total token count
 * exceeds the model's limit, considering the size of incoming content.
 *
 * @param {TruncateOptions} options - The options for truncation
 * @returns {Promise<ApiMessage[]>} The original or truncated conversation messages.
 */
export async function truncateConversationIfNeeded({
  messages,
  totalTokens,
  contextWindow,
  maxTokens,
  apiHandler,
  autoCondenseContext,
  autoCondenseContextPercent,
  systemPrompt,
  taskId,
  customCondensingPrompt,
  condensingApiHandler,
}: TruncateOptions): Promise<TruncateResponse> {
  let error: string | undefined;
  let cost = 0;
  // Calculate the maximum tokens reserved for response
  const reservedTokens = maxTokens || contextWindow * 0.2;
  // Calculate available tokens for conversation history
  // Truncate if we're within TOKEN_BUFFER_PERCENTAGE of the context window
  const allowedTokens = contextWindow * (1 - TOKEN_BUFFER_PERCENTAGE) - reservedTokens;
  const messagesSinceLastSummary = getMessagesSinceLastSummary(messages);
  console.log('%c [ 上下文压缩计算中messages ]-122', 'font-size:13px; background:pink; color:#bf2c9f;', messages);
  console.log(
    '%c [ 上下文压缩计算中messagesSinceLastSummary ]-122',
    'font-size:13px; background:pink; color:#bf2c9f;',
    messagesSinceLastSummary
  );
  let realTotaltokens = 0;
  if (messagesSinceLastSummary.length > 1 /*&& totalTokens === 0 由于网关返回token不对，全部重算*/) {
    console.log('%c [ 上下文压缩模型不返回用量，手动计算 ]-118', 'font-size:13px; background:pink; color:#bf2c9f;');
    // Calculate tokens for all messages except the last one
    const prevMessages = messagesSinceLastSummary.slice(0, -1); //计算前面消息的token
    const contextBlocks = [{ role: 'user', content: systemPrompt }, ...prevMessages].flatMap((message) =>
      typeof message.content === 'string' ? [{ text: message.content, type: 'text' as const }] : message.content
    );
    const estimatePrevTotalTokens = await estimateTokenCount(contextBlocks, apiHandler);
    console.log(
      '%c [ 上下文压缩estimatePrevTotalTokens ]-138',
      'font-size:13px; background:pink; color:#bf2c9f;',
      estimatePrevTotalTokens
    );
    realTotaltokens += estimatePrevTotalTokens;
  }

  let [lastMessage, isLastMessageTruncated] = await truncateSingleMessageIfNeeded({
    message: messages[messages.length - 1],
    allowedTokens,
    contextWindow,
    prevMessagesTokens: realTotaltokens,
    apiHandler,
  });
  if (isLastMessageTruncated) {
    messages = [...messages.slice(0, -1), lastMessage];
  }
  let lastMessageContent = lastMessage.content;
  let lastMessageTokens = Array.isArray(lastMessageContent)
    ? await estimateTokenCount(lastMessageContent, apiHandler)
    : await estimateTokenCount([{ type: 'text', text: lastMessageContent as string }], apiHandler);
  console.log(
    '%c [ 上下文压缩lastMessageTokens ]-107',
    'font-size:13px; background:pink; color:#bf2c9f;',
    lastMessageTokens
  );

  // Calculate total effective tokens (totalTokens never includes the last message)
  let prevContextTokens = realTotaltokens + lastMessageTokens; // 正常来说算到这里就可以了，但是有些模型不返回token，需要继续处理

  console.log(
    '%c [ 上下文压缩prevContextTokens ]-113',
    'font-size:13px; background:pink; color:#bf2c9f;',
    prevContextTokens
  );

  // Determine the effective threshold to use
  let effectiveThreshold = autoCondenseContextPercent;

  // If no specific threshold is found for the profile, fall back to global setting

  if (autoCondenseContext && !isLastMessageTruncated) {
    const contextPercent = (100 * prevContextTokens) / contextWindow;
    console.log(
      '%c [ 上下文压缩contextPercent ]-164',
      'font-size:13px; background:pink; color:#bf2c9f;',
      contextPercent
    );
    if (contextPercent >= effectiveThreshold || prevContextTokens > allowedTokens) {
      console.log('%c [ 上下文压缩-判定为需要压缩 ]-136', 'font-size:13px; background:pink; color:#bf2c9f;');
      // Attempt to intelligently condense the context
      const result = await summarizeConversation(
        messages,
        apiHandler,
        systemPrompt,
        taskId,
        prevContextTokens,
        true, // automatic trigger
        customCondensingPrompt,
        condensingApiHandler
      );
      console.log(
        '%c [ 上下文压缩-判定为需要压缩-result ]-139',
        'font-size:13px; background:pink; color:#bf2c9f;',
        result
      );

      if (result.error) {
        error = result.error;
        cost = result.cost;
      } else {
        return { ...result, prevContextTokens };
      }
    }
  }

  // Fall back to sliding window truncation if needed
  if (prevContextTokens > allowedTokens) {
    const truncatedMessages = truncateConversation(messages, 0.5, taskId);
    console.log(
      '%c [ 上下文压缩Fall back to sliding window truncation truncatedMessages ]-149',
      'font-size:13px; background:pink; color:#bf2c9f;',
      truncatedMessages
    );
    return { messages: truncatedMessages, prevContextTokens, summary: '', cost, error };
  }

  // No truncation or condensation needed
  return { messages, summary: '', cost, prevContextTokens, error };
}
/**
 * 使用Jimp库压缩Base64图片
 * @param base64Data - 原始Base64图片数据（不包含data:image前缀）
 * @param mediaType - 图片媒体类型，如 'image/jpeg', 'image/png'
 * @param quality - 压缩质量 (1-100)，默认70
 * @param maxWidth - 最大宽度，默认800
 * @returns 压缩后的Base64数据
 */
async function compressBase64Image(
  base64Data: string,
  mediaType: string,
  quality: number = 70,
  maxWidth: number = 800
): Promise<string> {
  try {
    // 将Base64转换为Buffer
    const imageBuffer = Buffer.from(base64Data, 'base64');

    // 使用Jimp读取图片
    const image = await Jimp.read(imageBuffer);

    // 获取图片尺寸
    const { width, height } = image.bitmap;

    // 如果图片宽度超过最大宽度，则调整尺寸
    if (width > maxWidth) {
      // 按比例缩放，保持宽高比
      const aspectRatio = height / width;
      const newHeight = Math.round(maxWidth * aspectRatio);
      image.resize(maxWidth, newHeight);
    }

    // 根据图片类型进行压缩并获取Buffer
    let compressedBuffer: Buffer;
    if (mediaType.includes('jpeg') || mediaType.includes('jpg')) {
      compressedBuffer = await image.quality(quality).getBufferAsync(Jimp.MIME_JPEG);
    } else if (mediaType.includes('png')) {
      // PNG格式，Jimp会自动处理透明度
      compressedBuffer = await image.getBufferAsync(Jimp.MIME_PNG);
    } else if (mediaType.includes('webp')) {
      // Jimp不直接支持WebP，转换为JPEG
      compressedBuffer = await image.quality(quality).getBufferAsync(Jimp.MIME_JPEG);
    } else {
      // 对于其他格式，转换为JPEG
      compressedBuffer = await image.quality(quality).getBufferAsync(Jimp.MIME_JPEG);
    }

    // 转换回Base64
    return compressedBuffer.toString('base64');
  } catch (error) {
    console.warn('图片压缩失败，返回原始数据:', error);
    return base64Data;
  }
}

/**
 * 压缩图片内容块
 * @param imageBlock - 图片内容块
 * @param compressionLevel - 压缩级别 (1-5)，数字越大压缩越强
 * @returns 压缩后的图片内容块
 */
async function compressImageBlock(
  imageBlock: Anthropic.Messages.ImageBlockParam,
  compressionLevel: number = 1
): Promise<Anthropic.Messages.ImageBlockParam> {
  // 根据压缩级别计算质量和最大宽度
  const quality = Math.max(30, 90 - compressionLevel * 15); // 质量从75到30
  const maxWidth = Math.max(400, 1200 - compressionLevel * 200); // 宽度从1000到400

  if (imageBlock.source.type === 'base64') {
    try {
      const compressedData = await compressBase64Image(
        imageBlock.source.data,
        imageBlock.source.media_type,
        quality,
        maxWidth
      );

      return {
        ...imageBlock,
        source: {
          ...imageBlock.source,
          data: compressedData,
        },
      };
    } catch (error) {
      console.warn('图片压缩失败，保持原样:', error);
      return imageBlock;
    }
  }

  // URL类型的图片无法直接压缩，保持原样
  return imageBlock;
}
type SingleTruncateOptions = {
  message: ApiMessage;
  allowedTokens: number;
  contextWindow: number;
  prevMessagesTokens: number;
  // maxTokens?: number | null;
  apiHandler: ApiHandler;
  // autoCondenseContext: boolean;
  // autoCondenseContextPercent: number;
  // systemPrompt: string;
  // taskId: string;
  // customCondensingPrompt?: string;
  // condensingApiHandler?: ApiHandler;
};

export async function truncateSingleMessageIfNeeded({
  message,
  allowedTokens,
  contextWindow,
  prevMessagesTokens,
  apiHandler,
}: SingleTruncateOptions): Promise<[ApiMessage, boolean]> {
  const availableTokens = contextWindow - prevMessagesTokens;
  if (availableTokens <= 100) {
    message.content = 'This message is truncated. Condense previous messages first!';
    return [message, true];
  }
  // 选择0.3的原因：上下文压缩会保留最新的3条消息，设置为0.3可以保证上下文不会被打满
  const allowedMessageTokens = Math.min(0.3 * allowedTokens, 0.3 * contextWindow);
  const MAX_TURNCATE_RETRIES = 10;
  let turncateMessageCount = 0;
  let messageContent = message.content;
  if (typeof messageContent === 'string') {
    messageContent = [{ type: 'text', text: messageContent as string }];
  }
  let messageTokens = await estimateTokenCount(messageContent, apiHandler);
  console.log(
    '%c [ 上下文压缩LastMessage，传入消息Tokens ]-338',
    'font-size:13px; background:pink; color:#bf2c9f;',
    messageTokens,
    message
  );

  let wasTruncated = false;

  while (messageTokens > allowedMessageTokens && turncateMessageCount < MAX_TURNCATE_RETRIES) {
    console.log(
      `%c [ 上下文压缩LastMessage，第${turncateMessageCount}次压缩 ]-340`,
      'font-size:13px; background:pink; color:#bf2c9f;',
      messageContent,
      messageTokens,
      allowedMessageTokens
    );
    turncateMessageCount++;
    wasTruncated = true;

    // 分别处理文本和图片内容
    const newContent: Anthropic.Messages.ContentBlockParam[] = [];

    for (const block of messageContent) {
      if (block.type === 'text') {
        // 对文本内容进行截断（保留前一半）
        const textBlock = block as Anthropic.Messages.TextBlockParam;
        if (textBlock.text.startsWith('<environment_details>')) {
          newContent.push(textBlock); //系统带入的环境信息不压缩
          continue;
        }
        const halfLength = Math.floor(textBlock.text.length / 2);
        if (halfLength > 0) {
          newContent.push({
            ...textBlock,
            text: textBlock.text.substring(0, halfLength),
          });
        }
      } else if (block.type === 'image') {
        // 对图片进行压缩处理
        const imageBlock = block as Anthropic.Messages.ImageBlockParam;
        try {
          // 根据截断次数增加压缩级别
          const compressionLevel = Math.min(5, turncateMessageCount);
          const compressedImageBlock = await compressImageBlock(imageBlock, compressionLevel);
          newContent.push(compressedImageBlock);
        } catch (error) {
          console.warn('图片压缩失败，尝试移除图片:', error);
          // 如果压缩失败且有文本内容，则移除图片；否则保留原图片
          const hasTextContent = messageContent.some((b) => b.type === 'text');
          if (!hasTextContent) {
            newContent.push(imageBlock);
          }
        }
      } else {
        // 其他类型的内容块保持不变
        newContent.push(block);
      }
    }

    messageContent = newContent;

    // 如果内容为空，添加一个最小的文本块避免完全空白
    if (messageContent.length === 0) {
      messageContent = [{ type: 'text', text: '...' }];
    }

    // 重新计算token数
    messageTokens = await estimateTokenCount(messageContent, apiHandler);
    console.log(
      `%c [ 上下文压缩LastMessage，第${turncateMessageCount}次压缩后 ]-395`,
      'font-size:13px; background:pink; color:#bf2c9f;',
      messageContent,
      messageTokens,
      allowedMessageTokens
    );
  }

  // 如果消息被截断了，在前后添加提示信息
  if (wasTruncated) {
    const truncationWarning: Anthropic.Messages.TextBlockParam = {
      type: 'text',
      text: '[注意：此消息因长度超限已被截断]',
    };

    const truncationSuffix: Anthropic.Messages.TextBlockParam = {
      type: 'text',
      text: '[消息截断结束]',
    };

    messageContent = [truncationWarning, ...messageContent, truncationSuffix];
    console.log(
      `%c [ 上下文压缩LastMessage，最终结果 ]-417`,
      'font-size:13px; background:pink; color:#bf2c9f;',
      messageContent,
      messageTokens,
      allowedMessageTokens
    );
  } else {
    console.log('%c [ 上下文压缩LastMessage，无需被压缩 ]-404', 'font-size:13px; background:pink; color:#bf2c9f;');
  }

  // 更新消息内容
  message.content = messageContent;

  return [message, wasTruncated];
}

//下面是旧的代码
/*
We can't implement a dynamically updating sliding window as it would break prompt cache
every time. To maintain the benefits of caching, we need to keep conversation history
static. This operation should be performed as infrequently as possible. If a user reaches
a 200k context, we can assume that the first half is likely irrelevant to their current task.
Therefore, this function should only be called when absolutely necessary to fit within
context limits, not as a continuous process.
*/
// export function truncateHalfConversation(
// 	messages: Anthropic.Messages.MessageParam[],
// ): Anthropic.Messages.MessageParam[] {
// 	// API expects messages to be in user-assistant order, and tool use messages must be followed by tool results. We need to maintain this structure while truncating.

// 	// Always keep the first Task message (this includes the project's file structure in environment_details)
// 	const truncatedMessages = [messages[0]]

// 	// Remove half of user-assistant pairs
// 	const messagesToRemove = Math.floor(messages.length / 4) * 2 // has to be even number

// 	const remainingMessages = messages.slice(messagesToRemove + 1) // has to start with assistant message since tool result cannot follow assistant message with no tool use
// 	truncatedMessages.push(...remainingMessages)

// 	return truncatedMessages
// }

/*
getNextTruncationRange: Calculates the next range of messages to be "deleted"
- Takes the full messages array and optional current deleted range
- Always preserves the first message (task message)
- Removes 1/2 of remaining messages (rounded down to even number) after current deleted range
- Returns [startIndex, endIndex] representing inclusive range to delete

getTruncatedMessages: Constructs the truncated array using the deleted range
- Takes full messages array and optional deleted range
- Returns new array with messages in deleted range removed
- Preserves order and structure of remaining messages

The range is represented as [startIndex, endIndex] where both indices are inclusive
The functions maintain the original array integrity while allowing progressive truncation
through the deletedRange parameter

Usage example:
const messages = [user1, assistant1, user2, assistant2, user3, assistant3];
let deletedRange = getNextTruncationRange(messages); // [1,2] (assistant1,user2)
let truncated = getTruncatedMessages(messages, deletedRange);
// [user1, assistant2, user3, assistant3]

deletedRange = getNextTruncationRange(messages, deletedRange); // [2,3] (assistant2,user3)
truncated = getTruncatedMessages(messages, deletedRange);
// [user1, assistant3]
*/
