import os from 'os';
import { debounce } from 'lodash';
import pWait<PERSON>or from 'p-wait-for';
import * as vscode from 'vscode';
import { buildApiHandler } from '../../adaptor/api';
import { openFile, openImage } from '../../integrations/misc/open-file';
import { selectImages } from '../../integrations/misc/process-images';
import { getTheme } from '../../integrations/theme/getTheme';
import { ExtensionState } from '../../shared/ExtensionMessage';
import { JoyCoderCheckpointRestore, WebviewMessage } from '../../shared/WebviewMessage';
import { JoyCoder } from '../Joycoder';
import { openMention } from '../mentions';
import {
  checkLogin,
  getSelectedText,
  getSelectionInfo,
  ideAppName,
  isIDE,
  PLUGIN_ID,
  forceJdhLogin,
  reportAction,
  IActionCustomReportParam,
  ActionType,
} from '@joycoder/shared';
import { setCurrentChatGPTModel } from '@joycoder/plugin-base-ai/src/dialog';
import { Mode } from '../../shared/modes';
import { defaultModeSlug } from '../../../web-agent/src/utils/modes';
import { CustomSupportPrompts, supportPrompt } from '../../../web-agent/src/utils/support-prompt';
import { singleCompletionHandler } from '../../utils/single-completion-handler';
import { restoreCheckpoint } from '../JoyCoder/restoreCheckpoint';
import { JoyCoderMCPMessageMap } from '../../adaptor/translate/message';
import { JoyCoderProvider } from './JoycoderProvider';
import { setConfigInfo } from '@joycoder/plugin-base-ai/src/dialog/setting';
import AdoptResultCache from '@joycoder/plugin-base-code-completion/src/stats/adoptResultCache';
import { setPendingTodoList } from '../JoyCoder/tools/updateTodoListTool';

export async function handleWebviewMessage(JCProvider: JoyCoderProvider, message: WebviewMessage) {
  switch (message.type) {
    case 'webviewDidLaunch':
      JCProvider.postStateToWebview();
      JCProvider.workspaceTracker?.populateFilePaths(); // don't await
      JCProvider.docTracker?.updateDocList();
      getTheme().then((theme) =>
        JCProvider.postMessageToWebview({
          type: 'theme',
          text: JSON.stringify(theme),
        })
      );
      const promptList = await JCProvider.userPromptManager?.readPromptFile();
      JCProvider.postMessageToWebview({
        type: 'updateUserPrompt',
        promptList,
      });
      // 监听选择代码事件
      vscode.window.onDidChangeTextEditorSelection(
        debounce(async () => {
          const selectText = getSelectedText() ?? '';
          const selectInfo = getSelectionInfo();
          JCProvider.postMessageToWebview({
            type: 'updateSelectionContext',
            selectionInfo: selectInfo,
          });
          JCProvider.selectionContext = selectInfo;
          await JCProvider.updateGlobalState('selectionContext', JCProvider.selectionContext);
          await JCProvider.postStateToWebview();
        }, 100)
      );
      break;
    case 'updateSupportPrompt':
      try {
        if (Object.keys(message?.values ?? {}).length === 0) {
          return;
        }

        const existingPrompts = JCProvider.getGlobalState('customSupportPrompts') ?? {};
        const updatedPrompts = { ...existingPrompts, ...message.values };
        await JCProvider.updateGlobalState('customSupportPrompts', updatedPrompts);
        await JCProvider.postStateToWebview();
      } catch (error) {
        console.error(`Error update support prompt: ${JSON.stringify(error, Object.getOwnPropertyNames(error), 2)}`);
      }
      break;
    case 'resetSupportPrompt':
      try {
        if (!message?.text) {
          return;
        }

        const existingPrompts = JCProvider.getGlobalState('customSupportPrompts') ?? {};
        const updatedPrompts = { ...existingPrompts } as unknown as CustomSupportPrompts;
        updatedPrompts[message.text] = undefined;
        await JCProvider.updateGlobalState('customSupportPrompts', updatedPrompts);
        await JCProvider.postStateToWebview();
      } catch (error) {
        console.error('%c [ error ]-473', 'font-size:13px; background:pink; color:#bf2c9f;', error);
      }
      break;
    case 'updatePrompt':
      if (message.promptMode && message.customPrompt !== undefined) {
        const existingPrompts = JCProvider.getGlobalState('customModePrompts') ?? {};
        const updatedPrompts = { ...existingPrompts, [message.promptMode]: message.customPrompt };
        await JCProvider.updateGlobalState('customModePrompts', updatedPrompts);
        JCProvider.postStateToWebview();
      }
      break;
    case 'chatgpt-set-model':
      await setCurrentChatGPTModel(message?.model as any);
      JCProvider.updateModelConfig({ model: message?.model });
      break;
    case 'chatgpt-get-model':
      JCProvider.updateModelConfig();
      break;
    case 'chatgpt-get-model-list':
      await JCProvider.getModelList();
      break;

    case 'newTask':
      await JCProvider.initJoyCoderWithTask(message.text, message.images);
      break;
    case 'condense':
      JCProvider.getCurrentJoyCoder()?.handleWebviewAskResponse('yesButtonClicked');
      break;
    case 'openInNewTab':
      await vscode.commands.executeCommand('joycoder.joycoder.openInNewTab');
      break;
    case 'queryIdeName':
      JCProvider.postMessageToWebview({
        type: 'updatedIdeName',
        ideAppName,
      });
      break;
    case 'apiConfiguration':
      // const BASE_URL = 'http://chatgpt-relay.jd.com/v1/';
      if (message.apiConfiguration) {
        const { apiProvider, apiModelId, apiKey, openRouterApiKey, openAiBaseUrl, openAiApiKey, openAiModelId } =
          message.apiConfiguration;
        await JCProvider.updateGlobalState('apiProvider', apiProvider);
        JCProvider.updateModelState(apiModelId, openAiModelId, openAiBaseUrl);
        await JCProvider.updateStateByName('apiModelId', apiModelId ?? '');
        await JCProvider.storeSecret('apiKey', apiKey);
        await JCProvider.storeSecret('openRouterApiKey', openRouterApiKey);
        await JCProvider.updateStateByName('openAiBaseUrl', openAiBaseUrl ?? '');
        await JCProvider.storeSecret('openAiApiKey', openAiApiKey);
        await JCProvider.updateStateByName('openAiModelId', openAiModelId ?? '');
        if (JCProvider.getCurrentJoyCoder()) {
          const jc = JCProvider.getCurrentJoyCoder() as JoyCoder;
          jc.api = buildApiHandler(message.apiConfiguration);
        }
      }
      await JCProvider.postStateToWebview();
      break;
    case 'customInstructions':
      await JCProvider.updateCustomInstructions(message.text);
      break;
    case 'autoApprovalSettings':
      if (message.autoApprovalSettings) {
        await JCProvider.updateGlobalState('autoApprovalSettings', message.autoApprovalSettings);
        if (JCProvider.getCurrentJoyCoder()) {
          (JCProvider.getCurrentJoyCoder() as JoyCoder).autoApprovalSettings = message.autoApprovalSettings;
        }
        await JCProvider.postStateToWebview();
        // 通过 plugin-base-ai 的 setConfigInfo 触发设置页面更新
        if (setConfigInfo) {
          await setConfigInfo(
            {
              type: 'autoApprovalSettings',
              autoApprovalSettings: message.autoApprovalSettings,
            },
            false
          );
        }
      }
      break;
    case 'autoExecute':
      if (typeof message.autoExecute === 'boolean') {
        // 更新全局状态
        await JCProvider.updateWorkspaceState('autoExecute', message.autoExecute);
        // 更新当前 JoyCoder 实例的状态
        if (JCProvider.getCurrentJoyCoder()) {
          await (JCProvider.getCurrentJoyCoder() as JoyCoder).updateWorkspaceAutoExecute(message.autoExecute);
        }
        await JCProvider.postStateToWebview();
      }
      break;
    case 'browserSettings':
      if (message.browserSettings) {
        await JCProvider.updateGlobalState('browserSettings', message.browserSettings);
        if (JCProvider.getCurrentJoyCoder()) {
          (JCProvider.getCurrentJoyCoder() as JoyCoder).updateBrowserSettings(message.browserSettings);
        }
        await JCProvider.postStateToWebview();
      }
      break;
    case 'optionsResponse':
      await JCProvider.postMessageToWebview({
        type: 'invoke',
        invoke: 'sendMessage',
        text: message.text,
      });
      break;
    case 'askResponse':
      JCProvider.getCurrentJoyCoder()?.handleWebviewAskResponse(message.askResponse!, message.text, message.images);
      break;
    case 'terminalOperation':
      if (message.terminalOperation) {
        JCProvider.getCurrentJoyCoder()?.handleTerminalOperation(message.terminalOperation);
      }
      break;
    case 'clearTask':
      await JCProvider.finishSubTask('任务已取消：用户主动停止并取消了任务。');
      // await JCProvider.clearTask();
      await JCProvider.postStateToWebview();
      break;
    case 'didShowAnnouncement':
      await JCProvider.updateGlobalState('lastShownAnnouncementId', JCProvider.latestAnnouncementId);
      await JCProvider.postStateToWebview();
      break;
    case 'selectImages':
      const images = await selectImages();
      await JCProvider.postMessageToWebview({
        type: 'selectedImages',
        images,
      });
      break;
    case 'exportCurrentTask':
      const currentTaskId = JCProvider.getCurrentJoyCoder()?.taskId;
      if (currentTaskId) {
        JCProvider.exportTaskWithId(currentTaskId);
      }
      break;
    case 'showTaskWithId':
      JCProvider.showTaskWithId(message.text!);
      break;
    case 'deleteTaskWithId':
      JCProvider.deleteTaskWithId(message.text!);
      break;
    case 'exportTaskWithId':
      JCProvider.exportTaskWithId(message.text!);
      break;
    case 'resetState':
      await JCProvider.resetState();
      break;
    case 'refreshOpenAiModels':
      const { apiConfiguration } = await JCProvider.getState();
      const openAiModels = await JCProvider.getOpenAiModels(
        apiConfiguration.openAiBaseUrl,
        apiConfiguration.openAiApiKey
      );
      JCProvider.postMessageToWebview({ type: 'openAiModels', openAiModels });
      break;
    case 'openImage':
      openImage(message.text!);
      break;
    case 'openFile':
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (workspaceFolders && workspaceFolders.length > 0) {
        // 直接传递原始路径给 openFile 函数，让它处理路径解析
        openFile(message.text!);
      } else {
        vscode.window.showErrorMessage(JoyCoderMCPMessageMap['No workspace folder open']);
      }
      break;

    case 'openMention':
      openMention(message.text);
      break;
    case 'checkpointDiff': {
      if (message.number) {
        await JCProvider.getCurrentJoyCoder()?.presentMultifileDiff(message.number, false);
      }
      break;
    }
    case 'checkpointRestore': {
      await JCProvider.cancelTask(); // we cannot alter message history say if the task is active, as it could be in the middle of editing a file or running a command, which expect the ask to be responded to rather than being superceded by a new message eg add deleted_api_reqs
      // cancel task waits for any open editor to be reverted and starts a new joycoder instance
      if (message.number) {
        // wait for messages to be loaded
        await pWaitFor(() => JCProvider.getCurrentJoyCoder()?.isInitialized === true, {
          timeout: 3_000,
        }).catch(() => {
          console.error('Failed to init new joycoder instance');
        });
        // NOTE: cancelTask awaits abortTask, which awaits diffViewProvider.revertChanges, which reverts any edited files, allowing us to reset to a checkpoint rather than running into a state where the revertChanges function is called alongside or after the checkpoint reset
        await restoreCheckpoint(
          JCProvider.getCurrentJoyCoder() as JoyCoder,
          message.number,
          message.text! as JoyCoderCheckpointRestore
        );
      }
      break;
    }
    case 'taskCompletionViewChanges': {
      if (message.number) {
        await JCProvider.getCurrentJoyCoder()?.presentMultifileDiff(message.number, true);
      }
      break;
    }
    case 'cancelTask':
      JCProvider.cancelTask();
      break;
    case 'chat-get-system':
      const isMac = os.platform() === 'darwin';
      await JCProvider.postMessageToWebview({ type: 'updatePlatformInfo', data: { isMac, platform: os.platform() } });
      break;
    case 'getLatestState':
      await JCProvider.postStateToWebview();
      break;
    case 'subscribeEmail':
      JCProvider.subscribeEmail(message.text);
      break;
    case 'openMcpSettings': {
      const mcpSettingsFilePath = await JCProvider.mcpHub?.getMcpSettingsFilePath();
      if (mcpSettingsFilePath) {
        openFile(mcpSettingsFilePath);
      }
      break;
    }
    case 'openProjectMcpSettings': {
      const mcpSettingsFilePath = await JCProvider.mcpHub?.getMcpSettingsFilePath('initial');
      if (mcpSettingsFilePath) {
        const absolutePath = vscode.Uri.parse(mcpSettingsFilePath).path;
        openFile(absolutePath);
      }
      break;
    }
    case 'toggleMcpServer': {
      try {
        await JCProvider.mcpHub?.toggleServerDisabled(message.serverName!, message.disabled!);
      } catch (error) {
        console.error(`Failed to toggle MCP server ${message.serverName}:`, error);
      }
      break;
    }
    case 'toggleToolAutoApprove': {
      try {
        await JCProvider.mcpHub?.toggleToolAutoApprove(message.serverName!, message.toolName!, message.autoApprove!);
      } catch (error) {
        console.error(`Failed to toggle auto-approve for tool ${message.toolName}:`, error);
      }
      break;
    }
    case 'restartMcpServer': {
      try {
        await JCProvider.mcpHub?.restartConnection(message.text!);
      } catch (error) {
        console.error(`Failed to retry connection for ${message.text}:`, error);
      }
      break;
    }
    case 'updateServerConnections': {
      try {
        await JCProvider.mcpHub?.updateMcpServers();
      } catch (error) {
        console.error(`Failed to retry connection for ${message.text}:`, error);
      }
      break;
    }
    case 'openExtensionSettings': {
      const settingsFilter = message.text || '';
      await vscode.commands.executeCommand(
        'workbench.action.openSettings',
        `@ext:${PLUGIN_ID} ${settingsFilter}`.trim() // trim whitespace if no settings filter
      );
      break;
    }
    // Add more switch case statements here as more webview message commands
    // are created within the webview context (i.e. inside media/main.js)
    case 'CHECK_LOGIN_STATUS':
      const isLogin = await checkLogin();
      await JCProvider.postMessageToWebview({ type: 'updateLoginStatus', data: { isLogin } });
      break;
    case 'CHECK_PLUGIN_TYPE':
      const isIde = await isIDE();
      await JCProvider.postMessageToWebview({ type: 'updatePluginType', data: { isIde } });
      break;
    case 'JUMP_LOGIN':
      await forceJdhLogin(false);
      break;
    case 'clearAllTaskHistory': {
      await JCProvider.deleteAllTaskHistory();
      await JCProvider.postStateToWebview();
      await JCProvider.refreshTotalTasksSize();
      JCProvider.postMessageToWebview({ type: 'relinquishControl' });
      break;
    }
    case 'joycoder-set-mode': {
      await JCProvider.handleModeSwitch(message.text as Mode);
      // await JCProvider.postStateToWebview();
      break;
    }
    case 'openSettings': {
      if (message.text) {
        await vscode.commands.executeCommand('JoyCode.config.setting', { type: message.text });
      } else {
        await vscode.commands.executeCommand('JoyCode.config.setting');
      }
      break;
    }
    case 'chatgpt-webview-report': {
      const reportData = message.reportData as IActionCustomReportParam;
      reportAction(reportData);
      if ([ActionType.aCopy, ActionType.copy].indexOf(reportData.actionType) >= 0) {
        AdoptResultCache.setRemote(
          reportData.result || '',
          reportData.model || '',
          reportData.actionType === ActionType.aCopy
            ? AdoptResultCache.ADOPT_CODE_SOURCE.CHAT_CLICK_COPY
            : AdoptResultCache.ADOPT_CODE_SOURCE.CHAT_COPY,
          reportData.conversationId
        );
      }
      break;
    }
    case 'applicationGenerationConfirmed': {
      if (message.applicationGenerationParams) {
        await JCProvider.handleApplicationGenerationConfirmed(message.applicationGenerationParams);
      }
      break;
    }
    case 'updatePrompt':
      if (message.promptMode && message.customPrompt !== undefined) {
        const existingPrompts = JCProvider.getGlobalState('customModePrompts') ?? {};
        const updatedPrompts = { ...existingPrompts, [message.promptMode]: message.customPrompt };
        await JCProvider.updateGlobalState('customModePrompts', updatedPrompts);
        const currentState = await JCProvider.getStateToPostToWebview();
        const stateWithPrompts = { ...currentState, customModePrompts: updatedPrompts } as ExtensionState;
        JCProvider.postMessageToWebview({ type: 'state', state: stateWithPrompts });
      }
      break;
    case 'openCustomModesSettings': {
      const customModesFilePath = await JCProvider.customModesManager?.getCustomModesFilePath();
      if (customModesFilePath) {
        openFile(customModesFilePath);
      }
      break;
    }
    case 'getThinkingMode': {
      // 获取思考模式状态
      const thinkingMode = (await JCProvider.getGlobalState('thinkingMode')) ?? false;
      await JCProvider.postMessageToWebview({
        type: 'thinkingModeState',
        value: Boolean(thinkingMode),
      });
      break;
    }
    case 'setThinkingMode': {
      // 设置思考模式状态
      if (message.value !== undefined) {
        await JCProvider.updateGlobalState('thinkingMode', message.value);
        // 不需要再发送消息回前端，因为前端已经更新了状态
      }
      break;
    }
    case 'getWebSearchEnabled': {
      // 获取联网搜索状态
      const webSearchEnabled = (await JCProvider.getGlobalState('webSearchEnabled')) ?? false;
      await JCProvider.postMessageToWebview({
        type: 'webSearchEnabledState',
        value: Boolean(webSearchEnabled),
      });
      break;
    }
    case 'setWebSearchEnabled': {
      // 设置联网搜索状态
      if (message.value !== undefined) {
        await JCProvider.updateGlobalState('webSearchEnabled', message.value);
        // 不需要再发送消息回前端，因为前端已经更新了状态
      }
      break;
    }
    case 'getAutoExecute': {
      // 获取自动执行状态
      const autoExecute = (await JCProvider.getWorkspaceState('autoExecute')) ?? false;
      await JCProvider.postMessageToWebview({
        type: 'autoExecuteState',
        value: Boolean(autoExecute),
      });
      break;
    }
    case 'setAutoExecute': {
      // 设置自动执行状态
      if (message.value !== undefined) {
        await JCProvider.updateWorkspaceState('autoExecute', message.value);
        // 更新当前 JoyCoder 实例的状态
        if (JCProvider.getCurrentJoyCoder()) {
          await (JCProvider.getCurrentJoyCoder() as JoyCoder).updateWorkspaceAutoExecute(message.value);
        }
      }
      break;
    }
    case 'enhancePrompt':
      if (message.text) {
        try {
          const { apiConfiguration, customSupportPrompts } = await JCProvider.getState();

          // Try to get enhancement config first, fall back to current config.
          let configToUse = apiConfiguration;
          const enhancedPrompt = await singleCompletionHandler(
            configToUse,
            supportPrompt.create('ENHANCE', { userInput: message.text }, customSupportPrompts)
          );
          await JCProvider.postMessageToWebview({ type: 'enhancedPrompt', text: enhancedPrompt });
        } catch (error) {
          console.log(`Error enhancing prompt: ${JSON.stringify(error, Object.getOwnPropertyNames(error), 2)}`);
          await JCProvider.postMessageToWebview({ type: 'enhancedPrompt' });
        }
      }
      break;
    case 'updateCustomMode':
      if (message.modeConfig) {
        await JCProvider.customModesManager?.updateCustomMode(message.modeConfig.agentId, message.modeConfig);
        // Update state after saving the mode
        const customModes = await JCProvider.customModesManager?.getCustomModes();
        await JCProvider.updateGlobalState('customModes', customModes);
        await JCProvider.updateStateByName('mode', message.modeConfig.agentId);
        // await JCProvider.updateGlobalState('mode', message.modeConfig.agentId);
        await JCProvider.postStateToWebview();
      }
      break;
    case 'deleteCustomMode':
      if (message.agentId) {
        const answer = await vscode.window.showInformationMessage('确定要删除自定义模式吗？', { modal: true }, '是');
        if (answer !== '是') {
          break;
        }
        await JCProvider.customModesManager?.deleteCustomMode(message.agentId);
        // Switch back to default mode after deletion
        await JCProvider.updateStateByName('mode', defaultModeSlug);
        // await JCProvider.updateGlobalState('mode', defaultModeSlug);
        await JCProvider.postStateToWebview();
      }
      break;
    case 'OneKeyCreateAgent':
      if (message?.text) {
        const agentInfo = await JCProvider.createAgent(message.text);
        if (agentInfo) {
          await JCProvider.postMessageToWebview({
            type: 'postAgentInfo',
            agentInfo,
          });
        }
      }
      break;
    case 'openMcpMarket':
      vscode.commands.executeCommand('JoyCode.config.setting', { type: 'mcp' });
      break;
    case 'webview-todo-list-change':
      const { newTodoList } = message?.values || {};
      if (Array.isArray(newTodoList)) {
        try {
          // 更新todo列表
          setPendingTodoList(newTodoList);
          // console.log('Todo list updated successfully:', newTodoList);
        } catch (error) {
          console.error('Failed to update todo list:', error);
        }
      } else {
        console.warn('Invalid todo list data received:', message?.values);
      }
      break;
    case 'openAiResources':
      try {
        vscode.commands.executeCommand('workbench.resource.toggleSidebar', message.bool);
      } catch (error) {
        console.error(`[openAiResources]:${error}`);
      }
      break;
    case 'executeCommand':
      if (message.command) {
        try {
          await vscode.commands.executeCommand(message.command, message.commandArgs);
        } catch (error) {
          console.error(`Failed to execute command ${message.command}:`, error);
        }
      }
      break;
    case 'deployProject': {
      // 处理部署项目请求
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (workspaceFolders && workspaceFolders.length > 0) {
        // 获取第一个工作区文件夹的路径
        const authority = workspaceFolders[0].uri.authority;

        // 从路径中提取项目ID（下划线后的数字）
        const folderName = authority.split('/').pop() || '';
        const projectId = folderName.split('_').pop() || authority;

        // 使用从前端传递的文本拼接部署消息
        const deployMessagePrefix = message.text || '帮我部署下项目，项目id是';
        const deployMessage = `${deployMessagePrefix}${projectId}`;

        // 检查是否已有JoyCoder实例，如果有则直接发送消息响应，避免重复创建任务
        const currentJoyCoder = JCProvider.getCurrentJoyCoder();
        if (currentJoyCoder) {
          // 直接调用JoyCoder的handleWebviewAskResponse方法，避免触发新任务创建
          currentJoyCoder.handleWebviewAskResponse('messageResponse', deployMessage);
        } else {
          // 如果没有JoyCoder实例，创建新任务而不是发送sendMessage避免循环调用
          await JCProvider.initJoyCoderWithTask(deployMessage);
        }
      } else {
        // 可以选择显示错误消息给用户
        vscode.window.showErrorMessage('没有打开的工作区，无法获取项目路径');
      }
      break;
    }
  }
}
