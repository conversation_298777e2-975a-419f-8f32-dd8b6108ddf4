import { JoyCoderRestoreMessageMap } from '../../../adaptor/translate/message';
import {
  BrowserAction,
  browserActions,
  JoyCoderSayBrowserAction,
  BrowserActionResult,
} from '../../../shared/ExtensionMessage';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import { pushToolResult, removeClosingTag, askApproval, handleError } from './common';

export async function useBrowserTool(joyCoder: JoyCoder, block: ToolUse) {
  const action: BrowserAction | undefined = block.params.action as BrowserAction;
  const url: string | undefined = block.params.url;
  const coordinate: string | undefined = block.params.coordinate;
  const size: string | undefined = block.params.size;
  const text: string | undefined = block.params.text;
  const file_path: string | undefined = block.params.file_path;

  if (!action || !browserActions.includes(action)) {
    // checking for action to ensure it is complete and valid
    if (!block.partial) {
      // if the block is complete and we don't have a valid action this is a mistake
      joyCoder.consecutiveMistakeCount++;
      await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('use_browser', 'action'));
      await joyCoder.browserSession.closeBrowser();
      await joyCoder.saveCheckpoint();
    }
    return;
  }

  try {
    if (block.partial) {
      if (action === 'launch') {
        if (joyCoder.shouldAutoApproveTool(block.name)) {
          joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'use_browser_launch');
          await joyCoder.say(
            'use_browser_launch',
            removeClosingTag(joyCoder, 'url', url, block.partial),
            undefined,
            block.partial
          );
        } else {
          joyCoder.removeLastPartialMessageIfExistsWithType('say', 'use_browser_launch');
          await joyCoder
            .ask('use_browser_launch', removeClosingTag(joyCoder, 'url', url, block.partial), block.partial)
            .catch(() => {});
        }
      } else {
        await joyCoder.say(
          'use_browser',
          JSON.stringify({
            action: action as BrowserAction,
            coordinate: removeClosingTag(joyCoder, 'coordinate', coordinate, block.partial),
            text: removeClosingTag(joyCoder, 'text', text, block.partial),
            file_path: removeClosingTag(joyCoder, 'file_path', file_path, block.partial),
          } as JoyCoderSayBrowserAction),
          undefined,
          block.partial
        );
      }
      return;
    } else {
      let browserActionResult: BrowserActionResult;
      if (action === 'launch') {
        if (!url) {
          joyCoder.consecutiveMistakeCount++;
          await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('use_browser', 'url'));
          await joyCoder.browserSession.closeBrowser();
          await joyCoder.saveCheckpoint();
          return;
        }
        joyCoder.consecutiveMistakeCount = 0;

        if (joyCoder.shouldAutoApproveTool(block.name)) {
          joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'use_browser_launch');
          await joyCoder.say('use_browser_launch', url, undefined, false);
          joyCoder.consecutiveAutoApprovedRequestsCount++;
        } else {
          // showNotificationForApprovalIfAutoApprovalEnabled(joyCoder, `JoyCode 想要搜索文件在 ${url}`);
          joyCoder.removeLastPartialMessageIfExistsWithType('say', 'use_browser_launch');
          const didApprove = await askApproval(
            joyCoder,
            block,
            'use_browser_launch',
            url,
            joyCoder.conversationId,
            block.userContent
          );
          if (!didApprove) {
            await joyCoder.saveCheckpoint();
            return;
          }
        }

        // NOTE: it's okay that we call this message since the partial inspect_site is finished streaming. The only scenario we have to avoid is sending messages WHILE a partial message exists at the end of the messages array. For example the api_req_finished message would interfere with the partial message, so we needed to remove that.
        // await joyCoder.say("inspect_site_result", "") // no result, starts the loading spinner waiting for result
        await joyCoder.say('use_browser_result', ''); // starts loading spinner

        await joyCoder.browserSession.launchBrowser();
        browserActionResult = await joyCoder.browserSession.navigateToUrl(url);
      } else {
        if (action === 'click' || action === 'hover') {
          if (!coordinate) {
            joyCoder.consecutiveMistakeCount++;
            await pushToolResult(
              joyCoder,
              block,
              await joyCoder.sayAndCreateMissingParamError('use_browser', 'coordinate')
            );
            await joyCoder.browserSession.closeBrowser();
            return; // can't be within an inner switch
          }
        }
        if (action === 'type') {
          if (!text) {
            joyCoder.consecutiveMistakeCount++;
            await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('use_browser', 'text'));
            await joyCoder.browserSession.closeBrowser();

            return;
          }
        }
        if (action === 'save_screenshot') {
          if (!file_path) {
            joyCoder.consecutiveMistakeCount++;
            await pushToolResult(
              joyCoder,
              block,
              await joyCoder.sayAndCreateMissingParamError('use_browser', 'file_path')
            );
            await joyCoder.browserSession.closeBrowser();

            return;
          }
        }
        joyCoder.consecutiveMistakeCount = 0;
        await joyCoder.say(
          'use_browser',
          JSON.stringify({
            action: action as BrowserAction,
            coordinate,
            text,
            file_path,
          } as JoyCoderSayBrowserAction),
          undefined,
          false
        );
        switch (action) {
          case 'click':
            browserActionResult = await joyCoder.browserSession.click(coordinate!);
            break;
          case 'hover':
            browserActionResult = await joyCoder.browserSession.hover(coordinate!);
            break;
          case 'type':
            browserActionResult = await joyCoder.browserSession.type(text!);
            break;
          case 'scroll_down':
            browserActionResult = await joyCoder.browserSession.scrollDown();
            break;
          case 'scroll_up':
            browserActionResult = await joyCoder.browserSession.scrollUp();
            break;
          case 'resize':
            browserActionResult = await joyCoder.browserSession.resize(size!);
            break;
          case 'save_screenshot':
            browserActionResult = await joyCoder.browserSession.saveScreenshot(file_path!);
            break;
          case 'close':
            browserActionResult = await joyCoder.browserSession.closeBrowser();
            break;
        }
      }

      switch (action) {
        case 'launch':
        case 'click':
        case 'hover':
        case 'type':
        case 'scroll_down':
        case 'resize':
        case 'save_screenshot':
        case 'scroll_up': {
          await joyCoder.say('use_browser_result', JSON.stringify(browserActionResult));
          await pushToolResult(
            joyCoder,
            block,
            formatResponse.toolResult(
              `The browser action has been executed. The console logs and screenshot have been captured for your analysis.\n\nConsole logs:\n${
                browserActionResult.logs || '(No new logs)'
              }\n\n(REMEMBER: if you need to proceed to using non-\`use_browser\` tools or launch a new browser, you MUST first close this browser. For example, if after analyzing the logs and screenshot you need to edit a file, you must first close the browser before you can use the use_write_file tool.)`,
              browserActionResult.screenshot ? [browserActionResult.screenshot] : []
            )
          );
          break;
        }
        case 'close':
          await pushToolResult(
            joyCoder,
            block,
            formatResponse.toolResult(`The browser has been closed. You may now proceed to using other tools.`)
          );
          break;
      }

      return;
    }
  } catch (error) {
    await joyCoder.browserSession.closeBrowser(); // if any error occurs, the browser session is terminated
    await handleError(joyCoder, block, 'use_browser', error);
    // await joyCoder.saveCheckpoint();
    return;
  }
}
