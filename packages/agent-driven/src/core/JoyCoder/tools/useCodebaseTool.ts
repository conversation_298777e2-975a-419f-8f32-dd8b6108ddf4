import vscode from 'vscode';
import { CodebaseManager } from '@joycoder/shared';
import { CodebaseIndexingStatus } from '@joycoder/shared/src/types/codebase';
import { cwd } from 'process';
import { JoyCoderSayTool } from '../../../shared/ExtensionMessage';
import { FileSystemHelper } from '../../../utils/FileSystemHelper';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { removeClosingTag, pushToolResult, handleError } from './common';

export async function useCodeBaseTool(joyCoder: JoyCoder, block: ToolUse) {
  const query: string | undefined = block.params.query;
  const sharedMessageProps: JoyCoderSayTool = {
    tool: 'useCodeBase',
    query: removeClosingTag(joyCoder, 'query', query, block.partial),
  };
  try {
    if (block.partial) {
      const partialMessage = JSON.stringify({
        ...sharedMessageProps,
      } as JoyCoderSayTool);
      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'tool');
        await joyCoder.say('tool', partialMessage, undefined, block.partial);
      } else {
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'tool');
        await joyCoder.ask('tool', partialMessage, block.partial).catch(() => {});
      }
      return;
    } else {
      if (query) {
        const enableCodebase = vscode.workspace.getConfiguration('JoyCode').get<boolean>('enableCodebase', false);
        const codebaseIndexingStatus =
          ((await joyCoder.providerRef
            .deref()
            ?.context.workspaceState.get('codebaseIndexingStatus')) as CodebaseIndexingStatus) ??
          CodebaseIndexingStatus.UNINDEXED;
        const supportsCodebase = enableCodebase && codebaseIndexingStatus === CodebaseIndexingStatus.INDEXED;
        // // if (enableCodebase && codebaseIndexingStatus === CodebaseIndexingStatus.INDEXING) {
        // //   console.log('codebase正在索引中，将跳过查询codebase......');
        // // }
        if (!supportsCodebase) {
          joyCoder.say('use_codebase_not_support', codebaseIndexingStatus, undefined, false);
          await pushToolResult(
            joyCoder,
            block,
            'Due to the codebase index being disabled or currently in progress, the use_codebase tool is temporarily unavailable. You may try using other tools to obtain relevant information.'
          );
          //joyCoder.say('use_codebase_not_support', codebaseIndexingStatus, undefined, false);
          return;
        }

        const useCodeBaseResult = await CodebaseManager.codebaseSearch(query);
        useCodeBaseResult.files = useCodeBaseResult.files.map((fileRelPath: string | vscode.Uri) => {
          return FileSystemHelper.resolveUri(joyCoder.cwd, fileRelPath).toString();
        });
        // console.log('codebase处理过的文件地址', useCodeBaseResult);
        const completeMessage = JSON.stringify({
          ...sharedMessageProps,
          files: useCodeBaseResult.files,
        } as JoyCoderSayTool);
        await joyCoder.say('tool', completeMessage, undefined, false);
        await pushToolResult(joyCoder, block, useCodeBaseResult.context);
        // await pushToolResult(joyCoder,block,'You must provide a query to search the codebase.');
        return;
      }
      await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('use_codebase', 'query'));
      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, 'use_codebase', error);
    return;
  }
}
