import * as vscode from 'vscode';
const path = require('path');
import Anthropic from '@anthropic-ai/sdk';
const WebviewPanelManager = require('@joycoder/plugin-base-browser/src/webview-panel/WebviewPanelManager').default;
import { findLast } from 'lodash-es';
import { COMPLETION_RESULT_CHANGES_FLAG } from '../../../shared/ExtensionMessage';
import { ToolResponse } from '../../../shared/tools';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import {
  removeClosingTag,
  pushToolResult,
  askApproval,
  askFinishSubTaskApproval,
  toolDescription,
  handleError,
} from './common';

export async function attemptTaskDoneTool(joyCoder: JoyCoder, block: ToolUse) {
  const result: string | undefined = block.params.result;
  const preview: string | undefined = block.params.preview;
  const command: string | undefined =
    ((await joyCoder.providerRef.deref()?.getState()) || {})?.mode === 'chat' ? undefined : block.params.command;

  // 从配置项读取，是否允许todolist未全部完成的情况下结束任务，现在没有这个配置项，默认为true
  // const preventCompletionWithOpenTodos = vscode.workspace
  //   .getConfiguration()
  //   .get<boolean>('preventCompletionWithOpenTodos', true);
  const preventCompletionWithOpenTodos = true;

  // Check if there are incomplete todos (only if the setting is enabled)
  const hasIncompleteTodos = joyCoder.todoList && joyCoder.todoList.some((todo) => todo.status !== 'completed');

  if (preventCompletionWithOpenTodos && hasIncompleteTodos) {
    joyCoder.consecutiveMistakeCount++;
    // joyCoder.recordToolError('attempt_task_done');
    await pushToolResult(
      joyCoder,
      block,
      formatResponse.toolError(
        'Cannot complete task while there are incomplete todos. Please finish all todos before attempting completion.'
      )
    );
    return;
  }

  const addNewChangesFlagToLastCompletionResultMessage = async () => {
    // Add newchanges flag if there are new changes to the workspace
    try {
      const hasNewChanges = await joyCoder.doesLatestTaskCompletionHaveNewChanges();
      const lastCompletionResultMessage = findLast(joyCoder.JoyCoderMessages, (m) => m.say === 'completion_result');
      if (
        lastCompletionResultMessage &&
        hasNewChanges &&
        !lastCompletionResultMessage.text?.endsWith(COMPLETION_RESULT_CHANGES_FLAG)
      ) {
        lastCompletionResultMessage.text += COMPLETION_RESULT_CHANGES_FLAG;
      }
      await joyCoder.saveJoyCoderMessages();
    } catch (error) {
      console.warn('%c [ error ]-38', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  };

  // 浏览器状态校验函数
  async function shouldSkipBrowserOpen(targetUrl: string): Promise<boolean> {
    try {
      // 获取 WebviewPanelManager 实例
      const manager = WebviewPanelManager.instance;
      if (!manager) {
        console.log('WebviewPanelManager 实例不存在，允许打开浏览器');
        return false;
      }

      // 检查是否有活跃的浏览器面板
      const hasActivePanels = manager.panels && manager.panels.size > 0;
      if (!hasActivePanels) {
        console.log('没有活跃的浏览器面板，允许打开浏览器');
        return false;
      }

      // 检查当前面板是否加载了相同的URL
      if (manager.current && manager.current.browserPage) {
        try {
          const currentUrl = manager.current.browserPage.playwrightPage.url();
          console.log('当前浏览器URL:', currentUrl);
          console.log('目标URL:', targetUrl);

          // 标准化URL进行比较（移除尾部斜杠）
          const normalizedCurrentUrl = currentUrl.replace(/\/$/, '');
          const normalizedTargetUrl = targetUrl.replace(/\/$/, '');

          if (normalizedCurrentUrl === normalizedTargetUrl) {
            return true;
          }
        } catch (error) {
          console.log('获取当前浏览器URL失败:', error);
        }
      }

      console.log('URL不同或无法获取当前URL，允许打开浏览器');
      return false;
    } catch (error) {
      console.log('浏览器状态检查失败，允许打开浏览器:', error);
      return false;
    }
  }

  const openBroswer = async (preview: string) => {
    try {
      // 检测是否为URL
      const isUrl = /^https?:\/\/[\w.-]+(?::\d+)?(?:\/.*)?$/i.test(preview);

      // 检测是否为HTML文件
      const isHtmlFile = /\.html?$/i.test(preview);

      console.log('开始处理 preview:', preview, isUrl, isHtmlFile);

      let targetUrl = '';

      if (isUrl) {
        targetUrl = preview;
      } else if (isHtmlFile) {
        // 处理 cwd 可能是 string 或 vscode.Uri 的情况
        const cwdPath = typeof joyCoder.cwd === 'string' ? joyCoder.cwd : joyCoder.cwd.fsPath;
        const absolutePath = path.isAbsolute(preview) ? preview : path.resolve(cwdPath, preview);
        // 添加文件协议前缀
        targetUrl = `file://${absolutePath}`;
      }

      if (targetUrl) {
        // 检查是否应该跳过浏览器打开操作
        const shouldSkip = await shouldSkipBrowserOpen(targetUrl);
        if (shouldSkip) {
          console.log('浏览器已打开相同URL，跳过打开操作');
          return;
        }

        console.log('打开浏览器URL:', targetUrl);
        await vscode.commands.executeCommand('JoyCode.browser.open', targetUrl);
      }
    } catch (err) {
      console.error('Failed to open preview:', err);
    }
  };

  try {
    const lastMessage = joyCoder.JoyCoderMessages.at(-1);
    if (block.partial) {
      if (command && !joyCoder.parentTask) {
        // the attempt_task_done text is done, now we're getting command
        // remove the previous partial attempt_task_done ask, replace with say, post state to webview, then stream command

        // const secondLastMessage = joyCoder.JoyCoderMessages.at(-2)
        // NOTE: we do not want to auto approve a command run as part of the attempt_task_done tool
        if (lastMessage && lastMessage.ask === 'command') {
          // update command
          await joyCoder
            .ask('command', removeClosingTag(joyCoder, 'command', command, block.partial), block.partial)
            .catch(() => {});
        } else {
          // last message is completion_result
          // we have command string, which means we have the result as well, so finish it (doesnt have to exist yet)
          await joyCoder.say(
            'completion_result',
            removeClosingTag(joyCoder, 'result', result, block.partial),
            undefined,
            false
          );
          await joyCoder.saveCheckpoint(true);
          await addNewChangesFlagToLastCompletionResultMessage();
          await joyCoder
            .ask('command', removeClosingTag(joyCoder, 'command', command, block.partial), block.partial)
            .catch(() => {});
        }
      } else if (preview && !joyCoder.parentTask) {
        openBroswer(preview);
      } else {
        // no command, still outputting partial result
        await joyCoder.say(
          'completion_result',
          removeClosingTag(joyCoder, 'result', result, block.partial),
          undefined,
          block.partial
        );
      }
      return;
    } else {
      if (!result) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('attempt_task_done', 'result')
        );
        return;
      }
      joyCoder.consecutiveMistakeCount = 0;

      let commandResult: ToolResponse | undefined = undefined;
      if (command && !joyCoder.parentTask) {
        if (lastMessage && lastMessage.ask !== 'command') {
          // havent sent a command message yet so first send completion_result then command
          await joyCoder.say(
            'completion_result',
            JSON.stringify({
              text: result,
              conversationId: joyCoder?.conversationId ?? '',
              taskId: joyCoder?.taskId ?? '',
              sessionId: joyCoder?.sessionId ?? '',
            }),
            undefined,
            false
          );

          await joyCoder.saveCheckpoint(true);
          await addNewChangesFlagToLastCompletionResultMessage();
        } else {
          // we already sent a command message, meaning the complete completion message has also been sent
          await joyCoder.saveCheckpoint(true);
        }

        // complete command message
        const didApprove = await askApproval(
          joyCoder,
          block,
          'command',
          command,
          joyCoder.conversationId,
          block.userContent
        );
        if (!didApprove) {
          await joyCoder.saveCheckpoint();
          return;
        }

        const [userRejected, execCommandResult] = await joyCoder.executeCommandTool(command!);
        if (userRejected) {
          joyCoder.didRejectTool = true;
          await pushToolResult(joyCoder, block, execCommandResult);
          await joyCoder.saveCheckpoint();
          return;
        }
        // user didn't reject, but the command may have output
        commandResult = execCommandResult;
      } else if (preview && !joyCoder.parentTask) {
        openBroswer(preview);
      } else {
        await joyCoder.say(
          'completion_result',
          JSON.stringify({
            text: result ?? '',
            conversationId: joyCoder?.conversationId ?? '',
            taskId: joyCoder?.taskId ?? '',
            sessionId: joyCoder?.sessionId ?? '',
          }),
          undefined,
          false
        );

        await joyCoder.saveCheckpoint(true);
        await addNewChangesFlagToLastCompletionResultMessage();
      }

      if (joyCoder.parentTask) {
        const didApprove = await askFinishSubTaskApproval(joyCoder, block);
        if (!didApprove) {
          return;
        }
        // tell the provider to remove the current subtask and resume the previous task in the stack
        await joyCoder.providerRef.deref()?.finishSubTask(result);
        return;
      }

      // we already sent completion_result says, an empty string asks relinquishes control over button and field
      const { response, text, images } = await joyCoder.ask('completion_result', '', false, undefined, !!command);
      if (response === 'yesButtonClicked') {
        await pushToolResult(joyCoder, block, ''); // signals to recursive loop to stop (for now this never happens since yesButtonClicked will trigger a new task)
        return;
      }
      joyCoder.newSession();

      if (text || images) {
        await joyCoder.say('user_feedback', text ?? '', images);
      }

      const toolResults: (Anthropic.TextBlockParam | Anthropic.ImageBlockParam)[] = [];
      if (commandResult) {
        if (typeof commandResult === 'string') {
          toolResults.push({
            type: 'text',
            text: commandResult,
          });
        } else if (Array.isArray(commandResult)) {
          toolResults.push(...commandResult);
        }
      }
      toolResults.push({
        type: 'text',
        text: `The user has provided feedback on the results. Consider their input to continue the task, and then attempt completion again.\n<feedback>\n${text}\n</feedback>`,
      });
      toolResults.push(...formatResponse.imageBlocks(images));
      joyCoder.userMessageContent.push({
        type: 'text',
        text: `${await toolDescription(block, joyCoder)} Result:`,
      });
      joyCoder.userMessageContent.push(...toolResults);
      return;
    }
  } catch (error) {
    console.warn('%c [ error ]-205', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    await handleError(joyCoder, block, 'attempt_task_done', error);
    // await joyCoder.saveCheckpoint();
    return;
  }
}
