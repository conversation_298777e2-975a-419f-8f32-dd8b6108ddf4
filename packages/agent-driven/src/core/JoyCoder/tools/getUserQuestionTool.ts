import { findLast } from 'lodash';
import { JoyCoderRestoreMessageMap } from '../../../adaptor/translate/message';
import { parsePartialArrayString } from '../../../shared/array';
import { JoyCoderAskQuestion } from '../../../shared/ExtensionMessage';
import { parseXml } from '../../../utils/xml';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import { removeClosingTag, pushToolResult, handleError } from './common';
import { Logger } from '@joycoder/shared';

export async function getUserQuestionTool(joyCoder: JoyCoder, block: ToolUse) {
  const question: string | undefined = block.params.question;
  const optionsRaw: string | undefined = block.params.options;
  const follow_up: string | undefined = block.params.follow_up;

  const sharedMessage = {
    question: removeClosingTag(joyCoder, 'question', question, block.partial),
    options: parsePartialArrayString(removeClosingTag(joyCoder, 'options', optionsRaw, block.partial)),
  } as JoyCoderAskQuestion;
  try {
    if (block.partial) {
      // await joyCoder.ask('followup', removeClosingTag(joyCoder,'question', question,block.partial), block.partial).catch(() => {});
      await joyCoder.ask('followup', JSON.stringify(sharedMessage), block.partial).catch(() => {});
      return;
    } else {
      if (!question) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('get_user_question', 'question', 'followup')
        );
        // await joyCoder.saveCheckpoint();
        return;
      }
      joyCoder.consecutiveMistakeCount = 0;
      type Suggest = { answer: string };

      const follow_up_json: { question: string; suggest: Suggest[]; options?: string[] } = {
        question,
        suggest: [] as Suggest[],
      };
      if (!joyCoder.shouldAutoApproveTool(block.name)) {
        if (follow_up) {
          // eslint-disable-next-line init-declarations
          let parsedSuggest: {
            suggest: Suggest[] | Suggest;
          };

          try {
            parsedSuggest = parseXml(follow_up, ['suggest']) as { suggest: Suggest[] | Suggest };
          } catch (error) {
            joyCoder.consecutiveMistakeCount++;
            Logger.error(`Failed to parse operations: ${error.message}`);
            // await joyCoder.say('error', `Failed to parse operations: ${error.message}`);
            await pushToolResult(joyCoder, block, formatResponse.toolError('Invalid operations xml format'));
            return;
          }

          const normalizedSuggest = Array.isArray(parsedSuggest?.suggest)
            ? parsedSuggest.suggest
            : [parsedSuggest?.suggest].filter((sug): sug is Suggest => sug !== undefined);

          follow_up_json.suggest = normalizedSuggest;
          follow_up_json.options = [];
        }
      }
      // const { text, images } = await joyCoder.ask('followup', question, false);
      const { text, images } = await joyCoder.ask(
        'followup',
        JSON.stringify({ ...sharedMessage, ...follow_up_json }),
        false
      );

      // Check if options contains the text response
      if (optionsRaw && text && parsePartialArrayString(optionsRaw).includes(text)) {
        // Valid option selected, don't show user message in UI
        // Update last followup message with selected option
        const lastFollowupMessage = findLast(joyCoder.JoyCoderMessages, (m) => m.ask === 'followup');
        if (lastFollowupMessage) {
          lastFollowupMessage.text = JSON.stringify({
            ...sharedMessage,
            ...follow_up_json,
            selected: text,
          } as JoyCoderAskQuestion);
          await joyCoder.saveJoyCoderMessages();
        }
      } else {
        // Option not selected, send user feedback
        await joyCoder.say('user_feedback', text ?? '', images);
      }
      await pushToolResult(joyCoder, block, formatResponse.toolResult(`<answer>\n${text}\n</answer>`, images));
      await joyCoder.saveCheckpoint();
      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, 'get_user_question', error);
    // await joyCoder.saveCheckpoint();
    return;
  }
}
