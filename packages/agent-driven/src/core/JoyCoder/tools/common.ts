import { reportAction, ActionType, WorkspaceState, Logger } from '@joycoder/shared';
import { serializeError } from 'serialize-error';
import { showSystemNotification } from '../../../integrations/notifications';
import { JoyCoderAsk } from '../../../shared/ExtensionMessage';
import { ToolResponse } from '../../../shared/tools';
import { ToolParamName, ToolUse, ToolUseName } from '../../assistant-message';
import { JoyCoder, UserContent } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import { defaultModeSlug, getModeBySlug } from '../../../../web-agent/src/utils/modes';
import delay from 'delay';
import { getReadFileToolDescription } from './useReadFileTool';

export const toolDescription = async (block: ToolUse, joyCoder: JoyCoder) => {
  switch (block.name) {
    case 'use_command':
      return `[${block.name} for '${block.params.command}']`;
    case 'use_read_file':
      return getReadFileToolDescription(block.name, block.params);
    // return `[${block.name} for '${block.params.path}']`;
    case 'use_write_file':
      return `[${block.name} for '${block.params.path}']`;
    case 'use_search_and_replace':
      return `[${block.name} for '${block.params.path}']`;
    case 'insert_content':
      return `[${block.name} for '${block.params.path}']`;
    case 'fetch_instructions':
      return `[${block.name} for '${block.params.task}']`;
    case 'apply_diff':
      if (block.params.path) {
        return `[${block.name} for '${block.params.path}']`;
      }
      return `[${block.name}]`;
    case 'use_search_files':
      return `[${block.name} for '${block.params.regex}'${
        block.params.file_pattern ? ` in '${block.params.file_pattern}'` : ''
      }]`;
    case 'use_list_files':
      return `[${block.name} for '${block.params.path}']`;
    case 'use_definition_names':
      return `[${block.name} for '${block.params.path}']`;
    case 'use_browser':
      return `[${block.name} for '${block.params.action}']`;
    case 'use_mcp_tools':
      return `[${block.name} for '${block.params.server_name}']`;
    case 'get_mcp_resource':
      return `[${block.name} for '${block.params.server_name}']`;
    case 'get_user_question':
      return `[${block.name} for '${block.params.question}']`;
    case 'attempt_task_done':
      return `[${block.name}]`;
    case 'get_mcp_instructions':
      return `[${block.name}]`;
    case 'use_web_search':
      return `[${block.name} for '${block.params.query}']`;
    case 'use_codebase':
      return `[${block.name} for '${block.params.query}']`; // Add case for the new tool
    case 'switch_mode':
      return `[${block.name} to '${block.params.mode_slug}'${
        block.params.reason ? ` because: ${block.params.reason}` : ''
      }]`;
    case 'new_task_creation': {
      const mode = block.params.mode ?? defaultModeSlug;
      const message = block.params.message ?? '(no message)';
      const customModes = ((await joyCoder.providerRef.deref()?.getState()) || {}).customModes;
      const modeName = getModeBySlug(mode, customModes)?.name ?? mode;
      return `[${block.name} in ${modeName} mode: '${message}']`;
      // return `[${block.name} for creating a new task]`;
    }
    case 'new_task_with_condense_context':
      return `[${block.name} for creating a new task]`;
    case 'condense':
      return `[${block.name}]`;
    case 'use_clear_publish':
      return `[${block.name} for project '${block.params.project_name}' at '${block.params.project_path}']`;
    case 'update_todo_list':
      return `[${block.name}]`;
    default:
      return '';
  }
};

export const pushToolResult = async (joyCoder: JoyCoder, block: ToolUse, content: ToolResponse) => {
  joyCoder.userMessageContent.push({
    type: 'text',
    text: `${await toolDescription(block, joyCoder)} Result:`,
  });
  if (typeof content === 'string') {
    joyCoder.userMessageContent.push({
      type: 'text',
      text: content || '(tool did not return anything)',
    });
  } else {
    joyCoder.userMessageContent.push(...content);
  }
  // once a tool result has been collected, ignore all other tool uses since we should only ever present one tool result per message
  joyCoder.didAlreadyUseTool = true;
};

export const pushAdditionalToolFeedback = async (
  joyCoder: JoyCoder,
  block: ToolUse,
  feedback?: string,
  images?: string[]
) => {
  if (!feedback && !images) {
    return;
  }
  const content = formatResponse.toolResult(
    `The user denied this operation and provided the following feedback:\n<feedback>\n${feedback}\n</feedback>`,
    images
  );
  await pushToolResult(joyCoder, block, content);
  if (typeof content === 'string') {
    joyCoder.userMessageContent.push({
      type: 'text',
      text: content,
    });
  } else {
    joyCoder.userMessageContent.push(...content);
  }
};
export const askApproval = async (
  joyCoder: JoyCoder,
  block: ToolUse,
  type: JoyCoderAsk,
  partialMessage?: string,
  conversationId?: string,
  userContent?: UserContent
) => {
  const { response, text, images } = await joyCoder.ask(type, partialMessage, false);
  if (response !== 'yesButtonClicked') {
    if (text || images?.length) {
      await pushAdditionalToolFeedback(joyCoder, block, text, images);
      await joyCoder.say('user_feedback', text, images);
    } else {
      await pushToolResult(joyCoder, block, formatResponse.toolDenied());
    }
    joyCoder.didRejectTool = true;
    return false;
  } else {
    if (text || images?.length) {
      await pushAdditionalToolFeedback(joyCoder, block, text, images);
      await joyCoder.say('user_feedback', text, images);
    }
    try {
      reportAction({
        actionCate: 'ai',
        accept: 1,
        actionType: ActionType.copy,
        question: userContent?.map((item: any) => item?.text)?.join('\n'),
        result: text,
        conversationId: joyCoder.conversationId,
        model: WorkspaceState.get('openAiModelId'),
        startTime: new Date(),
        extendMsg: {
          type: 'yesButtonClicked',
          modeType: ((await joyCoder.providerRef.deref()?.getState()) || {}).mode,
          taskId: joyCoder.taskId,
          sessionId: joyCoder.sessionId,
        },
      });
    } catch (error) {
      console.error(
        '%c [ askApproval-reportAction-error ]-975',
        'font-size:13px; background:pink; color:#bf2c9f;',
        error
      );
    }
  }
  return true;
};

export const askFinishSubTaskApproval = async (joyCoder: JoyCoder, block: ToolUse): Promise<boolean> => {
  // Ask the user to approve this task has completed, and he has
  // reviewed it, and we can declare task is finished and return
  // control to the parent task to continue running the rest of
  // the sub-tasks.
  const toolMessage = JSON.stringify({ tool: 'finishTask' });
  if (joyCoder.shouldAutoApproveTool('new_task_creation')) {
    await delay(1000); // 让用户可以看到总结的内容，避免生成完毕后立刻返回
    return true;
  }
  return await askApproval(joyCoder, block, 'tool', toolMessage);
};

export const showNotificationForApprovalIfAutoApprovalEnabled = (joyCoder: JoyCoder, message: string) => {
  if (joyCoder.autoApprovalSettings.enabled && joyCoder.autoApprovalSettings.enableNotifications) {
    showSystemNotification({
      subtitle: '需要审批',
      message,
    });
  }
};

export const handleError = async (joyCoder: JoyCoder, block: ToolUse, action: string, error: Error) => {
  if (joyCoder.abandoned) {
    console.log('Ignoring error since task was abandoned (i.e. from task cancellation after resetting)');
    return;
  }
  const errorString = `${action} Error: ${JSON.stringify(serializeError(error))}`;
  // await joyCoder.say(
  //   'error',
  //   `${action}${JoyCoderRestoreMessageMap['Error']} :\n${
  //     error.message ?? JSON.stringify(serializeError(error), null, 2)
  //   }`,
  // );
  Logger.error(errorString);
  joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'tool');
  joyCoder.removeLastPartialMessageIfExistsWithType('say', 'tool');
  await pushToolResult(
    joyCoder,
    block,
    formatResponse.toolError(errorString) +
      '. Your next response should ONLY reply tool use XML-tags, DONOT apologize or reply any other words.'
  );
};

// If block is partial, remove partial closing tag so its not presented to user
export const removeClosingTag = (joyCoder: JoyCoder, tag: ToolParamName, text?: string, partial: boolean = false) => {
  if (!partial) {
    return text || '';
  }
  if (!text) {
    return '';
  }
  // this regex dynamically constructs a pattern to match the closing tag:
  // - Optionally matches whitespace before the tag
  // - Matches '<' or '</' optionally followed by any subset of characters from the tag name
  const tagRegex = new RegExp(
    `\\s?<\/?${tag
      .split('')
      .map((char) => `(?:${char})?`)
      .join('')}$`,
    'g'
  );
  return text.replace(tagRegex, '');
};
