import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import { removeClosingTag, pushToolResult, handleError } from './common';

export async function newTaskWithContextTool(joyCoder: JoyCoder, block: ToolUse) {
  const context: string | undefined = block.params.context;
  try {
    if (block.partial) {
      await joyCoder
        .ask('new_task_with_condense_context', removeClosingTag(joyCoder, 'context', context), block.partial)
        .catch(() => {});
      return;
    } else {
      if (!context) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('new_task_with_condense_context', 'context')
        );
        await joyCoder.saveCheckpoint();
        return;
      }
      joyCoder.consecutiveMistakeCount = 0;

      const { text, images } = await joyCoder.ask('new_task_with_condense_context', context, false);

      // If the user provided a response, treat it as feedback
      if (text || (images && images.length > 0)) {
        await joyCoder.say('user_feedback', text ?? '', images);
        pushToolResult(
          joyCoder,
          block,
          formatResponse.toolResult(
            `The user provided feedback instead of creating a new task:\n<feedback>\n${text}\n</feedback>`,
            images
          )
        );
      } else {
        // If no response, the user clicked the "Create New Task" button
        pushToolResult(
          joyCoder,
          block,
          formatResponse.toolResult(`The user has created a new task with the provided context.`)
        );
      }
      await joyCoder.saveCheckpoint();
      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, 'new_task_with_condense_context', error);
    // await joyCoder.saveCheckpoint();
    return;
  }
}
