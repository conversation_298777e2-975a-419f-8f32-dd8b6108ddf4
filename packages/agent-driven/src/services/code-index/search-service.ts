import * as path from 'path';
import { VectorStoreSearchResult } from './interfaces';
import { IEmbedder } from './interfaces/embedder';
import { IVectorStore } from './interfaces/vector-store';
import { SEARCH_MIN_SCORE } from './constants';

/**
 * Service responsible for searching the code index.
 */
export class CodeIndexSearchService {
  constructor(private readonly embedder: IEmbedder, private readonly vectorStore: IVectorStore) {}

  /**
   * 添加新文档到知识库
   * @param text 文档内容
   * @throws Error 如果文档内容为空或向量化失败
   */
  public async addDocument(text: string): Promise<void> {
    if (!text.trim()) {
      throw new Error('文档内容不能为空');
    }

    try {
      console.log('[CodeIndexSearchService] 开始生成文档向量...');
      const embeddingResponse = await this.embedder.createEmbeddings([text]);

      if (!embeddingResponse || !Array.isArray(embeddingResponse.embeddings)) {
        throw new Error('嵌入响应格式无效');
      }

      const vector = embeddingResponse.embeddings[0];
      if (!vector || !Array.isArray(vector) || vector.length === 0) {
        throw new Error('生成的向量无效或为空');
      }

      console.log('[CodeIndexSearchService] 成功生成文档向量，维度:', vector.length);

      await this.vectorStore.addPoint(vector, text);
    } catch (error) {
      console.error('[CodeIndexSearchService] Error adding document:', error);
      throw error;
    }
  }

  /**
   * 从知识库中删除指定文档
   * @param id 文档ID
   * @throws Error 如果文档ID为空或删除失败
   */
  public async deleteDocument(id: string): Promise<void> {
    if (!id.trim()) {
      throw new Error('文档ID不能为空');
    }

    try {
      await this.vectorStore.deletePoint(id);
    } catch (error) {
      console.error('[CodeIndexSearchService] Error deleting document:', error);
      throw error;
    }
  }

  /**
   * 更新知识库中的文档
   * @param id 文档ID
   * @param newText 新的文档内容
   * @throws Error 如果参数无效或更新失败
   */
  public async updateDocument(id: string, newText: string): Promise<void> {
    if (!id.trim()) {
      throw new Error('文档ID不能为空');
    }
    if (!newText.trim()) {
      throw new Error('文档内容不能为空');
    }

    try {
      const embeddingResponse = await this.embedder.createEmbeddings([newText]);
      const vector = embeddingResponse?.embeddings[0];
      if (!vector) {
        throw new Error('无法为更新内容生成语义向量');
      }

      await this.vectorStore.updatePoint(id, vector, newText);
    } catch (error) {
      console.error('[CodeIndexSearchService] Error updating document:', error);
      throw error;
    }
  }

  /**
   * 从知识库中查询文档（基于文本相似度）
   * @param query 查询文本
   * @param limit 返回结果数量限制
   * @returns 相似文档列表
   * @throws Error 如果查询文本为空或查询失败
   */
  public async queryDocuments(query: string, limit: number = 5): Promise<VectorStoreSearchResult[]> {
    if (!query.trim()) {
      throw new Error('查询文本不能为空');
    }

    try {
      const embeddingResponse = await this.embedder.createEmbeddings([query]);
      const vector = embeddingResponse?.embeddings[0];
      if (!vector) {
        throw new Error('无法为查询文本生成语义向量');
      }

      return await this.vectorStore.search(vector, undefined, SEARCH_MIN_SCORE, limit);
    } catch (error) {
      console.error('[CodeIndexSearchService] Error querying documents:', error);
      throw error;
    }
  }

  /**
   * Searches the code index for relevant content.
   * @param query The search query
   * @param limit Maximum number of results to return
   * @param directoryPrefix Optional directory path to filter results by
   * @returns Array of search results
   * @throws Error if the service is not properly configured or ready
   */
  public async searchIndex(query: string, directoryPrefix?: string): Promise<VectorStoreSearchResult[]> {
    const minScore = SEARCH_MIN_SCORE;
    try {
      // Generate embedding for query
      const embeddingResponse = await this.embedder.createEmbeddings([query]);
      const vector = embeddingResponse?.embeddings[0];
      if (!vector) {
        throw new Error('无法为当前内容生成语义向量。');
      }

      // Handle directory prefix
      let normalizedPrefix: string | undefined = undefined;
      if (directoryPrefix) {
        normalizedPrefix = path.normalize(directoryPrefix);
      }

      // Perform search
      const results = await this.vectorStore.search(vector, normalizedPrefix, minScore);
      return results;
    } catch (error) {
      console.error('[CodeIndexSearchService] Error during search:', error);
      throw error; // Re-throw the error after setting state
    }
  }

  /**
   * 批量更新文档到知识库
   * @param texts 文档内容数组
   * @throws Error 如果文档内容无效或处理失败
   * @returns Promise<void>
   */
  public async updateBatchPoints(texts: string[]): Promise<void> {
    // 参数验证
    if (!Array.isArray(texts) || texts.length === 0) {
      throw new Error('文档内容数组不能为空');
    }

    if (texts.some((text) => !text || typeof text !== 'string' || !text.trim())) {
      throw new Error('文档内容数组中包含无效的文本');
    }

    try {
      // 批量生成向量
      const embeddingResponse = await this.embedder.createEmbeddings(texts);
      console.log('%c [ embeddingResponse ]-164', 'font-size:13px; background:pink; color:#bf2c9f;', embeddingResponse);

      if (!embeddingResponse?.embeddings || embeddingResponse.embeddings.length !== texts.length) {
        throw new Error('向量生成失败或向量数量不匹配');
      }

      // 构造符合 PointStruct 类型的数据
      const points = embeddingResponse.embeddings.map((vector, index) => ({
        id: `batch_${Date.now()}_${index}`, // 生成唯一ID
        vector,
        payload: {
          codeChunk: texts[index],
          filePath: '', // 由调用方设置
          startLine: 0, // 由调用方设置
          endLine: 0, // 由调用方设置
        },
      }));

      // 使用 upsertPoints 进行批量更新
      await this.vectorStore.upsertPoints(points);
    } catch (error) {
      console.error('[CodeIndexSearchService] Error updating batch points:', error);
      throw error instanceof Error ? error : new Error('批量更新文档失败：' + String(error));
    }
  }
}
