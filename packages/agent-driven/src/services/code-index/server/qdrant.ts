import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import * as https from 'https';
import * as childProcess from 'child_process';
import { EventEmitter } from 'events';
import { Logger } from '@joycoder/shared';
import fetch from 'node-fetch';

// 定义二进制包的URL
const BINARY_URLS: Record<string, string> = {
  'darwin-arm64': 'https://storage.360buyimg.com/dist-dev/cdn/joyCoder/qdrant-aarch64-apple-darwin.tar.gz',
  'darwin-x64': 'https://storage.360buyimg.com/dist-dev/cdn/joyCoder/qdrant-x86_64-apple-darwin.tar.gz',
  'win32-x64': 'https://storage.360buyimg.com/dist-dev/cdn/joyCoder/qdrant-x86_64-pc-windows-msvc.zip',
};

// 定义Qdrant服务的默认端口
const DEFAULT_PORT = 6333;

// 定义Qdrant服务的状态
export enum QdrantServerStatus {
  STOPPED = 'stopped',
  STARTING = 'starting',
  RUNNING = 'running',
  ERROR = 'error',
}

/**
 * Qdrant服务管理类
 * 负责下载、解压和启动Qdrant服务
 */
export class QdrantServer extends EventEmitter {
  private static instance: QdrantServer;
  private serverProcess: childProcess.ChildProcess | null = null;
  private status: QdrantServerStatus = QdrantServerStatus.STOPPED;
  private binaryPath: string = '';
  private dataPath: string = '';
  private port: number = DEFAULT_PORT;
  private startupTimeout: NodeJS.Timeout | null = null;
  private startupPromise: Promise<void> | null = null;
  private startupResolver: (() => void) | null = null;
  private startupRejecter: ((error: Error) => void) | null = null;

  /**
   * 获取QdrantServer的单例实例
   */
  public static getInstance(): QdrantServer {
    if (!QdrantServer.instance) {
      QdrantServer.instance = new QdrantServer();
    }
    return QdrantServer.instance;
  }

  /**
   * 构造函数
   * 初始化路径和输出通道
   */
  private constructor() {
    super();

    // 设置二进制文件和数据存储的路径
    const extensionStoragePath = this.getExtensionStoragePath();
    this.binaryPath = path.join(extensionStoragePath, 'bin');
    this.dataPath = path.join(extensionStoragePath, 'data');

    // 确保目录存在
    this.ensureDirectoryExists(this.binaryPath);
    this.ensureDirectoryExists(this.dataPath);
  }

  /**
   * 获取扩展存储路径
   */
  private getExtensionStoragePath(): string {
    // 使用临时目录作为备选
    const basePath = path.join(os.homedir(), '.joycode', 'qdrant');
    this.ensureDirectoryExists(basePath);
    return basePath;
  }

  /**
   * 确保目录存在
   */
  private ensureDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }
  /**
   * 确保配置文件存在
   */
  private async ensureConfigFile(configPath: string): Promise<void> {
    if (!fs.existsSync(configPath)) {
      // 创建存储目录
      const storagePath = path.join(this.dataPath, 'storage');
      const snapshotsPath = path.join(this.dataPath, 'snapshots');
      const tempPath = path.join(this.dataPath, 'temp');

      this.ensureDirectoryExists(storagePath);
      this.ensureDirectoryExists(snapshotsPath);
      this.ensureDirectoryExists(tempPath);

      // 创建基本的 qdrant 配置文件
      // 使用正确的YAML格式，路径需要用引号包围以避免特殊字符问题
      const configContent = `log_level: INFO
service:
  host: 0.0.0.0
  port: ${this.port}
  enable_cors: true
storage:
  storage_path: "${storagePath.replace(/\\/g, '/')}"
  snapshots_path: "${snapshotsPath.replace(/\\/g, '/')}"
  temp_path: "${tempPath.replace(/\\/g, '/')}"
  performance:
    max_search_threads: 0
    max_optimization_threads: 0
telemetry_disabled: true`;

      // 确保配置文件目录存在
      this.ensureDirectoryExists(path.dirname(configPath));

      // 写入配置文件
      fs.writeFileSync(configPath, configContent, 'utf8');
      this.log(`已创建配置文件: ${configPath}`);
    }
  }

  /**
   * 获取当前系统的平台标识符
   * @returns 平台标识符，如 'darwin-arm64'
   */
  private getPlatformIdentifier(): string | null {
    const platform = os.platform();
    const arch = os.arch();

    if (platform === 'darwin') {
      if (arch === 'arm64') {
        return 'darwin-arm64';
      } else if (arch === 'x64') {
        return 'darwin-x64';
      }
    } else if (platform === 'win32' && arch === 'x64') {
      return 'win32-x64';
    }

    return null;
  }

  /**
   * 获取二进制文件的URL
   */
  private getBinaryUrl(): string | null {
    const platformId = this.getPlatformIdentifier();
    if (!platformId) {
      return null;
    }

    // 使用类型断言确保TypeScript理解这是有效的键
    const url = BINARY_URLS[platformId as keyof typeof BINARY_URLS];
    return url || null;
  }

  /**
   * 获取二进制文件的本地路径
   */
  private getExecutablePath(): string {
    const platform = os.platform();
    const executableName = platform === 'win32' ? 'qdrant.exe' : 'qdrant';
    return path.join(this.binaryPath, executableName);
  }

  /**
   * 下载文件
   * @param url 文件URL
   * @param destPath 目标路径
   */
  private async downloadFile(url: string, destPath: string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.log(`正在下载 ${url} 到 ${destPath}`);

      const file = fs.createWriteStream(destPath);

      https
        .get(url, (response) => {
          if (response.statusCode !== 200) {
            reject(new Error(`下载失败，状态码: ${response.statusCode}`));
            return;
          }

          response.pipe(file);

          file.on('finish', () => {
            file.close();
            this.log('下载完成');
            resolve();
          });
        })
        .on('error', (err) => {
          fs.unlink(destPath, () => {}); // 删除部分下载的文件
          reject(err);
        });
    });
  }

  /**
   * 解压tar.gz文件
   * @param filePath 文件路径
   * @param extractPath 解压路径
   */
  private async extractTarGz(filePath: string, extractPath: string): Promise<void> {
    this.log(`正在解压 ${filePath} 到 ${extractPath}`);

    return new Promise<void>((resolve, reject) => {
      const { exec } = childProcess;

      // 使用Node.js执行解压命令
      const command = `tar -xzf "${filePath}" -C "${extractPath}"`;

      exec(command, (error) => {
        if (error) {
          this.log(`解压失败: ${error.message}`);
          reject(error);
          return;
        }

        this.log('解压完成');
        resolve();
      });
    });
  }

  /**
   * 解压zip文件
   * @param filePath 文件路径
   * @param extractPath 解压路径
   */
  private async extractZip(filePath: string, extractPath: string): Promise<void> {
    this.log(`正在解压 ${filePath} 到 ${extractPath}`);

    return new Promise<void>((resolve, reject) => {
      const { exec } = childProcess;

      // 在Windows上使用内置的解压功能
      const command = `powershell -command "Expand-Archive -Path '${filePath}' -DestinationPath '${extractPath}' -Force"`;

      exec(command, (error) => {
        if (error) {
          this.log(`解压失败: ${error.message}`);
          reject(error);
          return;
        }

        this.log('解压完成');
        resolve();
      });
    });
  }

  /**
   * 设置二进制文件的执行权限
   * @param filePath 文件路径
   */
  private async setExecutablePermission(filePath: string): Promise<void> {
    // 仅在非Windows平台上设置执行权限
    if (os.platform() !== 'win32') {
      this.log(`设置执行权限: ${filePath}`);

      return new Promise<void>((resolve, reject) => {
        const { exec } = childProcess;

        exec(`chmod +x "${filePath}"`, (error) => {
          if (error) {
            this.log(`设置执行权限失败: ${error.message}`);
            reject(error);
            return;
          }

          resolve();
        });
      });
    }

    return Promise.resolve();
  }

  /**
   * 准备Qdrant二进制文件
   * 下载并解压二进制包
   */
  private async prepareBinary(): Promise<string> {
    const executablePath = this.getExecutablePath();

    // 如果二进制文件已存在，直接返回路径
    if (fs.existsSync(executablePath)) {
      this.log(`Qdrant二进制文件已存在: ${executablePath}`);
      return executablePath;
    }

    const binaryUrl = this.getBinaryUrl();
    if (!binaryUrl) {
      throw new Error('不支持的平台或架构');
    }

    const platform = os.platform();
    const isWindows = platform === 'win32';
    const archiveExt = isWindows ? '.zip' : '.tar.gz';
    const archivePath = path.join(os.tmpdir(), `qdrant${archiveExt}`);

    try {
      // 下载二进制包
      await this.downloadFile(binaryUrl, archivePath);

      // 解压二进制包
      if (isWindows) {
        await this.extractZip(archivePath, this.binaryPath);
      } else {
        await this.extractTarGz(archivePath, this.binaryPath);
      }

      // 设置执行权限
      await this.setExecutablePermission(executablePath);

      // 删除下载的归档文件
      fs.unlinkSync(archivePath);

      return executablePath;
    } catch (error) {
      this.log(`准备二进制文件失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 检查端口是否可用
   * @param port 端口号
   */
  private async isPortAvailable(port: number): Promise<boolean> {
    return new Promise<boolean>((resolve) => {
      const server = require('net').createServer();

      server.once('error', () => {
        resolve(false);
      });

      server.once('listening', () => {
        server.close();
        resolve(true);
      });

      server.listen(port);
    });
  }

  /**
   * 启动Qdrant服务
   * @param port 端口号
   */
  public async start(port: number = DEFAULT_PORT): Promise<void> {
    if (this.status === QdrantServerStatus.RUNNING) {
      this.log('Qdrant服务已经在运行');
      return;
    }

    if (this.status === QdrantServerStatus.STARTING) {
      this.log('Qdrant服务正在启动中');
      return this.startupPromise || Promise.resolve();
    }

    this.port = port;
    this.status = QdrantServerStatus.STARTING;

    // 创建启动Promise
    this.startupPromise = new Promise<void>((resolve, reject) => {
      this.startupResolver = resolve;
      this.startupRejecter = reject;
    });

    try {
      const isPortFree = await this.isPortAvailable(port);
      if (!isPortFree) {
        // 端口被占用，检查是否是Qdrant服务
        try {
          const response = await fetch(`http://127.0.0.1:${port}/healthz`);
          if (response.ok) {
            // 如果健康检查成功，说明Qdrant服务已经在运行
            this.log('Qdrant服务已经在其他进程中运行');
            this.status = QdrantServerStatus.RUNNING;

            if (this.startupTimeout) {
              clearTimeout(this.startupTimeout);
              this.startupTimeout = null;
            }

            if (this.startupResolver) {
              this.startupResolver();
            }

            return;
          }
        } catch (error) {
          // 健康检查失败，可能是其他服务占用了端口
          this.log(`端口 ${port} 被占用，但不是Qdrant服务`);
          throw new Error(`端口 ${port} 已被占用，无法启动Qdrant服务`);
        }
      }

      // 准备二进制文件
      const executablePath = await this.prepareBinary();

      // 启动服务
      await this.startServer(executablePath);

      return this.startupPromise;
    } catch (error) {
      this.status = QdrantServerStatus.ERROR;
      this.log(`启动Qdrant服务失败: ${error.message}`);

      if (this.startupRejecter) {
        this.startupRejecter(error instanceof Error ? error : new Error(String(error)));
      }

      throw error;
    }
  }

  /**
   * 启动Qdrant服务进程
   * @param executablePath 可执行文件路径
   */
  private async startServer(executablePath: string): Promise<void> {
    this.log(`启动Qdrant服务: ${executablePath}`);

    // 设置启动超时
    this.startupTimeout = setTimeout(() => {
      if (this.status !== QdrantServerStatus.RUNNING) {
        this.log('Qdrant服务启动超时');
        this.status = QdrantServerStatus.ERROR;

        if (this.serverProcess) {
          this.serverProcess.kill();
          this.serverProcess = null;
        }

        if (this.startupRejecter) {
          this.startupRejecter(new Error('Qdrant服务启动超时'));
        }
      }
    }, 30000); // 30秒超时

    try {
      // 构建启动命令参数
      // 根据错误信息，qdrant 主要使用 --config-path 参数
      // --storage-snapshot 是用于从快照恢复的，不是常规启动参数
      const configPath = path.join(this.dataPath, 'config.yaml');

      // 确保配置文件存在
      await this.ensureConfigFile(configPath);

      // 只使用 --config-path 参数，存储路径在配置文件中指定
      // 确保路径格式正确，特别是在 Windows 系统上
      const normalizedConfigPath = path.resolve(configPath);
      const args = ['--config-path', normalizedConfigPath];

      this.log(`启动参数: ${args.join(' ')}`);
      this.log(`配置文件路径: ${normalizedConfigPath}`);

      // 启动进程
      this.serverProcess = childProcess.spawn(executablePath, args, {
        stdio: ['ignore', 'pipe', 'pipe'],
        detached: false,
      });

      // 处理标准输出
      if (this.serverProcess.stdout) {
        this.serverProcess.stdout.on('data', (data) => {
          const output = data.toString();
          this.log(`[Qdrant] ${output.trim()}`);

          // 检查服务是否已启动
          if (output.includes('Actix runtime found') || output.includes('Starting services')) {
            this.handleServerStarted();
          }
        });
      }

      // 处理标准错误
      if (this.serverProcess.stderr) {
        this.serverProcess.stderr.on('data', (data) => {
          const output = data.toString();
          this.log(`[Qdrant Error] ${output.trim()}`);

          // 检查是否是参数错误
          if (output.includes('unexpected argument') || output.includes('--storage-path')) {
            this.log('检测到参数错误，可能是配置文件格式问题');
            if (this.startupRejecter) {
              this.startupRejecter(new Error(`Qdrant启动参数错误: ${output.trim()}`));
            }
          }
        });
      }

      // 处理进程退出
      this.serverProcess.on('exit', (code, signal) => {
        this.log(`Qdrant服务已退出，退出码: ${code}, 信号: ${signal}`);

        // 保存当前状态以便检查
        const previousStatus = this.status;

        this.serverProcess = null;
        this.status = QdrantServerStatus.STOPPED;

        if (this.startupTimeout) {
          clearTimeout(this.startupTimeout);
          this.startupTimeout = null;
        }

        if (previousStatus === QdrantServerStatus.STARTING && this.startupRejecter) {
          this.startupRejecter(new Error(`Qdrant服务异常退出，退出码: ${code}`));
        }

        this.emit('exit', code);
      });

      // 处理进程错误
      this.serverProcess.on('error', (error) => {
        this.log(`Qdrant服务进程错误: ${error.message}`);

        // 保存当前状态以便检查
        const previousStatus = this.status;

        if (this.startupTimeout) {
          clearTimeout(this.startupTimeout);
          this.startupTimeout = null;
        }

        if (previousStatus === QdrantServerStatus.STARTING && this.startupRejecter) {
          this.startupRejecter(error);
        }

        this.status = QdrantServerStatus.ERROR;
        this.emit('error', error);
      });
    } catch (error) {
      this.log(`启动Qdrant服务进程失败: ${error.message}`);

      if (this.startupTimeout) {
        clearTimeout(this.startupTimeout);
        this.startupTimeout = null;
      }

      this.status = QdrantServerStatus.ERROR;

      if (this.startupRejecter) {
        this.startupRejecter(error instanceof Error ? error : new Error(String(error)));
      }

      throw error;
    }
  }

  /**
   * 处理服务启动成功
   */
  private handleServerStarted(): void {
    if (this.status !== QdrantServerStatus.RUNNING) {
      this.log('Qdrant服务已成功启动');

      this.status = QdrantServerStatus.RUNNING;

      if (this.startupTimeout) {
        clearTimeout(this.startupTimeout);
        this.startupTimeout = null;
      }

      if (this.startupResolver) {
        this.startupResolver();
      }

      this.emit('started');
    }
  }

  /**
   * 停止Qdrant服务
   */
  public async stop(): Promise<void> {
    if (this.status === QdrantServerStatus.STOPPED) {
      this.log('Qdrant服务已经停止');
      return;
    }

    this.log('正在停止Qdrant服务');

    if (this.startupTimeout) {
      clearTimeout(this.startupTimeout);
      this.startupTimeout = null;
    }

    return new Promise<void>((resolve) => {
      const cleanup = () => {
        this.serverProcess = null;
        this.status = QdrantServerStatus.STOPPED;
        this.log('Qdrant服务已停止');
        // 在cleanup函数中添加
        const forceReleasePort = () => {
          if (os.platform() === 'win32') {
            childProcess.exec(
              `for /f "tokens=5" %a in ('netstat -aon ^| find ":${this.port}" ^| find "LISTENING"') do taskkill /f /pid %a`,
              () => {}
            );
          } else {
            childProcess.exec(`lsof -i:${this.port} -t | xargs kill -9`, () => {});
          }
        };
        // 尝试释放端口
        try {
          forceReleasePort();
        } catch (error) {
          console.error('%c [ error ]-596', 'font-size:13px; background:pink; color:#bf2c9f;', error);
        }
        resolve();
      };

      if (!this.serverProcess) {
        cleanup();
        return;
      }

      // 监听进程退出事件
      this.serverProcess.once('exit', () => {
        cleanup();
      });

      // 尝试优雅地终止进程
      if (os.platform() === 'win32') {
        // Windows上使用taskkill强制终止进程
        childProcess.exec(`taskkill /pid ${this.serverProcess.pid} /f /t`, () => {
          // 忽略错误，进程退出事件会处理清理
        });
      } else {
        // 在类Unix系统上发送SIGTERM信号
        this.serverProcess.kill('SIGTERM');
        // 如果进程在5秒内没有退出，发送SIGKILL信号
        setTimeout(() => {
          if (this.serverProcess) {
            this.serverProcess.kill('SIGKILL');
          }
        }, 5000);
      }
    });
  }

  /**
   * 获取服务状态
   */
  public getStatus(): QdrantServerStatus {
    return this.status;
  }

  /**
   * 获取服务URL
   */
  public getUrl(): string {
    return `http://127.0.0.1:${this.port}`;
  }

  /**
   * 记录日志
   * @param message 日志消息
   */
  private log(message: string): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    Logger.info('qdrant==========================>', logMessage); // 使用共享的Logger记录日志
  }

  /**
   * 检查服务是否可用
   */
  public async isServerAvailable(): Promise<boolean> {
    if (this.status !== QdrantServerStatus.RUNNING) {
      return false;
    }

    try {
      const url = `${this.getUrl()}/healthz`;

      return new Promise<boolean>((resolve) => {
        const req = https.get(url, { timeout: 2000 }, (res) => {
          resolve(res.statusCode === 200);
        });

        req.on('error', () => {
          resolve(false);
        });

        req.on('timeout', () => {
          req.destroy();
          resolve(false);
        });
      });
    } catch (error) {
      return false;
    }
  }
}

/**
 * 获取Qdrant服务实例
 */
export function getQdrantServer(): QdrantServer {
  return QdrantServer.getInstance();
}

/**
 * 启动Qdrant服务
 * @param port 端口号
 */
export async function startQdrantServer(port: number = DEFAULT_PORT): Promise<string> {
  const server = getQdrantServer();

  try {
    // 如果服务已经在运行，直接返回URL
    if (server.getStatus() === QdrantServerStatus.RUNNING) {
      return server.getUrl();
    }

    // 检查服务是否可用
    try {
      const response = await fetch(`http://127.0.0.1:${port}/healthz`);
      if (response.ok) {
        // 如果健康检查成功，说明服务已经在运行
        server['status'] = QdrantServerStatus.RUNNING;
        return server.getUrl();
      }
    } catch (error) {
      // 健康检查失败，继续启动服务
    }

    await server.start(port);
    return server.getUrl();
  } catch (error) {
    console.error('启动Qdrant服务失败:', error);
    throw error;
  }
}

/**
 * 停止Qdrant服务
 */
export async function stopQdrantServer(): Promise<void> {
  const server = getQdrantServer();
  await server.stop();
}
