import { QdrantServer, QdrantServerStatus } from '../qdrant';
import * as net from 'net';

describe('QdrantServer', () => {
  let server: QdrantServer;
  const TEST_PORT = 6334; // 使用不同于默认端口的测试端口

  beforeEach(() => {
    server = QdrantServer.getInstance();
  });

  afterEach(async () => {
    await server.stop();
  });

  describe('start()', () => {
    it('应该成功启动服务', async () => {
      await server.start(TEST_PORT);
      expect(server.getStatus()).toBe(QdrantServerStatus.RUNNING);
    });

    it('当服务已经运行时，再次调用start不应该抛出错误', async () => {
      await server.start(TEST_PORT);
      await expect(server.start(TEST_PORT)).resolves.not.toThrow();
    });

    it('当端口被占用时，应该抛出错误', async () => {
      // 创建一个占用端口的服务
      const blockingServer = net.createServer();
      await new Promise<void>((resolve) => {
        blockingServer.listen(TEST_PORT, () => resolve());
      });

      // 尝试在被占用的端口上启动服务
      await expect(server.start(TEST_PORT)).rejects.toThrow('端口 6334 已被占用');

      // 清理
      await new Promise<void>((resolve) => {
        blockingServer.close(() => resolve());
      });
    });

    it('当多个进程尝试启动服务时，应该正确处理竞争条件', async () => {
      // 模拟第一个进程启动服务
      const startPromise1 = server.start(TEST_PORT);

      // 模拟第二个进程尝试启动服务
      const startPromise2 = server.start(TEST_PORT);

      // 两个Promise应该都能正常完成，第二个调用应该优雅地处理
      await expect(Promise.all([startPromise1, startPromise2])).resolves.not.toThrow();

      // 服务状态应该是运行中
      expect(server.getStatus()).toBe(QdrantServerStatus.RUNNING);
    });
  });

  describe('isServerAvailable()', () => {
    it('当服务未启动时应该返回false', async () => {
      expect(await server.isServerAvailable()).toBe(false);
    });

    it('当服务启动后应该返回true', async () => {
      await server.start(TEST_PORT);
      expect(await server.isServerAvailable()).toBe(true);
    });
  });
});
