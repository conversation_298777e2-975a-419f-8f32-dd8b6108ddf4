/**
 * Interface for vector database clients
 */
export type PointStruct = {
  id: string;
  vector: number[];
  payload: Record<string, any>;
};

export interface IVectorStore {
  /**
   * Initializes the vector store
   * @returns Promise resolving to boolean indicating if a new collection was created
   */
  initialize(): Promise<boolean>;

  /**
   * 添加单个向量点到存储
   * @param vector 向量数据
   * @param payload 附加数据
   * @returns 新创建点的ID
   */
  addPoint(vector: number[], payload: any): Promise<string>;

  /**
   * 删除指定ID的向量点
   * @param id 要删除的点ID
   */
  deletePoint(id: string): Promise<void>;

  /**
   * 更新指定ID的向量点
   * @param id 要更新的点ID
   * @param vector 新的向量数据
   * @param payload 新的附加数据
   */
  updatePoint(id: string, vector: number[], payload: any): Promise<void>;

  /**
   * Upserts points into the vector store
   * @param points Array of points to upsert
   */
  upsertPoints(points: PointStruct[]): Promise<void>;

  /**
   * Searches for similar vectors
   * @param queryVector Vector to search for
   * @param directoryPrefix Optional directory prefix to filter results
   * @param minScore Minimum similarity score threshold
   * @param limit Maximum number of results to return
   * @returns Promise resolving to search results
   */
  search(
    queryVector: number[],
    directoryPrefix?: string,
    minScore?: number,
    limit?: number
  ): Promise<VectorStoreSearchResult[]>;

  /**
   * Deletes points by file path
   * @param filePath Path of the file to delete points for
   */
  deletePointsByFilePath(filePath: string): Promise<void>;

  /**
   * Deletes points by multiple file paths
   * @param filePaths Array of file paths to delete points for
   */
  deletePointsByMultipleFilePaths(filePaths: string[]): Promise<void>;

  /**
   * Clears all points from the collection
   */
  clearCollection(): Promise<void>;

  /**
   * Deletes the entire collection.
   */
  deleteCollection(): Promise<void>;

  /**
   * Checks if the collection exists
   * @returns Promise resolving to boolean indicating if the collection exists
   */
  collectionExists(): Promise<boolean>;
}

export interface VectorStoreSearchResult {
  id: string | number;
  score: number;
  payload?: Payload | null;
}

export interface Payload {
  filePath: string;
  codeChunk: string;
  startLine: number;
  endLine: number;
  [key: string]: any;
}
