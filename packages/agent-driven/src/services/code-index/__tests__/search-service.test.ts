import { CodeIndexSearchService } from '../search-service';
import { IEmbedder } from '../interfaces/embedder';
import { IVectorStore } from '../interfaces/vector-store';

describe('CodeIndexSearchService', () => {
  let service: CodeIndexSearchService;
  let mockEmbedder: jest.Mocked<IEmbedder>;
  let mockVectorStore: jest.Mocked<IVectorStore>;

  beforeEach(() => {
    mockEmbedder = {
      createEmbeddings: jest.fn(),
    } as any;

    mockVectorStore = {
      upsertPoints: jest.fn(),
    } as any;

    service = new CodeIndexSearchService(mockEmbedder, mockVectorStore);
  });

  describe('updateBatchPoints', () => {
    it('应该成功处理有效的文档数组', async () => {
      const texts = ['文档1', '文档2'];
      const mockEmbeddings = [
        [1, 2, 3],
        [4, 5, 6],
      ];

      mockEmbedder.createEmbeddings.mockResolvedValue({
        embeddings: mockEmbeddings,
      });

      await service.updateBatchPoints(texts);

      // 验证向量生成
      expect(mockEmbedder.createEmbeddings).toHaveBeenCalledWith(texts);

      // 验证批量更新调用
      expect(mockVectorStore.upsertPoints).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            vector: mockEmbeddings[0],
            payload: expect.objectContaining({
              codeChunk: texts[0],
            }),
          }),
          expect.objectContaining({
            vector: mockEmbeddings[1],
            payload: expect.objectContaining({
              codeChunk: texts[1],
            }),
          }),
        ])
      );
    });

    it('应该在输入为空数组时抛出错误', async () => {
      await expect(service.updateBatchPoints([])).rejects.toThrow('文档内容数组不能为空');
    });

    it('应该在包含无效文本时抛出错误', async () => {
      const texts = ['有效文本', '', '  ', null as any];
      await expect(service.updateBatchPoints(texts)).rejects.toThrow('文档内容数组中包含无效的文本');
    });

    it('应该在向量生成失败时抛出错误', async () => {
      mockEmbedder.createEmbeddings.mockResolvedValue({
        embeddings: [],
      });

      await expect(service.updateBatchPoints(['文档1'])).rejects.toThrow('向量生成失败或向量数量不匹配');
    });

    it('应该在存储更新失败时正确处理错误', async () => {
      const texts = ['文档1'];
      mockEmbedder.createEmbeddings.mockResolvedValue({
        embeddings: [[1, 2, 3]],
      });
      mockVectorStore.upsertPoints.mockRejectedValue(new Error('存储错误'));

      await expect(service.updateBatchPoints(texts)).rejects.toThrow('存储错误');
    });
  });
});
