import { CodeIndexManager } from '../manager';
import { CodeIndexSearchService } from '../search-service';
import axios, { AxiosStatic } from 'axios';
import * as vscode from 'vscode';

jest.mock('../search-service');
jest.mock('vscode');
jest.mock('axios');

describe('CodeIndexManager', () => {
  let manager: CodeIndexManager;
  let mockSearchService: jest.Mocked<CodeIndexSearchService>;
  const mockedAxios = jest.mocked(axios);

  beforeEach(() => {
    // 重置所有的 mock
    jest.clearAllMocks();

    // 创建 vscode.ExtensionContext mock
    const mockContext = {
      subscriptions: [],
      extensionPath: '/test/path',
    } as unknown as vscode.ExtensionContext;

    // 获取 manager 实例
    manager = CodeIndexManager.getInstance(mockContext)!;

    // 获取 mock 的 SearchService 实例
    mockSearchService = (manager as any)._searchService;
  });

  afterEach(() => {
    CodeIndexManager.disposeAll();
  });

  describe('updateBatchPoints', () => {
    const testUrl = 'https://api.example.com/content';
    const testHtmlContent = '<div>Test content</div><p>More content</p>';
    const expectedCleanContent = 'Test content More content';

    it('应该正确处理有效的 HTML 响应', async () => {
      // 设置 axios mock 响应
      mockedAxios.mockResolvedValue({
        data: testHtmlContent,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      // 执行方法
      await manager.updateBatchPoints(testUrl);

      // 验证 axios 调用
      expect(mockedAxios).toHaveBeenCalledWith({
        method: 'get',
        url: testUrl,
        timeout: 5 * 60 * 1000,
        validateStatus: expect.any(Function),
      });

      // 验证 SearchService 的调用
      expect(mockSearchService.updateBatchPoints).toHaveBeenCalledWith([expectedCleanContent]);
    });

    it('应该处理空响应数据', async () => {
      mockedAxios.mockResolvedValue({
        data: '',
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      await manager.updateBatchPoints(testUrl);

      expect(mockSearchService.updateBatchPoints).toHaveBeenCalledWith(['']);
    });

    it('应该处理 null 响应数据', async () => {
      mockedAxios.mockResolvedValue({
        data: null,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      await manager.updateBatchPoints(testUrl);

      expect(mockSearchService.updateBatchPoints).toHaveBeenCalledWith(['']);
    });

    it('应该处理网络错误', async () => {
      const networkError = new Error('Network Error');
      mockedAxios.mockRejectedValue(networkError);

      await expect(manager.updateBatchPoints(testUrl)).rejects.toThrow('请求失败');
    });

    it('应该处理超时错误', async () => {
      const timeoutError = new Error('timeout of 300000ms exceeded');
      (timeoutError as any).code = 'ECONNABORTED';
      mockedAxios.mockRejectedValue(timeoutError);

      await expect(manager.updateBatchPoints(testUrl)).rejects.toThrow('请求超时');
    });

    it('应该处理服务器错误', async () => {
      const serverError = {
        response: {
          status: 500,
          statusText: 'Internal Server Error',
        },
      };
      mockedAxios.mockRejectedValue(serverError);

      await expect(manager.updateBatchPoints(testUrl)).rejects.toThrow('请求失败');
    });

    it('应该正确清理复杂的 HTML 内容', async () => {
      const complexHtml = `
        <div class="content">
          <h1>Title</h1>
          <p>Paragraph 1</p>
          <script>console.log('test');</script>
          <style>.test { color: red; }</style>
          <p>Paragraph 2</p>
        </div>
      `;
      const expectedClean = 'Title Paragraph 1 Paragraph 2';

      mockedAxios.mockResolvedValue({
        data: complexHtml,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      await manager.updateBatchPoints(testUrl);

      expect(mockSearchService.updateBatchPoints).toHaveBeenCalledWith([expectedClean.trim()]);
    });

    it('应该在服务未初始化时抛出错误', async () => {
      // 模拟服务未初始化的情况
      (manager as any)._searchService = undefined;

      await expect(manager.updateBatchPoints(testUrl)).rejects.toThrow('服务未初始化');
    });
  });
});
