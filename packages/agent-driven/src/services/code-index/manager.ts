import * as vscode from 'vscode';
import * as os from 'os';
import * as fs from 'fs';
import { getWorkspacePath } from '../../utils/path';
import { VectorStoreSearchResult } from './interfaces';
// import { IndexingState } from './interfaces/manager';
import { CodeIndexServiceFactory } from './service-factory';
import { CodeIndexSearchService } from './search-service';
import { CacheManager } from './cache-manager';
import ignore from 'ignore';
import axios from 'axios';
import to from 'await-to-js';
import { FileSystemHelper } from '../../utils/FileSystemHelper';

export class CodeIndexManager {
  // --- Singleton Implementation ---
  private static instances = new Map<string, CodeIndexManager>(); // Map workspace path to instance

  // Specialized class instances
  private _serviceFactory: CodeIndexServiceFactory | undefined;
  private _searchService: CodeIndexSearchService | undefined;
  private _cacheManager: CacheManager | undefined;

  public static getInstance(context: vscode.ExtensionContext): CodeIndexManager | undefined {
    const workspacePath = FileSystemHelper.join(os.homedir(), '.joycode', 'qdrant', 'indexDB');
    try {
      if (!workspacePath) {
        if (!fs.existsSync(workspacePath)) {
          fs.mkdirSync(workspacePath, { recursive: true });
        }
      }
    } catch (error) {
      return undefined;
    }

    if (!CodeIndexManager.instances.has(workspacePath)) {
      CodeIndexManager.instances.set(workspacePath, new CodeIndexManager(workspacePath, context));
    }
    return CodeIndexManager.instances.get(workspacePath)!;
  }

  public static disposeAll(): void {
    for (const instance of CodeIndexManager.instances.values()) {
      instance.dispose();
    }
    CodeIndexManager.instances.clear();
  }

  private readonly workspacePath: string;
  private readonly context: vscode.ExtensionContext;

  // Private constructor for singleton pattern
  private constructor(workspacePath: string, context: vscode.ExtensionContext) {
    this.workspacePath = workspacePath;
    this.context = context;
  }

  /**
   * 添加新文档到知识库
   * @param text 文档内容
   * @throws Error 当文档内容为空或添加失败时
   */
  public async addDocument(text: string): Promise<void> {
    if (!this._searchService) {
      throw new Error('服务未初始化');
    }
    if (!text.trim()) {
      throw new Error('文档内容不能为空');
    }

    try {
      await this._searchService.addDocument(text);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`添加文档失败：${errorMessage}`);
    }
  }

  /**
   * 从知识库中删除指定文档
   * @param id 文档ID
   * @throws Error 当文档ID为空或删除失败时
   */
  public async deleteDocument(id: string): Promise<void> {
    if (!this._searchService) {
      throw new Error('服务未初始化');
    }
    if (!id.trim()) {
      throw new Error('文档ID不能为空');
    }

    try {
      await this._searchService.deleteDocument(id);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`删除文档失败：${errorMessage}`);
    }
  }

  /**
   * 更新知识库中的文档
   * @param id 文档ID
   * @param newText 新的文档内容
   * @throws Error 当参数无效或更新失败时
   */
  public async updateDocument(id: string, newText: string): Promise<void> {
    if (!this._searchService) {
      throw new Error('服务未初始化');
    }
    if (!id.trim()) {
      throw new Error('文档ID不能为空');
    }
    if (!newText.trim()) {
      throw new Error('文档内容不能为空');
    }

    try {
      await this._searchService.updateDocument(id, newText);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`更新文档失败：${errorMessage}`);
    }
  }

  /**
   * 从知识库中查询文档（基于文本相似度）
   * @param query 查询文本
   * @param limit 返回结果数量限制
   * @returns 相似文档列表
   * @throws Error 当查询文本为空或查询失败时
   */
  public async queryDocuments(query: string, limit: number = 5): Promise<VectorStoreSearchResult[]> {
    if (!this._searchService) {
      throw new Error('服务未初始化');
    }
    if (!query.trim()) {
      throw new Error('查询文本不能为空');
    }

    try {
      return await this._searchService.queryDocuments(query, limit);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`查询文档失败：${errorMessage}`);
    }
  }

  /**
   * Initializes the manager with configuration and dependent services.
   * Must be called before using any other methods.
   * @returns Object indicating if a restart is needed
   */
  public async initialize(context: vscode.ExtensionContext) {
    // 3. CacheManager Initialization
    if (!this._cacheManager) {
      this._cacheManager = new CacheManager(context, this.workspacePath);
      await this._cacheManager.initialize();
    }

    // 4. Determine if Core Services Need Recreation
    const needsServiceRecreation = !this._serviceFactory;

    if (needsServiceRecreation) {
      // (Re)Initialize service factory
      this._serviceFactory = new CodeIndexServiceFactory(this.workspacePath);

      const ignoreInstance = ignore();
      const ignorePath = FileSystemHelper.join(getWorkspacePath(), '.gitignore');
      try {
        const content = await FileSystemHelper.readFile(ignorePath, 'utf8');
        ignoreInstance.add(content);
        ignoreInstance.add('.gitignore');
      } catch (error) {
        // Should never happen: reading file failed even though it exists
        console.error('Unexpected error loading .gitignore:', error);
      }

      // (Re)Create shared service instances
      const { embedder, vectorStore } = this._serviceFactory.createServices();

      // (Re)Initialize search service
      this._searchService = new CodeIndexSearchService(embedder, vectorStore);
    }
  }

  /**
   * Initiates the indexing process (initial scan and starts watcher).
   */
  public async startIndexing(): Promise<void> {
    // this.assertInitialized();
    // await this._orchestrator!.startIndexing();
  }

  /**
   * Cleans up the manager instance.
   */
  public dispose(): void {
    // this._stateManager.dispose();
  }

  /**
   * Clears all index data by stopping the watcher, clearing the Qdrant collection,
   * and deleting the cache file.
   */
  public async clearIndexData(): Promise<void> {
    // this.assertInitialized();
    // await this._orchestrator!.clearIndexData();
    await this._cacheManager!.clearCacheFile();
  }

  // --- Private Helpers ---

  public getCurrentStatus() {
    // return this._stateManager.getCurrentStatus();
  }

  public async searchIndex(query: string, directoryPrefix?: string): Promise<VectorStoreSearchResult[]> {
    if (!this._searchService) {
      throw new Error('服务未初始化');
    }
    return this._searchService.searchIndex(query, directoryPrefix);
  }

  /**
   * 从指定 URL 获取内容并更新到向量存储
   * @param url 要获取内容的 URL
   * @throws Error 当服务未初始化、请求失败或内容处理出错时
   */
  public async updateBatchPoints(url: string): Promise<void> {
    if (!this._searchService) {
      throw new Error('服务未初始化');
    }

    try {
      // 发送请求并等待响应
      const response = await axios({
        method: 'get',
        url: url,
        timeout: 5 * 60 * 1000, // 5分钟超时
        validateStatus: (status: number) => status === 200, // 只接受200状态码
      });

      // 验证响应数据
      if (!response?.data) {
        console.warn('[CodeIndexManager] 警告: 响应数据为空');
      }

      // 清理 HTML 内容
      const cleanedText = this.cleanHtmlContent(response?.data);

      // 记录处理后的文本长度（用于调试）
      console.log('[CodeIndexManager] 清理后的文本长度:', cleanedText.length);

      // 批量更新向量存储
      await this._searchService.updateBatchPoints([cleanedText]);
    } catch (error) {
      // 增强错误处理
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          throw new Error('请求超时');
        }
        const statusCode = error.response?.status;
        const statusText = error.response?.statusText;
        throw new Error(`请求失败: ${statusCode} ${statusText || error.message}`);
      }
      throw error;
    }
  }

  /**
   * 清理 HTML 内容，移除所有 HTML 标签和多余空白
   * @param html HTML 内容
   * @returns 清理后的纯文本
   */
  private cleanHtmlContent(html: string | null | undefined): string {
    if (!html || typeof html !== 'string') {
      return '';
    }

    return (
      html
        // 移除 script 和 style 标签及其内容
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
        // 移除所有 HTML 标签
        .replace(/<[^>]+>/g, ' ')
        // 替换连续空白字符为单个空格
        .replace(/\s+/g, ' ')
        // 移除首尾空白
        .trim()
    );
  }

  /**
   * Handles external settings changes by reloading configuration.
   * This method should be called when API provider settings are updated
   * to ensure the CodeIndexConfigManager picks up the new configuration.
   * If the configuration changes require a restart, the service will be restarted.
   */
  public async handleExternalSettingsChange(): Promise<void> {
    // If configuration changes require a restart and the manager is initialized, restart the service
    // if (this.isInitialized) {
    //   await this.startIndexing();
    // }
  }
}
