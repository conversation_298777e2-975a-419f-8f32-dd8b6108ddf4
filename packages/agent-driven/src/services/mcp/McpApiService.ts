import axios from 'axios';
import { getJdhLoginInfo, isIDE, getBaseUrl } from '@joycoder/shared';

function getIdeUrl() {
  return getBaseUrl();
}
const pluginUrl = 'http://joycoder-api-inner.jd.com';

export interface McpApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export class McpApiService {
  private static async callApi<T>(
    endpoint: string,
    method: 'get' | 'post' | 'put' | 'delete',
    data?: any
  ): Promise<McpApiResponse<T>> {
    try {
      const config: any = {
        method,
        url: `${isIDE() ? getIdeUrl() : pluginUrl}${endpoint}`,
        headers: {
          'source-type': isIDE() ? 'joycoder-ide' : 'joycoder-plugin',
          'Content-Type': 'application/json',
          ptKey: getJdhLoginInfo()?.ptKey,
        },
        timeout: 5 * 60 * 1000, // 5 minutes timeout
      };

      if (method === 'get') {
        config.params = data;
      } else {
        config.data = data;
      }
      const response = await axios(config);

      return {
        success: true,
        data: response.data,
      };
    } catch (error: unknown) {
      console.error('MCP API call failed:', error);
      return {
        success: false,
        data: null as T,
        message: error instanceof Error ? error.message : 'API call failed',
      };
    }
  }

  static async get<T>(endpoint: string, params?: any): Promise<McpApiResponse<T>> {
    return this.callApi<T>(endpoint, 'get', { ...params });
  }

  static async post<T>(endpoint: string, data?: any): Promise<McpApiResponse<T>> {
    return this.callApi<T>(endpoint, 'post', data);
  }

  static async put<T>(endpoint: string, data?: any): Promise<McpApiResponse<T>> {
    return this.callApi<T>(endpoint, 'put', data);
  }

  static async delete<T>(endpoint: string, data?: any): Promise<McpApiResponse<T>> {
    return this.callApi<T>(endpoint, 'delete', data);
  }

  // 示例方法：获取 MCP 列表
  static async getMcpList(
    keyword: string = '',
    pageNum: Number = 1,
    pageSize: Number = 10,
    expressSetup?: boolean
  ): Promise<McpApiResponse<any[]>> {
    return this.get<any[]>('/api/saas/mcp-service/page', { keyword, pageNum, pageSize, expressSetup });
  }

  // 示例方法：安装 MCP
  static async getInstallMcpParam(serviceId: string, version: number): Promise<McpApiResponse<boolean>> {
    return this.get<boolean>('/api/saas/mcp-service/install', { serviceId, version });
  }

  // 可以根据需要添加更多的 API 方法
  // 根据mcp名称获取mcp详情
  static async getMcpDetailByName(serviceName: string): Promise<McpApiResponse<any>> {
    return this.get<any>('/api/saas/mcp-service/getByName', { serviceName, version: 1 });
  }
}
