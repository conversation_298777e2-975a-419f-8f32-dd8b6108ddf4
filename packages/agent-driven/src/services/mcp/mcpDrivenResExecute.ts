import * as vscode from 'vscode';
import { McpSendMsgType, McpCommonSendMsgType } from '@joycoder/shared/src/mcp/McpTypes';
import { GroupEntry } from '../../shared/modes';
import { ToolGroup } from '../../../web-agent/src/utils/modes';
let settingWebview: vscode.WebviewPanel | undefined = undefined;
export async function notifyWebviewOfServerChanges(data?: any) {
  const type = data.type;
  switch (type) {
    case 'mcpServers':
      const message = {
        type: McpSendMsgType.GET_MCP_CONNECTION_SERVER,
      };
      await vscode.commands.executeCommand('JoyCode.config.setting.mcp', message);
      break;
    default:
      console.log('消息未匹配');
      break;
  }
}

// Helper to extract group name regardless of format
export function getGroupName(group: GroupEntry): ToolGroup {
  if (typeof group === 'string') {
    return group;
  }

  return group[0];
}
