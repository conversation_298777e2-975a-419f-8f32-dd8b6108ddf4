import { getChatModelAndConfig } from '@joycoder/plugin-base-ai/src/model';
import { WorkspaceState } from '@joycoder/shared';
import { ApiMessage } from '../../../core/task-persistence/apiMessages';

import { <PERSON>pi<PERSON>and<PERSON> } from '../index';

/* Removes image blocks from messages if they are not supported by the Api Handler */
export function maybeRemoveImageBlocks(messages: ApiMessage[], apiHandler: ApiHandler): ApiMessage[] {
  return messages.map((message) => {
    // Handle array content (could contain image blocks).
    let { content } = message;
    if (Array.isArray(content)) {
      const label = WorkspaceState.get('openAiModelId');
      const modelConfig = getChatModelAndConfig(label);
      if (!modelConfig.features?.includes('vision')) {
        // Convert image blocks to text descriptions.
        content = content.map((block) => {
          if (block.type === 'image') {
            // Convert image blocks to text descriptions.
            // Note: We can't access the actual image content/url due to API limitations,
            // but we can indicate that an image was present in the conversation.
            return {
              type: 'text',
              text: '[Referenced image in conversation]',
            };
          }
          return block;
        });
      }
    }
    return { ...message, content };
  });
}
