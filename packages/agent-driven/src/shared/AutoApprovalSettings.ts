export interface AutoApprovalSettings {
  // Whether auto-approval is enabled
  enabled: boolean;
  // Individual action permissions
  actions: {
    readFiles: boolean; // Read files and directories
    editFiles: boolean; // Edit files
    executeCommands: boolean; // Execute safe commands
    useBrowser: boolean; // Use browser
    useMcp: boolean; // Use MCP servers
    todoList: boolean; // Todo list operations
  };
  // Global settings
  maxRequests: number; // Maximum number of auto-approved requests
  enableNotifications: boolean; // Show notifications for approval and task completion
}

export const DEFAULT_AUTO_APPROVAL_SETTINGS: AutoApprovalSettings = {
  enabled: true,
  actions: {
    readFiles: true,
    editFiles: false,
    executeCommands: false,
    useBrowser: false, // 默认不启用浏览器工具，需要用户手动勾选
    useMcp: true,
    todoList: false, // 默认不启用待办事项自动批准，需要用户手动勾选
  },
  maxRequests: 100,
  enableNotifications: false,
};
export const AUTO_APPROVAL_SETTINGS: AutoApprovalSettings = {
  enabled: true,
  actions: {
    readFiles: true,
    editFiles: true,
    executeCommands: true,
    useBrowser: true,
    useMcp: true,
    todoList: true, // 启用待办事项操作
  },
  maxRequests: 100,
  enableNotifications: false,
};
