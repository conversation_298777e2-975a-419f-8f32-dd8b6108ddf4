import { JoyCoderMessage } from './ExtensionMessage';

export type ParsedApiReqStartedTextType = {
  tokensIn: number;
  tokensOut: number;
  cacheWrites: number;
  cacheReads: number;
  cost?: number; // Only present if combineApiRequests has been called
  apiProtocol?: 'anthropic' | 'openai';
};

interface ApiMetrics {
  totalTokensIn: number;
  totalTokensOut: number;
  totalCacheWrites?: number;
  totalCacheReads?: number;
  totalCost: number;
  contextTokens: number;
}

/**
 * Calculates API metrics from an array of JoyCoderMessages.
 *
 * This function processes 'api_req_started' messages that have been combined with their
 * corresponding 'api_req_finished' messages by the combineApiRequests function. It also takes into account 'deleted_api_reqs' messages, which are aggregated from deleted messages.
 * It extracts and sums up the tokensIn, tokensOut, cacheWrites, cacheReads, and cost from these messages.
 *
 * @param messages - An array of JoyCoderMessage objects to process.
 * @returns An ApiMetrics object containing totalTokensIn, totalTokensOut, totalCacheWrites, totalCacheReads, and totalCost.
 *
 * @example
 * const messages = [
 *   { type: "say", say: "api_req_started", text: '{"request":"GET /api/data","tokensIn":10,"tokensOut":20,"cost":0.005}', ts: 1000 }
 * ];
 * const { totalTokensIn, totalTokensOut, totalCost } = getApiMetrics(messages);
 * // Result: { totalTokensIn: 10, totalTokensOut: 20, totalCost: 0.005 }
 */
export function getApiMetrics(messages: JoyCoderMessage[]): ApiMetrics {
  const result: ApiMetrics = {
    totalTokensIn: 0,
    totalTokensOut: 0,
    totalCacheWrites: undefined,
    totalCacheReads: undefined,
    totalCost: 0,
    contextTokens: 0,
  };

  // Calculate running totals
  messages.forEach((message) => {
    if (message.type === 'say' && message.say === 'api_req_started' && message.text) {
      try {
        const parsedText: ParsedApiReqStartedTextType = JSON.parse(message.text);
        const { tokensIn, tokensOut, cacheWrites, cacheReads, cost } = parsedText;

        if (typeof tokensIn === 'number') {
          result.totalTokensIn += tokensIn;
        }
        if (typeof tokensOut === 'number') {
          result.totalTokensOut += tokensOut;
        }
        if (typeof cacheWrites === 'number') {
          result.totalCacheWrites = (result.totalCacheWrites ?? 0) + cacheWrites;
        }
        if (typeof cacheReads === 'number') {
          result.totalCacheReads = (result.totalCacheReads ?? 0) + cacheReads;
        }
        if (typeof cost === 'number') {
          result.totalCost += cost;
        }
      } catch (error) {
        console.error('Error parsing JSON:', error);
      }
    } else if (message.type === 'say' && message.say === 'condense_context') {
      result.totalCost += message.contextCondense?.cost ?? 0;
    }
  });

  // Calculate context tokens, from the last API request started or condense context message
  result.contextTokens = 0;
  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i];
    if (message.type === 'say' && message.say === 'api_req_started' && message.text) {
      try {
        const parsedText: ParsedApiReqStartedTextType = JSON.parse(message.text);
        const { tokensIn, tokensOut, cacheWrites, cacheReads, apiProtocol } = parsedText;
        result.contextTokens = (tokensIn || 0) + (tokensOut || 0) + (cacheWrites || 0) + (cacheReads || 0);

        // // Calculate context tokens based on API protocol
        // if (apiProtocol === 'anthropic') {
        //   result.contextTokens = (tokensIn || 0) + (tokensOut || 0) + (cacheWrites || 0) + (cacheReads || 0);
        // } else {
        //   // For OpenAI (or when protocol is not specified)
        //   result.contextTokens = (tokensIn || 0) + (tokensOut || 0);
        // }
      } catch (error) {
        console.error('Error parsing JSON:', error);
        continue;
      }
    } else if (message.type === 'say' && message.say === 'condense_context') {
      result.contextTokens = message.contextCondense?.newContextTokens ?? 0;
    }
    if (result.contextTokens) {
      break;
    }
  }

  return result;
}
