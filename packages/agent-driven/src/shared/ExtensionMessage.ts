// type that represents json data that is sent from extension to webview, called ExtensionMessage and has 'type' enum which can be 'plusButtonClicked' or 'settingsButtonClicked' or 'hello'

import { ApiConfiguration, ModelInfo } from './api';
import { AutoApprovalSettings } from './AutoApprovalSettings';
import { BrowserSettings } from './BrowserSettings';
import { HistoryItem } from './HistoryItem';
import { McpServer } from './mcp';
import { GitCommit } from '../utils/git';
import { ContextCondense, ToolProgressStatus } from '../schemas';
import { Mode } from './modes';
import { ApiConfigMeta, CodebaseIndexModels, CustomModePrompts, ModeConfig } from '../../web-agent/src/utils/modes';
import { UserContent } from '../core/Joycoder';
import { ISelectionInfo } from '@joycoder/shared/src/types';
import { CodebaseIndexingStatus } from '@joycoder/shared/src/types/codebase';

export type { ApiConfigMeta, ToolProgressStatus };
interface ChatModelConfig {
  label: string;
  description: string;
  avatar: string;
  chatApiModel: string;
  chatApiUrl?: string;
  maxTotalTokens: number;
  bizId?: string;
  bizToken?: string;
  systemMessage?: string;
  respMaxTokens?: number;
  // 标记类
  hidden?: boolean;
  prefer?: boolean;
  context?: boolean;
  features?: string[];
  temperature?: number;
  stream?: boolean;
  modelFunctionType?: string;
}

// webview will hold state
export interface ExtensionMessage {
  type:
    | 'mcpList'
    | 'action'
    | 'updateGPTConfig'
    | 'totalTasksSize'
    | 'updateGPTModel'
    | 'state'
    | 'selectedImages'
    | 'ollamaModels'
    | 'lmStudioModels'
    | 'theme'
    | 'workspaceUpdated'
    | 'invoke'
    | 'updateLoginStatus'
    | 'updateLoginType'
    | 'partialMessage'
    | 'openRouterModels'
    | 'openAiModels'
    | 'mcpServers'
    | 'relinquishControl'
    | 'vsCodeLmModels'
    | 'requestVsCodeLmModels'
    | 'emailSubscribed'
    | 'updatedIdeName'
    | 'commitSearchResults'
    | 'updatePluginType'
    | 'workspaceUpdating'
    | 'docListUpdated'
    | 'updateSelectionContext'
    | 'updatePlatformInfo'
    | 'listApiConfig'
    | 'enhancedPrompt'
    | 'codebase'
    | 'updateUserPrompt'
    | 'postAgentInfo'
    | 'postTaskInfo'
    | 'updateCoderMode'
    | 'modeUpdate'
    | 'indexCleared'
    | 'indexingStatusUpdate'
    | 'noWorkspaceError'
    | 'autoApprovalSettings'
    | 'ResourceImport'
    | 'thinkingModeState'
    | 'webSearchEnabledState'
    | 'autoExecuteState'
    | 'showApplicationGenerationDialog';
  text?: string;
  ideAppName?: string;
  action?:
    | 'chatButtonClicked'
    | 'mcpButtonClicked'
    | 'settingsButtonClicked'
    | 'historyButtonClicked'
    | 'didBecomeVisible'
    | 'accountLoginClicked'
    | 'updatedIdeName'
    | 'openInNewChat'
    | 'indexingStatusUpdate'
    | 'accountLogoutClicked';
  invoke?: 'sendMessage' | 'primaryButtonClick' | 'secondaryButtonClick';
  state?: ExtensionState;
  images?: string[];
  ollamaModels?: string[];
  lmStudioModels?: string[];
  vsCodeLmModels?: { vendor?: string; family?: string; version?: string; id?: string }[];
  filePaths?: string[];
  ruleFiles?: string[]; // 添加 ruleFiles 属性，用于存储 .joycoder/rules/*.mdc 文件列表
  resourceFiles?: string[]; // 添加 resourceFiles 属性，用于存储 .joycode/ai-resource/*.md 文件列表
  modelConfig?: Record<string, any>;
  modelList?: ChatModelConfig[];
  partialMessage?: JoyCoderMessage;
  openRouterModels?: Record<string, ModelInfo>;
  openAiModels?: string[];
  mcpServers?: McpServer[];
  data?: Record<string, any>;
  commits?: GitCommit[];
  updateStatus?: string;
  totalTasksSize?: number | null;
  addRemoteServerResult?: {
    success: boolean;
    serverName: string;
    error?: string;
  };
  selectionInfo?: ISelectionInfo | null;
  codebaseIndexingStatus?: CodebaseIndexingStatus;
  listApiConfig?: any;
  mode?: string; // 添加mode字段，用于updateCoderMode消息
  promptList?: any;
  agentInfo?: any;
  mcpList?: any[];
  docList?: any[];
  values?: any;
  parentTaskInfo?: any;
  autoApprovalSettings?: any;
  value?: boolean; // 用于思考模式状态
  applicationGenerationParams?: { mode?: string; text?: string }; // 应用生成参数
}

export type Platform = 'aix' | 'darwin' | 'freebsd' | 'linux' | 'openbsd' | 'sunos' | 'win32' | 'unknown';

export const DEFAULT_PLATFORM = 'unknown';

export interface ExtensionState {
  version: string;
  apiConfiguration?: ApiConfiguration;
  customInstructions?: string;
  uriScheme?: string;
  currentTaskItem?: HistoryItem;
  checkpointTrackerErrorMessage?: string;
  joycoderMessages: JoyCoderMessage[];
  taskHistory: HistoryItem[];
  shouldShowAnnouncement: boolean;
  autoApprovalSettings: AutoApprovalSettings;
  browserSettings: BrowserSettings;
  isLoggedIn: boolean;
  platform: Platform;
  isRemoteEnvironment?: boolean;
  userInfo?:
    | any
    | {
        displayName: string | null;
        email: string | null;
        photoURL: string | null;
      };
  updateStatus?: string;
  mode: Mode;
  customModes: ModeConfig[];
  customModePrompts?: CustomModePrompts;
  listApiConfigMeta?: any;
  customSupportPrompts?: any;
  codebaseIndexConfig?: any;
  codebaseIndexModels?: CodebaseIndexModels;
  isIDE: boolean;
  cwd?: string; // Current working directory
}

export interface JoyCoderMessage {
  ts: number;
  type: 'ask' | 'say';
  ask?: JoyCoderAsk | 'command';
  say?: JoyCoderSay;
  text?: string;
  reasoning?: string;
  reasoning_content?: string;
  images?: string[];
  partial?: boolean;
  lastCheckpointHash?: string;
  isCheckpointCheckedOut?: boolean;
  conversationHistoryIndex?: number;
  progressStatus?: ToolProgressStatus;
  conversationHistoryDeletedRange?: [number, number]; // for when conversation history is truncated for API requests
  modeInfo?: {
    agentId: string;
    name: string;
    avatar?: string;
  }; // Store mode information at message creation time
  isUserMessage?: boolean; // Node层设置的用户消息标识
  contextCondense?: ContextCondense;
}
export interface JoyCoderPlanModeResponse {
  response: string;
  options?: string[];
  selected?: string;
}

export interface JoyCoderAskQuestion {
  question: string;
  options?: string[];
  selected?: string;
}
export interface ButtonText {
  primaryButtonText: string | undefined;
  secondaryButtonText: string | undefined;
}

export type JoyCoderAsk =
  | 'followup'
  | 'command'
  | 'command_output'
  | 'completion_result'
  | 'tool'
  | 'api_req_failed'
  | 'resume_task'
  | 'resume_completed_task'
  | 'mistake_limit_reached'
  | 'auto_approval_max_req_reached'
  | 'new_task_with_condense_context'
  | 'use_browser_launch'
  | 'use_web_search'
  | 'use_codebase'
  | 'use_mcp_server'
  | 'condense'
  | 'switchMode'
  | 'new_task_creation';

export type JoyCoderSay =
  | 'task'
  | 'error'
  | 'api_req_started'
  | 'api_req_finished'
  | 'text'
  | 'reasoning'
  | 'reasoning_content'
  | 'completion_result'
  | 'user_feedback'
  | 'user_feedback_diff'
  | 'api_req_retried'
  | 'command'
  | 'command_output'
  | 'tool'
  | 'shell_integration_warning'
  | 'use_browser_launch'
  | 'use_web_search'
  | 'use_codebase'
  | 'use_codebase_not_support'
  | 'use_browser'
  | 'use_browser_result'
  | 'mcp_server_request_started'
  | 'mcp_server_response'
  | 'use_mcp_server'
  | 'diff_error'
  | 'deleted_api_reqs'
  | 'joycoderignore_error'
  | 'get_mcp_instructions'
  | 'new_task_creation'
  | 'switchMode'
  | 'subtask_result'
  | 'codebase_search_result'
  | 'checkpoint_created'
  | 'condense_context'
  | 'condense_context_error'
  | 'user_edit_todos';
export const JoyCoderAsks = [
  'followup',
  'command',
  'command_output',
  'completion_result',
  'tool',
  'api_req_failed',
  'resume_task',
  'resume_completed_task',
  'mistake_limit_reached',
  'auto_approval_max_req_reached',
  'use_browser_launch',
  'use_web_search',
  'use_codebase',
  'use_mcp_server',
  'condense',
  'new_task_creation',
] as const;

export const JoyCoderSays = [
  'task',
  'error',
  'api_req_started',
  'api_req_finished',
  'text',
  'reasoning',
  'reasoning_content',
  'completion_result',
  'user_feedback',
  'user_feedback_diff',
  'api_req_retried',
  'command',
  'command_output',
  'tool',
  'shell_integration_warning',
  'use_browser_launch',
  'use_web_search',
  'use_codebase',
  'use_codebase_not_support',
  'use_browser',
  'use_browser_result',
  'mcp_server_request_started',
  'mcp_server_response',
  'use_mcp_server',
  'diff_error',
  'deleted_api_reqs',
  'joycoderignore_error',
  'get_mcp_instructions',
  'new_task_creation',
  'checkpoint_created',
] as const;

export interface JoyCoderSayTool {
  tool:
    | 'editedExistingFile'
    | 'searchAndReplace'
    | 'newFileCreated'
    | 'readFile'
    | 'listFilesTopLevel'
    | 'listFilesRecursive'
    | 'listCodeDefinitionNames'
    | 'searchFiles'
    | 'useCodeBase'
    | 'appliedDiff'
    | 'insertContent'
    | 'fetchInstructions'
    | 'finishTask'
    | 'clearPublish'
    | 'webSearch';
  path?: string;
  diff?: string;
  content?: string;
  search?: string;
  replace?: string;
  regex?: string;
  filePattern?: string;
  query?: string;
  files?: string[];
  lineNumber?: number;
  startLine?: number;
  endLine?: number;
  useRegex?: boolean;
  ignoreCase?: boolean;
  isOutsideWorkspace?: boolean;
  mode?: string;
  reason?: string;
  conversationId?: string;
  userContent?: UserContent;
  taskId?: string;
  sessionId?: string;
  project_name?: string;
  batchFiles?: Array<{
    path: string;
    lineSnippet: string;
    isOutsideWorkspace?: boolean;
    key: string;
    content?: string;
  }>;
}

export interface JoyCoderSayText {
  text: string;
  userContent?: UserContent;
  conversationId?: string;
  taskId?: string;
  sessionId?: string;
}

// must keep in sync with system prompt
export const browserActions = [
  'launch',
  'click',
  'hover',
  'type',
  'scroll_down',
  'scroll_up',
  'resize',
  'save_screenshot',
  'close',
] as const;
export type BrowserAction = (typeof browserActions)[number];

export interface JoyCoderSayBrowserAction {
  action: BrowserAction;
  coordinate?: string;
  text?: string;
  file_path?: string;
}

export type BrowserActionResult = {
  screenshot?: string;
  logs?: string;
  currentUrl?: string;
  currentMousePosition?: string;
};

export interface JoyCoderAskUseMcpServer {
  serverName: string;
  type: 'use_mcp_tools' | 'get_mcp_resource';
  toolName?: string;
  arguments?: string;
  uri?: string;
}

export interface JoyCoderApiReqInfo {
  request?: string;
  tokensIn?: number;
  tokensOut?: number;
  cacheWrites?: number;
  cacheReads?: number;
  cost?: number;
  cancelReason?: JoyCoderApiReqCancelReason;
  streamingFailedMessage?: string;
  retryStatus?: {
    attempt: number;
    maxAttempts: number;
    delaySec: number;
    errorSnippet?: string;
  };
}

export type JoyCoderApiReqCancelReason = 'streaming_failed' | 'user_cancelled' | 'retries_exhausted';

export const COMPLETION_RESULT_CHANGES_FLAG = 'HAS_CHANGES';
