import { type Language, isLanguage } from '../schemas';

export { type Language, isLanguage };

/**
 * Language name mapping from ISO codes to full language names
 */

export const LANGUAGES: Record<Language, string> = {
  en: 'English',
  'zh-CN': '简体中文',
};

/**
 * Formats a VSCode locale string to ensure the region code is uppercase.
 * For example, transforms "en-us" to "en-US" or "fr-ca" to "fr-CA".
 *
 * @param vscodeLocale - The VSCode locale string to format (e.g., "en-us", "fr-ca")
 * @returns The formatted locale string with uppercase region code
 */

export function formatLanguage(vscodeLocale: string): Language {
  if (!vscodeLocale) {
    return 'zh-CN';
  }

  const formattedLocale = vscodeLocale.replace(/-(\w+)$/, (_, region) => `-${region.toUpperCase()}`);
  return isLanguage(formattedLocale) ? formattedLocale : 'zh-CN';
}
