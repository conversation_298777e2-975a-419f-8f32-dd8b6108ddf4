module.exports = {
  // 必须返回文件匹配规则数组
  patterns: [
    // 匹配源文件 - 排除测试文件和测试目录
    'src/core/prompts/**/!(*.test|*.spec).{ts,js}',
    '!src/core/prompts/**/__tests__/**',
    '!src/core/prompts/**/*.d.ts',
    // 更精确地匹配编译后的文件 - 排除测试相关文件
    'dist/core/prompts/**/!(*.test|*.spec).js',
    'dist/**/prompts/**/!(*.test|*.spec).js',
    '!dist/**/__tests__/**',
    '!dist/**/*.d.js',
    // 特定重要文件的编译版本
    'dist/**/system.js',
    'dist/**/responses.js',
    'dist/**/commands.js',
    'dist/**/loadMcpDocumentation.js',
    'dist/**/getUseBrowserTool.js',
  ],
  // 可选：子项目专属混淆选项（会覆盖顶级默认配置）
  options: {
    // 增强混淆强度，特别针对字符串
    stringArrayThreshold: 1.0,
    stringArrayEncoding: ['base64', 'rc4'],
    stringArrayWrappersCount: 5,
    stringArrayWrappersChainedCalls: true,
    // 增强控制流混淆
    controlFlowFlatteningThreshold: 1.0,
    // 增强死代码注入
    deadCodeInjectionThreshold: 0.6,
  },
};
