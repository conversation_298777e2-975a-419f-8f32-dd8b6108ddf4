{"name": "@joycoder/agent-driven", "description": "", "publisher": "JoyCode", "version": "3.3.0", "galleryBanner": {"color": "#617A91", "theme": "dark"}, "license": "MIT", "main": "initAgentDriven.ts", "module": "initAgentDriven.ts", "files": ["src"], "scripts": {"vscode:prepublish": "npm run package", "compile": "npm run check-types && npm run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:web": "cd web-agent && npm run watch", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "npm run build:webview && npm run check-types && npm run lint && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "check-types": "tsc --noEmit", "lint": "eslint  src --ext ts ", "format": "prettier . --check", "format:fix": "prettier . --write", "test": "vscode-test", "test:tools": "jest src/core/prompts/__tests__/tools.test.ts --no-cache", "install:all": "npm install && cd web-agent && npm install", "start:webview": "cd web-agent && npm run start", "build:webview": "cd web-agent && npm run build", "test:webview": "cd web-agent && npm run test"}, "devDependencies": {"@changesets/cli": "^2.27.12", "@types/chai": "^5.0.1", "@types/diff": "^5.2.1", "@types/jest": "^27.5.2", "@types/mocha": "^10.0.7", "@types/jimp": "^0.2.28", "@types/node": "^20.17.x", "@types/node-cache": "^4.1.3", "@types/should": "^11.2.0", "@types/sinon": "^17.0.4", "@types/sm-crypto": "^0.3.4", "@types/vscode": "^1.93.0", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.11.0", "@vscode/test-cli": "^0.0.9", "@vscode/test-electron": "^2.4.0", "chai": "^4.3.10", "esbuild": "^0.25.0", "eslint": "^8.57.0", "husky": "^9.1.7", "npm-run-all": "^4.1.5", "prettier": "^3.3.3", "should": "^13.2.3", "ts-jest": "^29.3.2", "typescript": "^5.4.5", "vite": "^5.4.2"}, "dependencies": {"@anthropic-ai/sdk": "^0.56.0", "@google/genai": "^1.4.0", "@joycoder/agent-common": "workspace:*", "@joycoder/plugin-base-ai": "workspace:*", "@joycoder/shared": "workspace:^", "@modelcontextprotocol/sdk": "^1.11.1", "@test/joycoder-rag-mcp-server": "^1.0.77", "@types/clone-deep": "^4.0.4", "@types/pdf-parse": "^1.1.4", "@types/turndown": "^5.0.5", "@vscode/codicons": "^0.0.36", "axios": "^1.7.4", "cheerio": "^1.0.0", "chokidar": "^4.0.1", "clone-deep": "^4.0.1", "default-shell": "^2.2.0", "delay": "^6.0.0", "diff": "^5.2.0", "execa": "^9.5.2", "fast-deep-equal": "^3.1.3", "fast-xml-parser": "^4.3.2", "fastest-levenshtein": "^1.0.16", "get-folder-size": "^5.0.0", "globby": "^14.0.2", "iconv-lite": "^0.6.3", "ignore": "^7.0.3", "isbinaryfile": "^5.0.2", "jschardet": "^3.1.4", "mammoth": "^1.8.0", "minimatch": "^9.0.5", "monaco-vscode-textmate-theme-converter": "^0.1.7", "node-cache": "^5.1.2", "open": "^8.4.2", "openai": "^4.61.0", "os-name": "^6.0.0", "p-wait-for": "^5.0.2", "pdf-parse": "^1.1.1", "pinyin-pro": "^3.26.0", "posthog-node": "^4.8.1", "proper-lockfile": "^4.1.2", "serialize-error": "^11.0.3", "jimp": "^0.22.12", "simple-git": "^3.27.0", "sinon": "^19.0.2", "sm-crypto": "^0.3.13", "stream-json": "^1.8.0", "strip-ansi": "^7.1.0", "strip-bom": "^5.0.0", "tiktoken": "^1.0.21", "tree-sitter-wasms": "^0.1.11", "turndown": "^7.2.0", "uuid": "^9.0.1", "web-tree-sitter": "^0.22.6", "workerpool": "^9.2.0", "zod": "^3.23.8"}}