import * as vscode from 'vscode';
import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';

// 脱敏关键词
const excludeKey = ['pt_key', 'ptKey', 'pk', 'userToken'];

function maskSensitiveData(data: any): any {
  if (data === null || data === undefined) {
    return data;
  }

  if (typeof data === 'object') {
    if (Array.isArray(data)) {
      return data.map((item) => maskSensitiveData(item));
    }

    return Object.keys(data).reduce((acc, key) => {
      const value = data[key];
      acc[key] = excludeKey.includes(key) ? '*******************' : maskSensitiveData(value);
      return acc;
    }, {});
  }

  return data;
}

function getNonDefaultSettings() {
  const configuration = vscode.workspace.getConfiguration();
  return [
    'http.proxy',
    'http.noProxy',
    'http.proxyAuthorization',
    'http.proxyStrictSSL',
    'http.proxySupport',
    'http.electronFetch',
    'http.fetchAdditionalSupport',
    'http.proxyKerberosServicePrincipal',
    'http.systemCertificates',
    'http.experimental.systemCertificatesV2',
  ]
    .map((key) => {
      const i = configuration.inspect(key);
      const v = configuration.get(key, i?.defaultValue);
      if (
        v !== i?.defaultValue &&
        !(Array.isArray(v) && Array.isArray(i?.defaultValue) && v.length === 0 && i?.defaultValue.length === 0)
      ) {
        return `\n  "${key}": ${JSON.stringify(v)},`;
      }
      return '';
    })
    .join('');
}

function formatBytes(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

function getExtensionsInfo(): string {
  const extensions = vscode.extensions.all;
  const activeExtensions = extensions.filter((ext) => ext.isActive);

  return `- 总数: ${extensions.length}
- 已激活: ${activeExtensions.length}
- 内置扩展: ${extensions.filter((ext) => ext.packageJSON.isBuiltin).length}
- 第三方扩展: ${extensions.filter((ext) => !ext.packageJSON.isBuiltin).length}
- 开发中扩展: ${extensions.filter((ext) => ext.packageJSON.isUnderDevelopment).length}
- 前5个活跃扩展:
${activeExtensions
  .slice(0, 5)
  .map((ext) => `  - ${ext.packageJSON.displayName || ext.id} (${ext.packageJSON.version})`)
  .join('\n')}`;
}

export function initBaseDiag(context) {
  async function appendText(editor: vscode.TextEditor, string: string) {
    await editor.edit((builder) => {
      builder.insert(editor.document.lineAt(editor.document.lineCount - 1).range.end, string);
    });
  }

  context.subscriptions.push(
    vscode.commands.registerCommand('JoyCode.debug.diag', async () => {
      const document = await vscode.workspace.openTextDocument({ language: 'markdown' });
      const editor = await vscode.window.showTextDocument(document);

      const config = vscode.workspace.getConfiguration();

      await appendText(
        editor,
        `## JoyCode 诊断信息

### 系统环境
- JoyCode 版本: ${(vscode.env as any).joyCoderVersion}
- VS Code 版本: ${vscode.version}
- 应用名称: ${vscode.env.appName}
- 应用主机: ${vscode.env.appHost}
- 语言: ${vscode.env.language}
- 操作系统: ${process.platform} ${os.release()}
- CPU架构: ${os.arch()}
- Hosts配置:
\`\`\`
${(() => {
  try {
    const hostsPath =
      process.platform === 'win32'
        ? path.join(process.env.windir || 'C:\\Windows', 'System32', 'drivers', 'etc', 'hosts')
        : '/etc/hosts';
    return fs.readFileSync(hostsPath, 'utf8');
  } catch (error) {
    return `无法读取hosts文件: ${error.message}`;
  }
})()}
\`\`\`
- 内存: ${formatBytes(os.totalmem())} (可用: ${formatBytes(os.freemem())})
- Shell: ${process.env.SHELL || 'unknown'}
- 远程连接: ${vscode.env.remoteName ? '是' : '否'}${
          vscode.env.remoteName
            ? `
  - 远程名称: ${vscode.env.remoteName}`
            : ''
        }

### 工作区信息
- 工作区数量: ${vscode.workspace.workspaceFolders?.length || 0}
- 工作区路径: ${vscode.workspace.workspaceFolders?.map((folder) => folder.uri.fsPath).join(', ') || 'none'}
- 打开的编辑器数量: ${vscode.window.visibleTextEditors.length}
- 活动的终端数量: ${vscode.window.terminals.length}

### 配置信息
- Base URL: ${config.get('server.baseUrl')}
- 文件监视排除:
\`\`\`json
${JSON.stringify(config.get('files.watcherExclude'), null, 2)}
\`\`\`
- 自动保存: ${config.get('files.autoSave')}
- 编辑器缩进: ${config.get('editor.tabSize')} 空格
- 字体大小: ${config.get('editor.fontSize')}px
- 主题: ${config.get('workbench.colorTheme')}
- 图标主题: ${config.get('workbench.iconTheme')}
- 非默认设置:
\`\`\`json
{${getNonDefaultSettings()}
}
\`\`\`

### 性能配置
- 文件监视限制:
\`\`\`json
${JSON.stringify(config.get('files.watcherExclude'), null, 2)}
\`\`\`
- 搜索排除:
\`\`\`json
${JSON.stringify(config.get('search.exclude'), null, 2)}
\`\`\`
- 文件排除:
\`\`\`json
${JSON.stringify(config.get('files.exclude'), null, 2)}
\`\`\`
- 热退出: ${config.get('files.hotExit')}
- 大文件优化阈值: ${config.get('files.maxMemoryForLargeFilesMB')}MB

### 扩展信息
${getExtensionsInfo()}

### 状态信息
\`\`\`json
${JSON.stringify(
  maskSensitiveData(
    context.globalState.keys().reduce(
      (acc, key) => ({
        ...acc,
        [key]: context.globalState.get(key),
      }),
      {}
    )
  ),
  null,
  2
)}
\`\`\`
`
      );
    })
  );
}
