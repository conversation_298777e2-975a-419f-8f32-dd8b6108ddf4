import { z } from 'zod';
import {
  DEFAULT_MCP_TIMEOUT_SECONDS,
  McpMode,
  McpResource,
  McpResourceResponse,
  McpResourceTemplate,
  McpServer,
  McpTool,
  McpToolCallResponse,
  MIN_MCP_TIMEOUT_SECONDS,
  MCP_SETTINGS_SCHEMA,
} from './mcp';

export const AutoApproveSchema = z.array(z.string()).default([]);
export const BaseConfigSchema = z.object({
  autoApprove: AutoApproveSchema.optional(),
  disabled: z.boolean().optional(),
  timeout: z.number().min(MIN_MCP_TIMEOUT_SECONDS).optional().default(DEFAULT_MCP_TIMEOUT_SECONDS),
});

export const SseConfigSchema = BaseConfigSchema.extend({
  url: z.string().url(),
}).transform((config) => ({
  ...config,
  transportType: 'sse' as const,
}));

export const StdioConfigSchema = BaseConfigSchema.extend({
  command: z.string(),
  args: z.array(z.string()).optional(),
  env: z.record(z.string()).optional(),
  autoApprove: AutoApproveSchema.optional(),
  disabled: z.boolean().optional(),
  timeout: z.number().min(MIN_MCP_TIMEOUT_SECONDS).optional().default(DEFAULT_MCP_TIMEOUT_SECONDS),
}).transform((config) => ({
  ...config,
  transportType: 'stdio' as const,
}));

export const ServerConfigSchema = z.union([StdioConfigSchema, SseConfigSchema]);

export const McpSettingsSchema = z.object({
  mcpServers: z.record(ServerConfigSchema),
});
