/**
 * 管理全局状态
 */
import * as vscode from 'vscode';

export class WorkspaceState {
  static context: vscode.ExtensionContext;
  static hasWorkspaceFolder = !!vscode.workspace.workspaceFolders;

  static init(context: vscode.ExtensionContext) {
    this.context = context;
    this.hasWorkspaceFolder = !!vscode.workspace.workspaceFolders;
  }

  static update(...args: [string, any]) {
    if (this.hasWorkspaceFolder) {
      this.context.workspaceState.update(...args);
    }
  }

  static get(...args: [string]): any {
    if (this.hasWorkspaceFolder) {
      return this.context.workspaceState.get(...args);
    }
    return this.context.globalState.get(...args);
  }

  static del(key: string): any {
    this.update(key, undefined);
  }

  static keys(): any {
    if (this.hasWorkspaceFolder) return this.context.workspaceState.keys();
    return null;
  }
}
