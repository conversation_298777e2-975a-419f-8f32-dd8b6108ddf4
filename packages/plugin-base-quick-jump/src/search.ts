import * as vscode from 'vscode';
import { reportRd, openInBrowser, getSelectedText, openInVscode, sleep } from '@joycoder/shared';
import { openInVscodeWithBrowser } from '@joycoder/plugin-base-browser';
import { DEFAULT_START_URL } from '@joycoder/plugin-base-browser/src/shared/constant';
// 定义
import type BrowserPage from '@joycoder/plugin-base-browser/src/browser/BrowserPage';

const enum WebView {
  ExternalBrowser,
  VscodeWebview,
  VscodeBrowser,
}
interface SearchItemType {
  label: string;
  url: string;
  webview?: WebView;
  callback?: (text: string) => void;
}

const TARO_CAN_I_USE = 'http://ipaas-docs-pro.local-pf.jd.com/canIUse';
const SEARCH_LIST: SearchItemType[] = [
  {
    label: 'DevDocs',
    url: 'https://devdocs.io#q={queryStr}',
    webview: WebView.VscodeBrowser,
  },
  {
    label: 'MDN',
    url: 'https://developer.mozilla.org/zh-CN/search?q={queryStr}',
    webview: WebView.VscodeBrowser,
  },
  {
    label: 'Can I Use',
    url: 'https://caniuse.com/?search={queryStr}',
    webview: WebView.VscodeBrowser,
  },
  {
    label: 'Taro',
    url: 'https://docs.taro.zone/search?q={queryStr}',
    webview: WebView.VscodeBrowser,
  },
  {
    label: 'React',
    url: 'https://zh-hans.react.dev?q={queryStr}',
    webview: WebView.VscodeBrowser,
  },
  {
    label: 'Vue',
    url: 'https://cn.vuejs.org?q={queryStr}',
    webview: WebView.VscodeBrowser,
  },
  {
    label: 'JoySpace',
    url: 'https://joyspace.jd.com?q={queryStr}',
    webview: WebView.ExternalBrowser,
  },
  {
    label: '神灯',
    url: 'http://xingyun.jd.com/shendeng/search?q={queryStr}',
    webview: WebView.ExternalBrowser,
  },
  {
    label: '泰山',
    url: 'http://taishan.jd.com/global-search/index?key={queryStr}',
    webview: WebView.ExternalBrowser,
  },
  {
    label: 'CF',
    url: 'https://cf.jd.com/dosearchsite.action?queryString={queryStr}',
    webview: WebView.ExternalBrowser,
  },
  {
    label: 'Google',
    url: 'https://www.google.com/search?q={queryStr}',
    webview: WebView.VscodeBrowser,
  },
  {
    label: '百度',
    url: 'https://www.baidu.com/s?wd={queryStr}',
    webview: WebView.VscodeBrowser,
  },
  {
    label: 'JD-前端标准体系',
    url: 'http://ipaas-docs-pro.local-pf.jd.com/docs/standard/%E6%80%BB%E8%A7%88',
    webview: WebView.VscodeBrowser,
  },
  {
    label: 'JD-Can I Use',
    url: TARO_CAN_I_USE,
    webview: WebView.VscodeBrowser,
    callback: (keyword: string) => {
      openInVscodeWithBrowser(DEFAULT_START_URL, async (browserPage: BrowserPage) => {
        // 获取页面对象
        const playwrightPage = browserPage.playwrightPage;
        // 跳转指定页面
        await playwrightPage.goto(TARO_CAN_I_USE, { waitUntil: 'domcontentloaded' });
        // 追加兼容样式
        await sleep(200);
        await playwrightPage.addStyleTag({
          content: `.canIUse_Miao {
            margin-left: 5% !important;
            margin-right: 5% !important;
          }
          @media only screen and (max-width: 1000px) {
            .canIUse_Miao>div[class^="header_"] {
              padding-left: 16px !important;
              padding-right: 16px !important;
            }
            .canIUse_Miao input[class*="input"] {
              margin-left: 16px !important;
              margin-right: 16px !important;
            }
            .canIUse_Miao div[class^="wrap_"] {
              margin-left: 8px !important;
            }
            .canIUse_Miao div[class^="filter_"] {
              margin-right: 0 !important;
            }
          }`,
        });
        // 如果存在关键字则进行检索
        if (keyword && typeof keyword === 'string') {
          await sleep(200);
          await playwrightPage.type('.canIUse_Miao input', keyword);
          await sleep(200);
          await playwrightPage.$eval('.canIUse_Miao input', (e) => e.blur());
        }
      });
    },
  },
  {
    label: 'JD-JSSDK',
    url: 'http://doc.jd.com/jd-jssdk/v5/',
    webview: WebView.VscodeBrowser,
  },
];

export function addSearchItem(...item: SearchItemType[]) {
  SEARCH_LIST.push(...item);
}

function resortList(label) {
  const index = SEARCH_LIST.findIndex((item) => item.label === label);
  const item = SEARCH_LIST.splice(index, 1)[0];
  SEARCH_LIST.unshift(item);
}

async function search(type: string, totalText: string) {
  const item = SEARCH_LIST.filter((item) => item.label == type)[0];
  if (!item) return;

  let { url } = item;
  const { webview, label } = item;
  const searchStr = encodeURIComponent(totalText);
  url = url.replace('{queryStr}', searchStr);
  if (webview == WebView.VscodeBrowser) {
    openInVscodeWithBrowser(url);
  } else if (webview == WebView.VscodeWebview) {
    openInVscode({
      pageTitle: label,
      url,
      viewColumn: vscode.ViewColumn.Beside,
    });
  } else {
    openInBrowser(url);
  }
}

/**
 * 小灯泡逻辑
 */
class CodeActionPrivider implements vscode.CodeActionProvider {
  public static readonly providedCodeActionKinds = [vscode.CodeActionKind.QuickFix];

  public provideCodeActions(document: vscode.TextDocument, range: vscode.Range): vscode.CodeAction[] | undefined {
    const editor = vscode.window.activeTextEditor;
    if (!editor || editor.document !== document) {
      return;
    }

    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);
    if (!selectedText) {
      return;
    }

    const commandAction = this.createCommand('JoyCode: 快速搜索(Shift+Alt+S)', 'JoyCode.quickJump.search', [
      selectedText,
    ]);
    return [commandAction];
  }

  private createCommand(title, commandId, commandArgs): vscode.CodeAction {
    const action = new vscode.CodeAction(title, vscode.CodeActionKind.Refactor);
    action.command = {
      command: commandId,
      title: title,
      arguments: commandArgs,
    };
    return action;
  }
}

export const searchCommands = [
  vscode.commands.registerCommand('JoyCode.quickJump.search', async (queryString: string) => {
    const totalText = getSelectedText() || queryString || '';

    const selLabel = await vscode.window.showQuickPick(SEARCH_LIST.map((item) => item.label));
    if (!selLabel) return;

    const selItem = SEARCH_LIST.filter((item) => item.label == selLabel)[0];
    if (!selItem) return;

    if (selItem.callback) {
      selItem.callback(totalText);
    } else {
      search(selLabel, totalText);
    }
    resortList(selLabel);
    reportRd(11);
  }),
  vscode.languages.registerCodeActionsProvider('*', new CodeActionPrivider(), {
    providedCodeActionKinds: CodeActionPrivider.providedCodeActionKinds,
  }),
];
