import * as vscode from 'vscode';
import codeCompletionProvider from './codeCompletionProvider';
import CompletionCodeLensProvider from './utils/codeLensProcider';
import { updateStatusBarItem } from './utils/updateStatusBarItem';
import { completionModel } from './utils/constant';
import {
  getCompletionConfig,
  startReportAction,
  ActionType,
  isBusiness,
  codeModelConfigs,
  codeModelConfigsLocal,
  Logger,
  isBusinessLocal,
  ImmediateChangeConfiguration,
  setVscodeConfig,
  getPluginRunBaseConfig,
  getVscodeConfig,
  updateCodeCompletionsMoreContext,
} from '@joycoder/shared';
import { getInnerModelNameToBusiness } from './utils/util';
import { hookCodeStatistics } from './stats/codeStatManager';
export { stopCodeStatService } from './stats/codeStatService';

let vsDisposable: vscode.Disposable | null = null;
let removeCodeLensDisposable: vscode.Disposable | null = null;
let vsCodeLensDisposable: vscode.Disposable | null = null;
let commandDisposable: vscode.Disposable | null = null;
let manuallyDisposable: vscode.Disposable | null = null;
let newCompletionDisposable: vscode.Disposable | null = null;
let configDisposables: vscode.Disposable[] = [];
const g_isLoading = false;
const defaultModel = getInnerModelNameToBusiness('JoyCode-Base-Lite');
let completionsStatusBarItem: vscode.StatusBarItem | null = null;
const COMPLETION_STATUS_TEXT = isBusiness() ? 'JoyCode' : '预测补全';

export default function (context: vscode.ExtensionContext) {
  try {
    // 清理之前的资源
    disposeAllResources();

    commandDisposable = vscode.commands.registerCommand('JoyCode.code-completions', () => {
      if (vsDisposable) {
        vsDisposable.dispose();
        vsDisposable = null;
      }
      context.globalState.update('enableCompletionCommand', true);
      if (completionsStatusBarItem) {
        updateStatusBarItem(completionsStatusBarItem, g_isLoading, false, COMPLETION_STATUS_TEXT);
      }
      context.globalState.update('JoyCode.rejectInlineCompletion.number', '0');
      setCompletionsInit(context);
      setGlobalState(context, 'JoyCode.enableCompletionCommand', '1');
      setDefaultModel(context);
    });

    const statusBar = setStatusBar(context, 'JoyCode.code-completions-setting', 100, COMPLETION_STATUS_TEXT);
    completionsStatusBarItem = statusBar;

    const statusCommand = vscode.commands.registerCommand('JoyCode.code-completions-setting', () => {
      setCompletionsPopup(context);
    });

    context.subscriptions.push(statusCommand);
    if (commandDisposable) {
      context.subscriptions.push(commandDisposable);
    }

    manuallyTriggerCompletion(context);
    setStatusBarData(context);
    registerCodeLensProvider(context);
    setDefaultModel(context);
    hookCodeStatistics(context);
    switchConfigStatus(context);
    updateCodeCompletionsMoreContext();

    // 注册清理函数
    context.subscriptions.push(
      new vscode.Disposable(() => {
        disposeAllResources();
      })
    );
  } catch (error) {
    Logger.error('%c [ error ]-47', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
}

function disposeAllResources() {
  vsDisposable?.dispose();
  vsDisposable = null;
  removeCodeLensDisposable?.dispose();
  removeCodeLensDisposable = null;
  vsCodeLensDisposable?.dispose();
  vsCodeLensDisposable = null;
  commandDisposable?.dispose();
  commandDisposable = null;
  manuallyDisposable?.dispose();
  manuallyDisposable = null;
  newCompletionDisposable?.dispose();
  newCompletionDisposable = null;
  configDisposables.forEach((d) => d.dispose());
  configDisposables = [];

  if (CompletionCodeLensProvider.vsCodeLensInstance) {
    CompletionCodeLensProvider.vsCodeLensInstance.disposeCodeLenses();
  }
}

function setStatusBar(context: vscode.ExtensionContext, command?: string, right?: number, barText?: string) {
  const barItem: vscode.StatusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, right);
  barItem.command = command;
  context.subscriptions.push(barItem);
  updateStatusBarItem(barItem, g_isLoading, false, barText || '');
  return barItem;
}

async function setGlobalState(context: vscode.ExtensionContext, key: string, status: string | number) {
  await context.globalState.update(key, status);
}

async function setStatusBarData(context: vscode.ExtensionContext) {
  const enable: string | unknown = context.globalState.get('JoyCode.enableCompletionCommand');
  if (enable === '1') {
    vscode.commands.executeCommand('JoyCode.code-completions');
    if (completionsStatusBarItem) {
      updateStatusBarItem(completionsStatusBarItem, g_isLoading, false, COMPLETION_STATUS_TEXT);
    }
  } else {
    const remoteCompletionConfig = await getCompletionConfig();
    const switchFlag = remoteCompletionConfig?.switchFlag;
    if (switchFlag) {
      switchFlag && context.globalState.update('enableCompletionCommand', !!switchFlag);
      await setGlobalState(context, 'JoyCode.enableCompletionCommand', !!switchFlag ? '1' : '0');
      setCompletionsStatus(context, !!switchFlag);
    } else {
      setCompletionsInit(context);
    }
  }
}

async function setCompletionsStatus(context: vscode.ExtensionContext, enableCompletionCommand: boolean) {
  const reporter = startReportAction({
    actionCate: 'ai',
    actionType: enableCompletionCommand ? ActionType.completionOpen : ActionType.completionClose,
  });

  if (completionsStatusBarItem && enableCompletionCommand) {
    vscode.commands.executeCommand('JoyCode.code-completions');
    updateStatusBarItem(completionsStatusBarItem, g_isLoading, false, COMPLETION_STATUS_TEXT);
    reporter.success({});
  } else if (completionsStatusBarItem && !enableCompletionCommand) {
    context.globalState.update('enableCompletionCommand', false);
    setCompletionsInit(context);
    updateStatusBarItem(completionsStatusBarItem, g_isLoading, false, COMPLETION_STATUS_TEXT);
    setGlobalState(context, 'JoyCode.enableCompletionCommand', '0');
    reporter.success({});
  }
}

async function setCompletionsPopup(context: vscode.ExtensionContext) {
  const enableCompletionCommand = await context.globalState.get('enableCompletionCommand');
  const completionStatus = enableCompletionCommand ? '开启预测' : '关闭预测';
  const items = [
    {
      label: '开启预测',
      description: '开启代码自动补全及注释转代码等功能',
    },
    {
      label: '关闭预测',
      description: '关闭代码自动补全及注释转代码等功能',
    },
  ];
  const newCurrentItem = await getQuickPick(items, completionStatus);
  if (!newCurrentItem) return;
  if (newCurrentItem === '模型选择') {
    let codeModelConfig = isBusinessLocal() ? codeModelConfigsLocal : codeModelConfigs;
    if (!isBusiness()) {
      const codeModelRemote = await getPluginRunBaseConfig();
      codeModelConfig = codeModelRemote && codeModelRemote.length > 0 ? codeModelRemote : completionModel;
    }
    const label = (await context.globalState.get('JoyCode.gptCompletionModel')) || defaultModel;
    const newCurrentGPT = (await getQuickPick(codeModelConfig, label)) || label;
    const newCurrentOptionArr = codeModelConfig.filter(
      (codeModelConfigItem) => codeModelConfigItem.label === newCurrentGPT
    );
    const newCurrentOption = Array.isArray(newCurrentOptionArr) ? newCurrentOptionArr[0] : null;
    context.globalState.update('JoyCode.gptCompletionModel', newCurrentGPT);
    context.globalState.update('JoyCode.gptCompletionOption', newCurrentOption);
  } else if (newCurrentItem === '开启预测' || newCurrentItem === '关闭预测') {
    setCompletionsStatus(context, newCurrentItem === '开启预测');
  }
}

async function setDefaultModel(context) {
  setGlobalState(context, 'JoyCode.rejectInlineCompletion.number', '0');
  const cacheModel = await context.globalState.get('JoyCode.gptCompletionOption');
  let codeModelConfig = isBusinessLocal() ? codeModelConfigsLocal : codeModelConfigs;
  if (!isBusiness()) {
    const codeModelRemote = await getPluginRunBaseConfig();
    codeModelConfig = codeModelRemote && codeModelRemote.length > 0 ? codeModelRemote : completionModel;
  }
  if (cacheModel?.chat?.model === codeModelConfig[0]?.chat?.model) {
    return;
  }
  const modelOptionArr = codeModelConfig.filter((codeModelConfigItem) => codeModelConfigItem.label === defaultModel);
  const defaultModelOption = modelOptionArr[0] || completionModel[0];
  context.globalState.update('JoyCode.gptCompletionModel', defaultModelOption.label);
  context.globalState.update('JoyCode.gptCompletionOption', defaultModelOption);
}

async function getQuickPick(items, label, cb?: any) {
  const quickPick = vscode.window.createQuickPick();
  quickPick.items = items;
  quickPick.activeItems = quickPick.items.filter((item) => item.label == label);
  quickPick.placeholder = '预测补全开关和模型选择';
  quickPick.show();

  try {
    const newCurrentItem: string | undefined = await new Promise((resolve) => {
      const acceptDisposable = quickPick.onDidAccept(() => {
        acceptDisposable.dispose();
        hideDisposable.dispose();
        resolve(quickPick.selectedItems[0]?.label);
      });
      const hideDisposable = quickPick.onDidHide(() => {
        acceptDisposable.dispose();
        hideDisposable.dispose();
        resolve(undefined);
      });
    });
    cb?.(quickPick, newCurrentItem);
    return newCurrentItem;
  } finally {
    quickPick.dispose();
  }
}

async function registerCodeLensProvider(context) {
  const enableMenus = getVscodeConfig('JoyCode.config.codeLens-row-menus');
  if (enableMenus) {
    // 清理之前的实例
    if (vsCodeLensDisposable) {
      vsCodeLensDisposable.dispose();
      vsCodeLensDisposable = null;
    }
    if (CompletionCodeLensProvider.vsCodeLensInstance) {
      CompletionCodeLensProvider.vsCodeLensInstance.disposeCodeLenses();
    }

    const provider = new CompletionCodeLensProvider();
    vsCodeLensDisposable = provider.rerenderCodeLenses();
    CompletionCodeLensProvider.vsCodeLensInstance = provider;

    if (vsCodeLensDisposable) {
      context.subscriptions.push(vsCodeLensDisposable);
    }

    try {
      if (removeCodeLensDisposable) {
        removeCodeLensDisposable.dispose();
        removeCodeLensDisposable = null;
      }
      removeCodeLensDisposable = vscode.commands.registerCommand('JoyCode.codelens.del', (data) => {
        provider.removeCodeLensFromLine(data);
      });
      if (removeCodeLensDisposable) {
        context.subscriptions.push(removeCodeLensDisposable);
      }
    } catch (error) {}
    return vsCodeLensDisposable;
  } else {
    if (vsCodeLensDisposable) {
      vsCodeLensDisposable.dispose();
      vsCodeLensDisposable = null;
    }
    if (CompletionCodeLensProvider.vsCodeLensInstance) {
      CompletionCodeLensProvider.vsCodeLensInstance.disposeCodeLenses();
    }
  }
  return null;
}

function manuallyTriggerCompletion(context: vscode.ExtensionContext) {
  if (manuallyDisposable) {
    manuallyDisposable.dispose();
    manuallyDisposable = null;
  }
  manuallyDisposable = vscode.commands.registerCommand('JoyCode.code-completion.manually', async () => {
    const editor = vscode.window.activeTextEditor;
    if (editor) {
      await vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
      await setGlobalState(context, 'JoyCode.lastManualCompletionTimestamp', +new Date());
      await vscode.commands.executeCommand('editor.action.inlineSuggest.trigger');
    }
  });
  if (manuallyDisposable) {
    context.subscriptions.push(manuallyDisposable);
  }

  if (newCompletionDisposable) {
    newCompletionDisposable.dispose();
    newCompletionDisposable = null;
  }
  newCompletionDisposable = vscode.commands.registerCommand('JoyCode.completion.get-new-completion', async () => {
    const editor = vscode.window.activeTextEditor;
    if (editor) {
      await vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
      await vscode.commands.executeCommand('editor.action.inlineSuggest.trigger');
    }
  });
  if (newCompletionDisposable) {
    context.subscriptions.push(newCompletionDisposable);
  }
  return manuallyDisposable;
}

function setCompletionsInit(context) {
  if (vsDisposable) {
    vsDisposable.dispose();
    vsDisposable = null;
  }
  const codeProvider = codeCompletionProvider(g_isLoading, completionsStatusBarItem!, false, context);
  vsDisposable = vscode.languages.registerInlineCompletionItemProvider({ pattern: '**' }, codeProvider);
  if (vsDisposable) {
    context.subscriptions.push(vsDisposable);
  }
}

function switchConfigStatus(context) {
  try {
    // 清理之前的监听器
    configDisposables.forEach((d) => d.dispose());
    configDisposables = [];

    const disposable1 = new ImmediateChangeConfiguration({
      commandId: 'JoyCode.config.codeCompletionsGenTask',
      onChange: (config) => {
        try {
          if (config == 'FUNCTION') {
            setVscodeConfig('JoyCode.config.codeCompletionsMaxLines', 58);
          } else if (config == 'BLOCK') {
            setVscodeConfig('JoyCode.config.codeCompletionsMaxLines', 24);
          } else {
            setVscodeConfig('JoyCode.config.codeCompletionsMaxLines', 1);
          }
        } catch (error) {
          setVscodeConfig('JoyCode.config.codeCompletionsMaxLines', 1);
        }
      },
    });

    const disposable2 = new ImmediateChangeConfiguration({
      commandId: 'JoyCode.config.codeLens-row-menus',
      onChange: () => {
        try {
          registerCodeLensProvider(context);
        } catch (error) {}
      },
    });

    configDisposables.push(disposable1, disposable2);
    context.subscriptions.push(...configDisposables);
  } catch (error) {}
}
