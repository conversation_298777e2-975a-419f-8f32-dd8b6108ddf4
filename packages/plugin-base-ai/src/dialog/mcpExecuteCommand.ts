import * as vscode from 'vscode';
import { McpSendMsgType, McpCommonSendMsgType } from '@joycoder/shared/src/mcp/McpTypes';
export async function mcpExecuteCommand(settingWebview: vscode.WebviewPanel | undefined = undefined, data?: any) {
  const type = data.type;
  switch (type) {
    case McpSendMsgType.MCP_LIST:
      // 获取mcp列表
      const mcpList = await vscode.commands.executeCommand('joycode.autoCode.mcp.msg', data);
      settingWebview?.webview.postMessage({
        type: 'COMMON',
        payload: {
          type: McpCommonSendMsgType.SETTING_MCP,
          data: {
            type: McpSendMsgType.MCP_LIST,
            mcpList,
          },
          isLeft: data === undefined || !!data,
        },
      });
      break;
    case McpSendMsgType.GET_MCP_SERVER:
      // 获取mcp列表
      const mcpServerDetail = await vscode.commands.executeCommand('joycode.autoCode.mcp.msg', data);
      settingWebview?.webview.postMessage({
        type: 'COMMON',
        payload: {
          type: McpCommonSendMsgType.SETTING_MCP,
          data: {
            type: McpSendMsgType.GET_MCP_SERVER,
            mcpServerDetail,
          },
          isLeft: data === undefined || !!data,
        },
      });
      break;
    case McpSendMsgType.OPEN_MCP_SETTING_FILE:
      vscode.commands.executeCommand('joycode.autoCode.mcp.msg', data);
      break;
    case McpSendMsgType.GET_MCP_INSTALL_PARAM:
      // 安装mcp参数
      const mcpInstallParam = await vscode.commands.executeCommand('joycode.autoCode.mcp.msg', data);
      settingWebview?.webview.postMessage({
        type: 'COMMON',
        payload: {
          type: McpCommonSendMsgType.SETTING_MCP,
          data: {
            type: McpSendMsgType.GET_MCP_INSTALL_PARAM,
            mcpInstallParam,
          },
          isLeft: data === undefined || !!data,
        },
      });
      break;
    case McpSendMsgType.GET_MCP_SETTINGS_FILE_CONTENT:
      // 安装mcp参数
      const mcpSettingsFileContent = await vscode.commands.executeCommand('joycode.autoCode.mcp.msg', data);
      settingWebview?.webview.postMessage({
        type: 'COMMON',
        payload: {
          type: McpCommonSendMsgType.SETTING_MCP,
          data: {
            type: McpSendMsgType.GET_MCP_SETTINGS_FILE_CONTENT,
            fileContent: mcpSettingsFileContent,
          },
        },
      });
      break;
    case McpSendMsgType.UPDATE_OR_INSERT_MCP_SERVER:
      // 更新或插入 MCP 服务器
      const resFileContent = await vscode.commands.executeCommand('joycode.autoCode.mcp.msg', data);
      settingWebview?.webview.postMessage({
        type: 'COMMON',
        payload: {
          type: McpCommonSendMsgType.SETTING_MCP,
          data: {
            type: McpSendMsgType.UPDATE_OR_INSERT_MCP_SERVER,
            fileContent: resFileContent,
          },
        },
      });
      break;
    case McpSendMsgType.OPEN_OLD_MCP_SERVER_CONFIG_PAGE:
      vscode.commands.executeCommand('joycoder.Coder.mcpButtonClicked');
      //vscode.postMessage({ type: 'updateServerConnections' });
      break;
    case McpSendMsgType.REFRESH_MCP_SERVICE:
      vscode.commands.executeCommand('joycode.autoCode.mcp.msg', data);
      break;
    case McpSendMsgType.GET_MCP_CONNECTION_SERVER:
      const mcpConnectionServer = await vscode.commands.executeCommand('joycode.autoCode.mcp.msg', data);
      settingWebview?.webview.postMessage({
        type: 'COMMON',
        payload: {
          type: McpCommonSendMsgType.SETTING_MCP,
          data: {
            type: McpSendMsgType.GET_MCP_CONNECTION_SERVER,
            mcpServers: mcpConnectionServer,
          },
        },
      });
      break;
    case McpSendMsgType.TOGGLE_MCP_SERVER:
      await vscode.commands.executeCommand('joycode.autoCode.mcp.msg', data);
      break;
    case McpSendMsgType.RESTART_MCP_SERVER:
      await vscode.commands.executeCommand('joycode.autoCode.mcp.msg', data);
      break;
    case McpSendMsgType.UPDATE_TOOL_AUTO_APPROVE:
      await vscode.commands.executeCommand('joycode.autoCode.mcp.msg', data);
      break;
    case McpSendMsgType.DELETE_MCP_SERVER:
      await vscode.commands.executeCommand('joycode.autoCode.mcp.msg', data);
      break;
    default:
      console.log('消息未匹配');
      break;
  }
}
