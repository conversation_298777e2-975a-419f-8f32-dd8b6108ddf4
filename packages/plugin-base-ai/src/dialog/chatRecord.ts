import fs from 'fs/promises';
import os from 'os';
import * as path from 'path';

interface ChatTabItem {
  label: string;
  key: string;
  closable?: boolean;
  list: string[];
}

export async function saveChatPrompt(data) {
  try {
    const fileName = `${data.fileName}.json`;
    const chatPromptItems: Record<string, ChatTabItem> = data.chatPrompts;
    const chatHistroyFolder = await ensureTaskDirectoryExists();
    const chatHistroyPath = path.join(chatHistroyFolder, fileName);
    const chatContent =
      typeof chatPromptItems === 'string' ? chatPromptItems : JSON.stringify(chatPromptItems, null, 2);
    await fs.writeFile(chatHistroyPath, chatContent, 'utf-8');
  } catch (error) {
    console.error('%c [ error ]-21', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
}

export async function getChatPrompt(data): Promise<string> {
  try {
    const storageItems = data?.storageItems;
    const fileName = `${data.fileName}.json`;
    const chatHistroyFolder = await ensureTaskDirectoryExists();
    const chatHistroyPath = path.join(chatHistroyFolder, fileName);
    // 检查文件是否存在
    const fileExists = await fileExistsAtPath(chatHistroyPath);
    if (fileExists) {
      const content = await fs.readFile(chatHistroyPath, 'utf-8');
      return content;
    } else {
      storageItems &&
        saveChatPrompt({
          fileName: data.fileName,
          chatPrompts: storageItems || '{}',
        });
      return storageItems || '';
    }
  } catch (error) {
    return '';
  }
}

async function ensureTaskDirectoryExists(): Promise<string> {
  // 本地环境：使用传统的路径构建方式
  const rootPath = os.homedir();
  const taskDir = path.join(rootPath, '.joycode');
  await fs.mkdir(taskDir, { recursive: true });
  return taskDir;
}
async function fileExistsAtPath(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath);
    return true;
  } catch (error) {
    return false;
  }
}
