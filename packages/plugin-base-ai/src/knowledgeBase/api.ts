import to from 'await-to-js';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import os from 'os';
import fetch from 'node-fetch';
import { getJdhCgiUrl, getBaseUrl, getJdhLoginInfo } from '@joycoder/shared';

interface DataSetsProps {
  page?: number;
  page_size?: number;
  name?: string;
  orderby?: string;
  desc?: string;
  dataset_id?: string;
  id?: string;
}
interface DocInfo extends DataSetsProps {
  keywords?: string;
  document_id?: string;
  document_name?: string;
}

export class DataSetsAPI {
  // private static readonly getBaseUrl() = 'http://agentflow.jd.com/api/v1/datasets';
  private static readonly ptKey = '';
  // 'Zmxhc2g9M194VTNRWnR1NU5zY0VXUG1sY3V4dU9INU9Nb3hrYTVrTDlBdWpOdmx1YU5fZWFfMVBNeUhsLVI5MUV3LVhkSm40czhwT2hpLWx5dlZ5TVQwNWNxdlZWY012czNMNTZfck9PaVNTM3ZiMzVFaEVhaWdRb1JaeGJmdm1Ba1VsbGQzZEpOejhPR2lkMkktV2pCa3Flbk5iRlZOWFNSYjUmZWlkPSYzQUI5RDIzRjdBNEIzQ1NTPWpkZDAzWFFBUVVMMlVHQkNIWjNRUUg0SlFaV0lXSE0yMzJFUEwzVFNFNTQ1U1o3RFBRVlcyUElFNUhXQ0pNQlpXQ1NHSFc2Q1ZMTjdZS0taUTVYUlg0WlhNVVRPN1pZQUFBQU1ZUEZVQkhDUUFBQUFBQ1JTWkFXQzVSNUpSVkFY';
  static getBaseUrl() {
    return `${getJdhCgiUrl(getBaseUrl() + '/api/saas/knowledge/v1/datasets')}`;
  }
  static getHeaders() {
    const baseHeaders = {
      // Authorization: 'Bearer ragflow-I1OTI2MmMwNjYyYzExZjBiOGFlMDI0Mm',
      ptKey: getJdhLoginInfo()?.ptKey || this.ptKey,
      ...(getJdhLoginInfo()?.loginType && { loginType: getJdhLoginInfo()?.loginType }),
      // loginType: 'N_PIN_PC',
    };
    return baseHeaders;
  }

  static headers() {
    return {
      ...this.getHeaders(),
      'content-Type': 'application/json',
    };
  }

  /**
   * 获取知识库数据集列表
   * @param props - 查询参数，包含分页、名称、排序等选项
   * @returns Promise<any[]> 返回数据集数组，失败时返回空数组
   */
  public static async getDataSets(props?: DataSetsProps) {
    const { page, page_size, name, orderby, desc } = props || {};
    const [err, response]: [any, any] = await to(
      axios.get(`${this.getBaseUrl()}`, {
        headers: this.getHeaders(),
        params: {
          page,
          page_size,
          name,
          orderby,
          desc,
        },
      })
    );
    if (err) {
      return [];
    }
    const resData = response.data?.data || [];
    try {
      const dataStesPath = path.join(os.homedir(), '.joycode', 'knowledge-base.json');
      const dir = path.dirname(dataStesPath);
      // 确保目录存在
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      fs.writeFileSync(dataStesPath, JSON.stringify(resData, null, 2));
    } catch (error) {
      console.error('%c [ error ]-54', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
    return resData;
  }

  public static async addDataSets(props?: DataSetsProps) {
    const { name } = props || {};
    const [err, response]: [any, any] = await to(
      axios.post(
        `${this.getBaseUrl()}`,
        {
          name,
        },
        { headers: this.headers() }
      )
    );
    if (err) {
      return null;
    }
    const resData = response.data;
    return resData;
  }

  public static async updateDataSetsById(props?: DataSetsProps) {
    const { name, dataset_id, id } = props || {};
    const [err, response]: [any, any] = await to(
      axios.put(
        `${this.getBaseUrl()}/${dataset_id || id}`,
        {
          name,
        },
        {
          headers: this.headers(),
        }
      )
    );
    if (err) {
      return [];
    }
    const resData = response.data;
    return resData;
  }

  public static async delDataSetsById(props?: DataSetsProps) {
    const { dataset_id, id } = props || {};
    const [err, response]: [any, any] = await to(
      axios.delete(`${this.getBaseUrl()}`, {
        headers: this.headers(),
        data: { ids: [dataset_id || id] },
      })
    );
    if (err) {
      return [];
    }
    const resData = response.data;
    return resData;
  }
  public static async getDocList(props: DocInfo) {
    const { page = 1, page_size = 20, orderby, desc, dataset_id, document_name } = props;
    const [err, response]: [any, any] = await to(
      axios.get(`${this.getBaseUrl()}/${dataset_id}/documents`, {
        headers: this.getHeaders(),
        params: {
          page,
          page_size,
          name: document_name,
          orderby,
          desc,
        },
      })
    );
    if (err) {
      return [];
    }
    const resData = response.data?.data || [];
    return resData;
  }
  public static async delDocInfo(props: DocInfo) {
    const { dataset_id, id } = props || {};
    const [err, response]: [any, any] = await to(
      axios.delete(`${this.getBaseUrl()}/${dataset_id}/documents`, {
        headers: this.headers(),
        data: { ids: [id] },
      })
    );
    if (err) {
      return [];
    }
    const resData = response.data;
    return resData;
  }
  public static async stopChunk(props: DocInfo) {
    const { dataset_id, id } = props || {};
    const [err, response]: [any, any] = await to(
      axios.delete(`${this.getBaseUrl()}/${dataset_id}/chunks`, {
        headers: this.headers(),
        data: { document_ids: [id] },
      })
    );
    if (err) {
      return [];
    }
    const resData = response.data;
    return resData;
  }
  public static async reChunkDoc(props) {
    const { dataset_id, document_ids } = props || {};
    const [err, response]: [any, any] = await to(
      axios.post(
        `${this.getBaseUrl()}/${dataset_id}/chunks`,
        {
          document_ids,
        },
        { headers: this.headers() }
      )
    );
    if (err) {
      return null;
    }
    const resData = response.data;
    return resData;
  }
  // Joyspace 授权
  public static async joyspaceAuth(props) {
    const { me_code, me_expire_in } = props || {};
    const [err, response]: [any, any] = await to(
      axios.post(
        `${getJdhCgiUrl(getBaseUrl() + '/api/saas/knowledge/v1/auth/mecode')}`,
        {
          me_code,
          me_expire_in,
        },
        { headers: this.headers() }
      )
    );
    if (err) {
      return null;
    }
    const resData = response.data;
    return resData;
  }
  public static async getDocUrlData(values) {
    const { type = '' } = values;
    if (type === 'joyspace') {
      const { name, source_type, type, location, datasetId } = values;
      const [error, response] = await to(
        axios.post(
          `${this.getBaseUrl()}/${datasetId}/documents/url`,
          {
            name,
            source_type,
            type,
            location,
          },
          {
            headers: this.headers(), // 不包含Content-Type，让FormData自动设置
          }
        )
      );
      if (error || response?.data.code != 0) {
        console.error('Upload error:', error);
        return { code: 500, msg: `joyspace添加失败` };
      }
      if (response?.data.code === 0) {
        return response?.data;
      }
      return { code: 500, msg: `'joyspace添加失败'}` };
    }
    const formData = new FormData();
    if (type === 'local') {
      const filesData = values.filesData;
      for (let index = 0; index < filesData.length; index++) {
        const file = filesData[index];
        const fileName = file.fileName;
        const fileContent = file.fileContent;
        // 2. 将HTML转换为Blob对象
        const blob = new File([fileContent], fileName, {
          type: 'text/plain;charset=utf-8',
        });
        // 将Blob作为文件添加到FormData
        formData.append('file[]', blob);
      }
    } else {
      const fileName = values.docName + '.md';
      const docUrl = values.docUrl;
      const [err, response]: [any, any] = await to(fetch(`${docUrl}`));
      if (err) {
        return { code: 500, msg: '文档地址错误' };
      }
      // 检查响应状态
      if (!response.ok) {
        return { code: 500, msg: `获取文档失败: ${response.status} ${response.statusText}` };
      }
      const fileContent = await response.text();
      // 2. 将HTML转换为Blob对象
      const blob = new File([fileContent], fileName, {
        type: 'text/plain;charset=utf-8',
      });
      // 将Blob作为文件添加到FormData
      formData.append('file[]', blob);
    }
    const datasetId = values.datasetId;
    // 4. 上传文件 - 移除手动设置的Content-Type，让浏览器自动设置
    const [uploadErr, uploadResponse] = await to(
      axios.post(`${this.getBaseUrl()}/${datasetId}/documents`, formData, {
        headers: this.getHeaders(), // 不包含Content-Type，让FormData自动设置
      })
    );

    // 优化错误处理
    if (uploadErr) {
      console.error('Upload error:', uploadErr);
      return { code: 500, msg: `上传失败: ${uploadErr.message || '网络错误'}` };
    }

    if (uploadResponse.data) {
      try {
        const result = uploadResponse.data;
        return result;
      } catch (jsonErr) {
        console.error('JSON parse error:', jsonErr);
        return { code: 500, msg: '响应解析失败' };
      }
    } else {
      return { code: 500, msg: '解析失败' };
    }
  }
}
