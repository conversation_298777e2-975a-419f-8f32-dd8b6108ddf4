#!/bin/bash
get_os_type() {
    local os_type
    if [[ "$OSTYPE" == "linux-gnu"* || "$OSTYPE" == "darwin"* ]]; then
        os_type="unix-like"
    elif [[ "$OSTYPE" == "cygwin" || "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        os_type="windows"
    else
         os_type="os"
    fi
    echo "$os_type"
}
# 根据操作系统设置路径转换规则
convert_path() {
    local path="$1"
case  $(get_os_type) in
     os)
   echo "$path" 
        ;;
    windows)
    echo $(cygpath -w $path)
        ;;
    unix-like)
   echo "$path"  
        ;;
    *)
        ;;
esac
}

# 检查端口是否存活的函数
check_port_alive() {
    local port=$1
    local os_type=$(get_os_type)
    
    case "$os_type" in
        windows)
            # Windows下使用netstat检查端口
            netstat -ano | findstr ":$port " >/dev/null 2>&1
            return $?
            ;;
        *)
            # Unix-like系统使用原有逻辑
            # 使用 nc (netcat) 检查端口是否监听
            if command -v nc >/dev/null 2>&1; then
                nc -z localhost "$port" 2>/dev/null
                return $?
            elif command -v telnet >/dev/null 2>&1; then
                # 如果没有nc，使用telnet
                timeout 1 telnet localhost "$port" >/dev/null 2>&1
                return $?
            else
                # 如果都没有，使用curl简单测试
                curl -s --connect-timeout 1 "http://localhost:$port" >/dev/null 2>&1
                return $?
            fi
            ;;
    esac
}

# 获取当前工作目录
current_project=$(pwd | tr -d '\n')
# 获取gitport
git_port=""
# 输入文本
input_text=$(grep -o '"[^"]*": [0-9]*' "$HOME/joyCoder/project_cache/git_commit_port.json" | sed 's/"\([^"]*\)": \([0-9]*\)/\1=\2/')
# 转换当前工作目录
converted_current_dir=$(convert_path "$current_project")

# 检测是否为Windows系统
is_windows=false
if [[ "$(get_os_type)" == "windows" ]]; then
    is_windows=true
fi

# 如果不是Windows系统，执行IDE进程检测和端口匹配逻辑
if [[ "$is_windows" == false ]]; then
    # 获取IDE进程ID
        ide_pid=$(ps -o ppid= -p $PPID | tr -d ' ')
        ide_ppid=$(ps -o ppid= -p $ide_pid | tr -d ' ')
        ide_pppid=$(ps -o ppid= -p $ide_ppid | tr -d ' ')
        IdePid=$(ps -o ppid= -p $ide_pppid | tr -d ' ')
        if [  $IdePid = 1 ]; then  
                IdePid=$ide_pppid 
        fi
        if [  $ide_pppid = 1 ]; then  
                IdePid=$ide_ppid 
        fi
        #test
        # echo IDE Pid: $IdePid
        # echo "Grandparent process: $(ps -o comm= $IdePid)"
        # ps -f -p $IdePid > ./git-hook-process1.log



        # 存储匹配的端口列表
        matching_ports=()
        matching_projects=()

        # 处理每一行并转换路径
        while IFS= read -r line; do
            if [[ $line =~ ^([^=]+)=[[:space:]]*([0-9]+),?$ ]]; then
                original_key="${BASH_REMATCH[1]}"
                port="${BASH_REMATCH[2]}"
                
                if check_port_alive "$port"; then
                 #test
                # echo "Key: $original_key, Port: $port"
                    # 检查key是否包含 | 分隔符 (新格式: ideType|projectPath)
                    if [[ "$original_key" == *"|"* ]]; then
                        # 提取项目路径部分 (| 后面的部分)
                        original_path="${original_key#*|}"
                    else
                        # 兼容旧格式，直接使用原始路径
                        original_path="$original_key"
                    fi

                    converted_path=$(convert_path "$original_path")
                    # 判断提取的路径是否等于转换后的当前工作目录
                    if [[ "$converted_path" == "$converted_current_dir" ]]; then
                        # 找到匹配的项目，添加到候选列表
                        matching_ports+=("$port")
                        matching_projects+=("$original_path")
                    fi
                
            
                else
                    # 删除不存活端口对应的数据 通过替换文件中 "9817," 这一行为空实现
                    config_file="$HOME/joyCoder/project_cache/git_commit_port.json"
                    if [[ -f "$config_file" ]]; then
                       #test
                        # echo   "===="$port

                        # 读取文件内容到变量
                        file_content=$(cat "$config_file")
                        
                        # 使用字符串替换删除包含该端口的条目
                        # 匹配格式: "path": port, 或 "ideType|path": port,
                        updated_content=$(echo "$file_content" | sed "s/\"[^\"]*\": ${port}\,//g")
                        # 清理可能产生的空行
                        updated_content=$(echo "$updated_content" | sed '/^[[:space:]]*$/d')

                        # 直接写回文件
                        echo "$updated_content" > "$config_file"
                        #test
                        # echo "$updated_content"
                        # echo "Removed dead port $port from config file"
                        
                    fi
                fi
            fi
        done <<< "$input_text"


        # 如果找到匹配的端口，检查哪个端口存活
        if [ ${#matching_ports[@]} -gt 0 ]; then
            for i in "${!matching_ports[@]}"; do
                port="${matching_ports[$i]}"
                project="${matching_projects[$i]}"
                

                # 检查端口是否存活
                if check_port_alive "$port"; then
                    # 获取端口对应的IDE进程ID
                    PID=$(lsof -t -i :$port)
                    p_pid=$(ps -o ppid= -p $PID | tr -d ' ')
                    PortPid=$(ps -o ppid= -p $p_pid | tr -d ' ')
                    if [  $PortPid = 1 ]; then  
                        PortPid=$p_pid 
                    fi
                     #test
                    # echo port Pid: $PortPid
                    # echo "Grandparent process: $(ps -o comm= $PortPid)"
                    # ps -f -p $PortPid > ./git-hook-process.log
                    if [ "$IdePid" = "$PortPid" ]; then              
                        git_port="$port"
                        current_project="$project"
                         #test
                        # echo "$port====$project"  > ./git-hook-process3.log
                        break
                    else
                        git_port="$port"
                    fi
                

                    
                fi
            done

            # 如果没有找到存活的端口，使用第一个匹配的端口
            if [[ -z "$git_port" ]]; then
                git_port="${matching_ports[0]}"
                current_project="${matching_projects[0]}"
                # echo "No alive port found, using first match: $git_port for project: $current_project"
            fi
        fi
else

    # 存储匹配的端口列表
        matching_ports=()
        matching_projects=()

        # 处理每一行并转换路径
        while IFS= read -r line; do
            if [[ $line =~ ^([^=]+)=[[:space:]]*([0-9]+),?$ ]]; then
                original_key="${BASH_REMATCH[1]}"
                port="${BASH_REMATCH[2]}"
                
                if check_port_alive "$port"; then
                
                    # 检查key是否包含 | 分隔符 (新格式: ideType|projectPath)
                    if [[ "$original_key" == *"|"* ]]; then
                        # 提取项目路径部分 (| 后面的部分)
                        original_path="${original_key#*|}"
                    else
                        # 兼容旧格式，直接使用原始路径
                        original_path="$original_key"
                    fi

                    converted_path=$(convert_path "$original_path")
                    # 判断提取的路径是否等于转换后的当前工作目录
                    if [[ "$converted_path" == "$converted_current_dir" ]]; then
                        # 找到匹配的项目，添加到候选列表
                        matching_ports+=("$port")
                        matching_projects+=("$original_path")
                    fi
                
            
                else
                    # 删除不存活端口对应的数据 通过替换文件中 "9817," 这一行为空实现
                    config_file="$HOME/joyCoder/project_cache/git_commit_port.json"
                    if [[ -f "$config_file" ]]; then
                        # echo   "===="$port
                        # 读取文件内容到变量
                        file_content=$(cat "$config_file")
                        
                        # 使用字符串替换删除包含该端口的条目
                        # 匹配格式: "path": port, 或 "ideType|path": port,
                        updated_content=$(echo "$file_content" | sed "s/\"[^\"]*\": ${port}\,//g")
                        # 清理可能产生的空行
                        updated_content=$(echo "$updated_content" | sed '/^[[:space:]]*$/d')
                        # 直接写回文件
                        echo "$updated_content" > "$config_file"
                        
                        # echo "Removed dead port $port from config file"
                    fi
                fi
            fi
        done <<< "$input_text"

        # 如果找到匹配的端口，检查哪个端口存活
        if [ ${#matching_ports[@]} -gt 0 ]; then
            for i in "${!matching_ports[@]}"; do
                port="${matching_ports[$i]}"
                project="${matching_projects[$i]}"
                

                # 检查端口是否存活
                if check_port_alive "$port"; then
                       git_port="$port"
                        current_project="$project"
                        # echo "$port====$project"  > ./git-hook-process3.log
                        break
                fi
            done

            # 如果没有找到存活的端口，使用第一个匹配的端口
            if [[ -z "$git_port" ]]; then
                git_port="${matching_ports[0]}"
                current_project="${matching_projects[0]}"
                # echo "No alive port found, using first match: $git_port for project: $current_project"
            fi
        fi


fi  # 结束Windows检测的条件判断

# echo $git_port
# 初始化 JSON 结构
json_output="{\"current_project\":\"$current_project\"}"
# 将 JSON 转换为 Base64
case  $(get_os_type) in
     os)
       json_base64=$(echo  "$json_output" | base64 )
        ;;
    windows)
       json_base64=$(echo  "$json_output" | base64 -w 0)
        ;;
    unix-like)
       json_base64=$(echo  "$json_output" | base64 )
        ;;
    *)
        ;;
esac
# echo $git_port > ./commitPort.log
# 构建和打印 curl 命令
curl_command="curl http://localhost:$git_port/gitCommit?pro=$json_base64"
# echo "$curl_command"
# 执行 curl 命令
(eval "$curl_command" &> /dev/null &)


