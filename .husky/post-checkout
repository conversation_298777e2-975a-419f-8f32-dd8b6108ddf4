#!/bin/sh

# 获取当前分支名
current_branch=$(git symbolic-ref --short HEAD 2>/dev/null)

# 获取旧分支名
old_branch=$1

# 获取新分支和是否是新分支标志
new_branch=$2
is_branch_checkout=$3

# 生成 UUID 函数
gen_uuid() {
  cat /proc/sys/kernel/random/uuid 2>/dev/null || uuidgen 2>/dev/null
}
project_path=$(pwd)
# 判断是否是新分支创建
if [ "$is_branch_checkout" -eq 1 ] && [ "$new_branch" = "$old_branch" ]; then
    # 检查新分支是否在远程存在
    if ! git branch -r | grep -q "origin/$current_branch$"; then
        # 获取远程仓库URL
        remote_url=$(git config --get remote.origin.url)
        # 获取当前用户
        current_user=$(git config user.name)
        #  echo "检测到新的本地分支创建，且远程不存在该分支："
        # echo "当前分支: $current_branch"
        # echo "旧分支: $old_branch"
        # echo "新分支: $new_branch"
        # echo "远程仓库URL: $remote_url"
        # echo "用户: $current_user"
        # 时间戳
        # 用shell方式获取13位毫秒级时间戳，兼容macOS和Linux
        start_time=$(($(date '+%s')*1000+$(date '+%N')/1000000))

        # 组装JSON
        json=$(
          cat <<EOF
             {
  "user": "$current_user",
  "question": null,
  "type": 1,
  "osName": "$(uname -s)",
  "ideaVersion": null,
  "ideaPlatform": null,
  "pluginVersion": null,
  "consumeTime": 0.0,
  "computerName": null,
  "computerDomain": null,
  "startTime": $start_time,
  "endTime": null,
  "result": null,
  "actionType": "checkout-b",
  "conversationId": null,
  "projectName": "$project_path",
  "curFileName": null,
  "curFilePath": null,
  "extendMsg": {
    "oldBranch": "$old_branch",
      "gitUrl": "$remote_url",
    "newBranch": "$current_branch"
  },
  "loginErp": null,
  "model": null,
  "userToken":"$current_user"
}
EOF
        )
        # echo "$json"

        # http://jdhgpt.jd.com/history/opeartionSave post请求这个发送数据json
        curl -X POST -H "Content-Type: application/json" -d "$json" http://jdhgpt.jd.com/history/opeartionSave > /dev/null 2>&1 &
        
    fi
fi
