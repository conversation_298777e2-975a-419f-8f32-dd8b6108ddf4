#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * VSCode插件替换脚本
 * 功能：解压vsix文件并替换到指定的插件目录
 */
class VSCodePluginReplacer {
  constructor() {
    this.vsixPattern = /joycoder-editor-.*\.vsix$/;
    this.sourceDir = path.join(process.cwd(), 'packages/vscode-IDE');
    this.targetDir = '/Applications/JoyCode.app/Contents/Resources/app/extensions/joycode.joycoder-editor';
    this.tempDir = path.join(this.sourceDir, 'temp_extract');
  }

  /**
   * 记录日志
   */
  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  /**
   * 查找vsix文件
   */
  findVsixFile() {
    try {
      const files = fs.readdirSync(this.sourceDir);
      const vsixFiles = files.filter((file) => this.vsixPattern.test(file));

      if (vsixFiles.length === 0) {
        throw new Error(`在目录 ${this.sourceDir} 中未找到匹配的vsix文件`);
      }

      if (vsixFiles.length > 1) {
        this.log(`找到多个vsix文件: ${vsixFiles.join(', ')}，将使用最新的文件`);
        // 按修改时间排序，使用最新的
        vsixFiles.sort((a, b) => {
          const statA = fs.statSync(path.join(this.sourceDir, a));
          const statB = fs.statSync(path.join(this.sourceDir, b));
          return statB.mtime - statA.mtime;
        });
      }

      const vsixFile = vsixFiles[0];
      this.log(`找到vsix文件: ${vsixFile}`, 'success');
      return path.join(this.sourceDir, vsixFile);
    } catch (error) {
      this.log(`查找vsix文件失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 检查目标目录是否存在
   */
  checkTargetDirectory() {
    if (!fs.existsSync(this.targetDir)) {
      throw new Error(`目标目录不存在: ${this.targetDir}`);
    }
    this.log(`目标目录存在: ${this.targetDir}`, 'success');
  }

  /**
   * 清理临时目录
   */
  cleanTempDirectory() {
    if (fs.existsSync(this.tempDir)) {
      this.log('清理临时目录...');
      execSync(`rm -rf "${this.tempDir}"`, { stdio: 'inherit' });
    }
  }

  /**
   * 解压vsix文件
   */
  extractVsix(vsixPath) {
    try {
      this.log('开始解压vsix文件...');

      // 创建临时目录
      fs.mkdirSync(this.tempDir, { recursive: true });

      // 解压vsix文件（vsix实际上是zip文件）
      execSync(`cd "${this.tempDir}" && unzip -q "${vsixPath}"`, { stdio: 'inherit' });

      this.log('vsix文件解压完成', 'success');

      // 检查解压后的内容
      const extractedFiles = fs.readdirSync(this.tempDir);
      this.log(`解压后的文件: ${extractedFiles.join(', ')}`);

      return this.tempDir;
    } catch (error) {
      this.log(`解压vsix文件失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 备份现有插件
   */
  backupExistingPlugin() {
    try {
      const backupDir = `${this.targetDir}.backup.${Date.now()}`;
      this.log(`备份现有插件到: ${backupDir}`);

      execSync(`cp -r "${this.targetDir}" "${backupDir}"`, { stdio: 'inherit' });

      this.log('插件备份完成', 'success');
      return backupDir;
    } catch (error) {
      this.log(`备份插件失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 替换插件文件
   */
  replacePlugin(extractedDir) {
    try {
      this.log('开始替换插件文件...');

      // vsix解压后，实际插件内容在extension目录中
      const extensionDir = path.join(extractedDir, 'extension');
      if (!fs.existsSync(extensionDir)) {
        throw new Error('解压后未找到extension目录');
      }

      // 删除现有插件目录内容（保留目录本身）
      const targetContents = fs.readdirSync(this.targetDir);
      for (const item of targetContents) {
        const itemPath = path.join(this.targetDir, item);
        execSync(`rm -rf "${itemPath}"`, { stdio: 'inherit' });
      }

      // 复制新插件文件（从extension目录）
      const extensionContents = fs.readdirSync(extensionDir);
      for (const item of extensionContents) {
        const sourcePath = path.join(extensionDir, item);
        const targetPath = path.join(this.targetDir, item);

        if (fs.statSync(sourcePath).isDirectory()) {
          execSync(`cp -r "${sourcePath}" "${targetPath}"`, { stdio: 'inherit' });
        } else {
          execSync(`cp "${sourcePath}" "${targetPath}"`, { stdio: 'inherit' });
        }
      }

      this.log('插件文件替换完成', 'success');
    } catch (error) {
      this.log(`替换插件文件失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 验证替换结果
   */
  validateReplacement() {
    try {
      this.log('验证替换结果...');

      // 检查关键文件是否存在
      const keyFiles = ['package.json', 'dist', 'assets'];
      const missingFiles = [];

      for (const file of keyFiles) {
        const filePath = path.join(this.targetDir, file);
        if (!fs.existsSync(filePath)) {
          missingFiles.push(file);
        }
      }

      if (missingFiles.length > 0) {
        throw new Error(`缺少关键文件: ${missingFiles.join(', ')}`);
      }

      // 读取package.json验证版本
      const packageJsonPath = path.join(this.targetDir, 'package.json');
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

      this.log(`插件版本: ${packageJson.version}`, 'success');
      this.log(`插件名称: ${packageJson.displayName || packageJson.name}`, 'success');
      this.log('替换验证通过', 'success');
    } catch (error) {
      this.log(`验证替换结果失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 主执行函数
   */
  async run() {
    let backupDir = null;

    try {
      this.log('开始执行VSCode插件替换流程...');

      // 1. 查找vsix文件
      const vsixPath = this.findVsixFile();

      // 2. 检查目标目录
      this.checkTargetDirectory();

      // 3. 清理临时目录
      this.cleanTempDirectory();

      // 4. 解压vsix文件
      const extractedDir = this.extractVsix(vsixPath);

      // 5. 备份现有插件
      backupDir = this.backupExistingPlugin();

      // 6. 替换插件文件
      this.replacePlugin(extractedDir);

      // 7. 验证替换结果
      this.validateReplacement();

      // 8. 清理临时文件
      this.cleanTempDirectory();

      this.log('🎉 VSCode插件替换完成！', 'success');
      this.log(`备份位置: ${backupDir}`);
      this.log('请重启JoyCode应用以加载新插件');
    } catch (error) {
      this.log(`插件替换失败: ${error.message}`, 'error');

      // 清理临时文件
      this.cleanTempDirectory();

      // 如果有备份，提示用户可以恢复
      if (backupDir && fs.existsSync(backupDir)) {
        this.log(`如需恢复，请运行: cp -r "${backupDir}"/* "${this.targetDir}"/`);
      }

      process.exit(1);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const replacer = new VSCodePluginReplacer();
  replacer.run();
}

module.exports = VSCodePluginReplacer;
