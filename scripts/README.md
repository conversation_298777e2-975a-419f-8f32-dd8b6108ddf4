# VSCode插件替换脚本

这个Node.js脚本用于自动化替换JoyCode应用中的VSCode插件。



## 功能特性

- 🔍 自动查找最新的vsix文件
- 📦 解压vsix文件并提取插件内容
- 💾 自动备份现有插件
- 🔄 替换插件文件到指定目录
- ✅ 验证替换结果
- 🧹 自动清理临时文件
- 📝 详细的日志输出

## 文件说明

- [`replaceIDEJoyCode.js`](replaceIDEJoyCode.js) - 主要的替换脚本
- [`../test/test-replace-plugin.js`](../test/test-replace-plugin.js) - 测试脚本，用于验证环境和功能

## 使用方法

### 1. 运行测试（推荐）

在执行实际替换之前，建议先运行测试脚本来验证环境：

```bash
node test/test-replace-plugin.js
```

测试脚本会检查：
- vsix文件是否存在
- 目标目录是否存在
- 解压工具是否可用
- vsix文件格式是否正确
- 模拟解压测试
- 目录权限检查

### 2. 执行插件替换

当所有测试通过后，运行主脚本：

```bash
node scripts/replaceIDEJoyCode.js
```

或者直接执行：

```bash
./scripts/replaceIDEJoyCode.js
```

## 工作流程

1. **查找vsix文件** - 在 `packages/vscode-IDE/` 目录中查找匹配 `joycoder-editor-*.vsix` 模式的文件
2. **检查目标目录** - 验证 `/Applications/JoyCode.app/Contents/Resources/app/extensions/joycode.joycoder-editor` 目录存在
3. **解压vsix文件** - 将vsix文件解压到临时目录
4. **备份现有插件** - 创建当前插件的备份副本
5. **替换插件文件** - 将新插件文件复制到目标目录
6. **验证结果** - 检查关键文件是否存在并读取版本信息
7. **清理临时文件** - 删除解压过程中创建的临时文件

## 安全特性

- ✅ 自动备份现有插件（带时间戳）
- ✅ 详细的错误处理和回滚提示
- ✅ 验证文件完整性
- ✅ 权限检查
- ✅ 临时文件自动清理

## 目录结构

```
packages/vscode-IDE/
├── joycoder-editor-3.1.3.vsix  # 源文件
└── temp_extract/               # 临时解压目录（自动清理）

/Applications/JoyCode.app/Contents/Resources/app/extensions/
└── joycode.joycoder-editor/    # 目标目录
    ├── package.json
    ├── dist/
    ├── assets/
    └── ...

备份目录示例：
/Applications/JoyCode.app/Contents/Resources/app/extensions/
└── joycode.joycoder-editor.backup.1691916000000/  # 备份目录
```

## 错误处理

如果替换过程中出现错误，脚本会：

1. 显示详细的错误信息
2. 自动清理临时文件
3. 提供恢复命令（如果有备份）

恢复命令示例：
```bash
cp -r "/Applications/JoyCode.app/Contents/Resources/app/extensions/joycode.joycoder-editor.backup.1691916000000"/* "/Applications/JoyCode.app/Contents/Resources/app/extensions/joycode.joycoder-editor"/
```

## 注意事项

1. **重启应用** - 替换完成后需要重启JoyCode应用以加载新插件
2. **权限要求** - 需要对目标目录有读写权限
3. **备份管理** - 脚本会创建备份，但不会自动清理旧备份
4. **版本兼容性** - 确保新插件版本与JoyCode应用兼容

## 故障排除

### 常见问题

1. **权限不足**
   ```
   解决方案: 使用 sudo 运行脚本或修改目录权限
   ```

2. **vsix文件未找到**
   ```
   解决方案: 确保vsix文件在 packages/vscode-IDE/ 目录中
   ```

3. **目标目录不存在**
   ```
   解决方案: 确保JoyCode应用已正确安装
   ```

4. **解压失败**
   ```
   解决方案: 检查vsix文件是否损坏，确保unzip工具可用
   ```

## 开发信息

- **语言**: Node.js
- **依赖**: 仅使用Node.js内置模块
- **兼容性**: macOS (当前配置)
- **测试**: 包含完整的测试套件