const path = require('path');
const fs = require('fs');
const glob = require('glob');
const WebpackObfuscator = require('webpack-obfuscator');
const { isProduction } = require('./utils');

// 加载顶层默认配置
const TOP_LEVEL_CONFIG_PATH = path.join(process.cwd(), 'obfuscate.config.js');
let GLOBAL_DEFAULTS = {};
if (fs.existsSync(TOP_LEVEL_CONFIG_PATH)) {
  const topConfig = require(TOP_LEVEL_CONFIG_PATH);
  GLOBAL_DEFAULTS = topConfig.options || GLOBAL_DEFAULTS;
}

// 递归查找所有子项目配置
const findObfuscationConfigs = (baseDir = process.cwd()) => {
  const configs = [];

  const scanDirectory = (currentDir, depth = 0) => {
    const entries = fs.readdirSync(currentDir, { withFileTypes: true });
    const hasPackageJson = entries.some((e) => e.name === 'package.json');
    let projectConfig = null;

    if (hasPackageJson) {
      try {
        const configPath = path.join(currentDir, 'obfuscate.config.js');
        if (fs.existsSync(configPath)) {
          const rawConfig = require(configPath) || {};
          projectConfig = {
            basePath: currentDir,
            depth,
            patterns: rawConfig.patterns || [],
            options: { ...GLOBAL_DEFAULTS, ...rawConfig.options },
          };
        }
      } catch (e) {
        throw new Error(`扫描项目混淆配置时出现错误 @ ${currentDir}:`, e.message);
      }
    }

    entries.forEach((entry) => {
      if (entry.isDirectory() && !['node_modules', 'dist', 'build'].includes(entry.name)) {
        scanDirectory(path.join(currentDir, entry.name), depth + 1);
      }
    });

    if (projectConfig) {
      configs.push(projectConfig);
    }
  };

  scanDirectory(baseDir);
  return configs.sort((a, b) => b.depth - a.depth);
};
// 生成Webpack loader的规则
const generateObfuscationRules = () => {
  const isProd = isProduction();

  if (!isProd) {
    return [];
  }

  // 确保从项目根目录开始扫描
  // 检查当前目录是否已经是项目根目录
  let projectRoot = process.cwd();
  if (projectRoot.endsWith('/packages/vscode-IDE')) {
    projectRoot = path.resolve(projectRoot, '../..');
  }

  const allConfigs = findObfuscationConfigs(projectRoot);

  const rules = [];
  const processedFiles = new Set();
  allConfigs.forEach((config) => {
    try {
      if (config.patterns.length === 0) return;

      // 分离包含和排除模式
      const includePatterns = config.patterns.filter((p) => !p.startsWith('!'));
      const excludePatterns = config.patterns.filter((p) => p.startsWith('!')).map((p) => p.substring(1));

      // 先获取所有匹配的文件
      let resolvedFiles = includePatterns.flatMap((pattern) =>
        glob.sync(pattern, {
          cwd: config.basePath,
          absolute: true,
          nodir: true,
          ignore: ['**/node_modules/**'],
        })
      );

      // 然后应用排除模式
      if (excludePatterns.length > 0) {
        const excludeFiles = excludePatterns.flatMap((pattern) =>
          glob.sync(pattern, {
            cwd: config.basePath,
            absolute: true,
            nodir: true,
          })
        );

        resolvedFiles = resolvedFiles.filter((file) => !excludeFiles.includes(file));
      }

      resolvedFiles = resolvedFiles.filter((filePath) => {
        // 安全校验，防止跨项目配置
        // const valid = filePath.startsWith(config.basePath);
        // if (!valid) {
        //   throw new Error(`路径越界: ${path.relative(config.basePath, filePath)}`);
        // }
        // return valid && !processedFiles.has(filePath);
        // 规范化路径
        const normalizedFilePath = path.normalize(filePath);
        const normalizedBasePath = path.normalize(config.basePath);

        // 检查文件是否属于当前项目
        const relativeToBase = path.relative(normalizedBasePath, normalizedFilePath);

        // 如果文件不在项目目录内，直接返回 false
        if (relativeToBase.startsWith('..') || path.isAbsolute(relativeToBase)) {
          console.warn(`警告: 文件 ${filePath} 指向项目外部，已跳过混淆`);
          return false;
        }

        // 额外安全检查：确保文件实际位置确实在项目目录内
        // 这可以防止符号链接或其他路径技巧导致的跨项目访问
        try {
          const realFilePath = fs.realpathSync(normalizedFilePath);
          const realBasePath = fs.realpathSync(normalizedBasePath);

          if (!realFilePath.startsWith(realBasePath)) {
            console.warn(`警告: 文件 ${filePath} 通过符号链接或其他方式指向项目外部，已跳过混淆`);
            return false;
          }
        } catch (error) {
          console.warn(`警告: 无法解析文件 ${filePath} 的真实路径，已跳过混淆`);
          return false;
        }
        return true;
      });

      if (resolvedFiles.length > 0) {
        const rule = {
          test: resolvedFiles,
          enforce: 'post',
          use: {
            loader: WebpackObfuscator.loader,
            options: config.options,
          },
        };
        rules.push(rule);
        resolvedFiles.forEach((file) => processedFiles.add(file));
      }
    } catch (e) {
      console.error(`处理失败 @ ${config.basePath}:`, e);
    }
  });

  return rules;
};

module.exports = {
  generateObfuscationRules,
};
