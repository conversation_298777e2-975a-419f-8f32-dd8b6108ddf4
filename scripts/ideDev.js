const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 主函数
async function main() {
  let vsixPath;
  let zipPath;
  let extractDir;

  try {
    // 1. 找到所有 .vsix 文件并按修改时间排序
    const vsixDir = path.join(__dirname, '../packages/vscode-IDE');
    const files = fs.readdirSync(vsixDir);
    const vsixFiles = files
      .filter((file) => file.endsWith('.vsix'))
      .map((file) => {
        const filePath = path.join(vsixDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          path: filePath,
          mtime: stats.mtime,
        };
      })
      .sort((a, b) => b.mtime - a.mtime); // 按修改时间降序排序

    if (vsixFiles.length === 0) {
      console.error('未找到 .vsix 文件');
      return;
    }

    // 使用最新的文件
    const latestVsix = vsixFiles[0];
    console.log(`使用最新的文件: ${latestVsix.name} (修改时间: ${latestVsix.mtime})`);

    vsixPath = latestVsix.path;
    zipPath = vsixPath.replace('.vsix', '.zip');

    // 2. 重命名为 .zip
    fs.renameSync(vsixPath, zipPath);

    // 3. 创建临时解压目录
    extractDir = path.join(vsixDir, 'temp_extract');
    if (!fs.existsSync(extractDir)) {
      fs.mkdirSync(extractDir);
    }

    // 4. 解压文件
    execSync(`unzip -o "${zipPath}" -d "${extractDir}"`);

    // 5. 找到目标文件夹
    const joycodePath = '/Applications/JoyCode.app/Contents/Resources/app/extensions';
    const files2 = fs.readdirSync(joycodePath);
    const targetFolder = files2.find((file) => file.startsWith('joycoder.joycoder-editor'));

    if (!targetFolder) {
      console.error('未找到目标文件夹');
      return;
    }

    const targetPath = path.join(joycodePath, targetFolder);
    const sourcePath = path.join(extractDir, 'extension');

    // 6. 清理目标目录中的现有文件
    if (fs.existsSync(targetPath)) {
      console.log('清理目标目录中的现有文件...');
      const targetFiles = fs.readdirSync(targetPath);
      for (const file of targetFiles) {
        const filePath = path.join(targetPath, file);
        fs.rmSync(filePath, { recursive: true, force: true });
      }
    }

    // 7. 复制新文件
    console.log('复制新文件到目标目录...');
    execSync(`cp -R "${sourcePath}/"* "${targetPath}/"`);

    // 8. 清理临时文件
    fs.rmSync(extractDir, { recursive: true, force: true });
    console.log('开发环境更新完成！');
  } catch (error) {
    console.error('发生错误:', error);
  } finally {
    // 清理临时文件和恢复文件扩展名
    try {
      // 删除临时解压目录
      if (extractDir && fs.existsSync(extractDir)) {
        fs.rmSync(extractDir, { recursive: true, force: true });
        console.log('已清理临时文件夹');
      }

      // 恢复 .vsix 扩展名
      if (fs.existsSync(zipPath)) {
        fs.renameSync(zipPath, vsixPath);
        console.log('已恢复文件扩展名为 .vsix');
      }
    } catch (cleanupError) {
      console.error('清理过程中发生错误:', cleanupError);
    }
  }
}

main();
